// 确保 URL polyfill 在所有其他导入之前加载（备用方案）
import 'react-native-url-polyfill/auto';

import React, { useEffect, useState } from 'react';
import { StatusBar, StyleSheet, useColorScheme, View, Text, SafeAreaView, ActivityIndicator } from 'react-native';
import { Provider } from 'react-redux';
import Toast from 'react-native-toast-message';
import { toastConfig } from './src/config/toastConfig';
import { PersistGate } from 'redux-persist/integration/react';
import { store, persistor } from './src/store';
import { checkPermissions } from './src/utils/permissions';
import AppNavigator from './src/navigation/AppNavigator'; // 导入 AppNavigator

function App() {
  return (
    <Provider store={store}>
      <PersistGate loading={<LoadingScreen />} persistor={persistor}>
        <AppContent />
        <Toast config={toastConfig} />
      </PersistGate>
    </Provider>
  );
}

function LoadingScreen() {
  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <Text style={styles.title}>面试助手</Text>
        <Text style={styles.subtitle}>AI实时面试辅助应用</Text>
        <ActivityIndicator size="large" color="#0000ff" style={styles.activityIndicator} />
        <Text style={styles.status}>正在加载...</Text>
      </View>
    </SafeAreaView>
  );
}

function AppContent() {
  const isDarkMode = useColorScheme() === 'dark';
  const [appStatus, setAppStatus] = useState('初始化中...');
  const [isLoading, setIsLoading] = useState(true);
  const [hasPermission, setHasPermission] = useState(false); // 新增权限状态

  useEffect(() => {
    const initializeApp = async () => {
      setAppStatus('检查权限...');
      const permissionGranted = await checkPermissions();
      setHasPermission(permissionGranted); // 更新权限状态

      if (permissionGranted) {
        // 模拟应用内容加载
        setTimeout(() => {
          setIsLoading(false);
          setAppStatus('加载完成');
        }, 2000); 
      } else {
        setAppStatus('权限不足，请在设置中开启麦克风权限。');
        setIsLoading(false);
      }
    };

    initializeApp();
  }, []);

  if (isLoading || !hasPermission) { // 如果还在加载或没有权限，显示加载/权限不足页面
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle={isDarkMode ? 'light-content' : 'dark-content'} />
        <View style={styles.content}>
          <Text style={styles.title}>面试助手</Text>
          <Text style={styles.subtitle}>AI实时面试辅助应用</Text>
          {isLoading ? (
            <ActivityIndicator size="large" color="#0000ff" style={styles.activityIndicator} />
          ) : null}
          <Text style={styles.status}>{appStatus}</Text>
        </View>
      </SafeAreaView>
    );
  }

  return <AppNavigator />; // 加载完成且有权限，显示主导航
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    marginBottom: 20,
  },
  status: {
    fontSize: 14,
    color: '#999',
    fontStyle: 'italic',
    textAlign: 'center',
    marginTop: 20,
  },
  activityIndicator: {
    marginTop: 20,
  },
});

export default App;
