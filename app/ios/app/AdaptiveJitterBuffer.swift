import Foundation
import AVFoundation

// A more realistic RTP Packet structure
struct RTPPacket: Comparable {
    let payload: Data
    let timestamp: UInt32
    let sequenceNumber: UInt16
    
    static func < (lhs: RTPPacket, rhs: RTPPacket) -> Bool {
        return lhs.sequenceNumber < rhs.sequenceNumber
    }
}

// 高效的优先队列实现 - 使用插入排序而不是全排序
struct PacketQueue {
    fileprivate var packets = [RTPPacket]() // 改为fileprivate以便AdaptiveJitterBuffer访问

    mutating func add(packet: RTPPacket) {
        // 使用二分查找插入，保持排序状态，O(log n + n) 复杂度
        let insertIndex = packets.firstIndex { $0.sequenceNumber > packet.sequenceNumber } ?? packets.count
        packets.insert(packet, at: insertIndex)
    }

    mutating func get() -> RTPPacket? {
        return packets.isEmpty ? nil : packets.removeFirst()
    }

    var count: Int {
        return packets.count
    }
}

class AdaptiveJitterBuffer {
    // MARK: - Properties

    private var packetQueue = PacketQueue()

    // 简单的时间戳缓冲控制
    private var firstTimestamp: UInt32?
    private let minBufferMs: Double = 50 // 最小50ms缓冲，确保不丢包
    private let sampleRate: Double

    // MARK: - Initialization

    init(sampleRate: Double, channels: UInt32) {
        self.sampleRate = sampleRate
    }

    // MARK: - Public Methods

    func addPacket(_ packet: RTPPacket) {
        // 绝不丢包策略：直接添加包到优先队列
        // 优先队列会自动按序列号排序
        packetQueue.add(packet: packet)

        // 记录第一个包的时间戳作为基准
        if firstTimestamp == nil {
            firstTimestamp = packet.timestamp
        }
    }

    func getPacket() -> RTPPacket? {
        if packetQueue.count == 0 {
            return nil
        }

        // 简单的时间戳缓冲：确保有最小缓冲时间，宁可延迟也不丢包
        if let firstTs = firstTimestamp, let nextPacket = packetQueue.packets.first {
            // 计算当前缓冲的时间长度（基于时间戳）
            let timestampDiff = nextPacket.timestamp - firstTs
            let bufferTimeMs = (Double(timestampDiff) * 1000.0) / sampleRate

            // 如果缓冲时间不足最小要求，等待更多包
            if bufferTimeMs < minBufferMs && packetQueue.count < 3 {
                return nil // 等待更多包，确保不丢包
            }
        }

        // 返回下一个包
        return packetQueue.get()
    }

    func getBufferSize() -> Int {
        return packetQueue.count
    }
}
