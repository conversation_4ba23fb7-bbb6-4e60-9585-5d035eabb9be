import Foundation
import AVFoundation

@objc(AudioStreamPlayer)
class AudioStreamPlayer: NSObject {

    private var audioEngine: AVAudioEngine!
    private var playerNode: AVAudioPlayerNode!
    private var audioFormat: AVAudioFormat!
    private var jitterBuffer: AdaptiveJitterBuffer!
    private let audioStreamQueue = DispatchQueue(label: "com.interviewmaster.audioStreamQueue")
    private var isStreaming = false

    override init() {
        super.init()
        audioEngine = AVAudioEngine()
        playerNode = AVAudioPlayerNode()
        audioEngine.attach(playerNode)
    }

    @objc(init:resolver:rejecter:)
    func `init`(options: NSDictionary, resolver resolve: @escaping RCTPromiseResolveBlock, rejecter reject: @escaping RCTPromiseRejectBlock) {
        // 确保使用传入的采样率，默认24000Hz而不是16000Hz
        let sampleRate = options["sampleRate"] as? Double ?? 24000.0
        let channels = options["channels"] as? UInt32 ?? 1

        print("iOS AudioStreamPlayer 初始化 - 采样率: \(sampleRate)Hz, 声道: \(channels)")

        audioFormat = AVAudioFormat(commonFormat: .pcmFormatInt16, sampleRate: sampleRate, channels: channels, interleaved: false)
        jitterBuffer = AdaptiveJitterBuffer(sampleRate: sampleRate, channels: channels)

        audioEngine.connect(playerNode, to: audioEngine.mainMixerNode, format: audioFormat)

        do {
            try audioEngine.start()
            playerNode.play()
            startStreaming()
            resolve(nil)
        } catch {
            reject("INIT_ERROR", "Failed to start audio engine", error)
        }
    }

    private var sequenceCounter: UInt16 = 0
    private var timestampCounter: UInt32 = 0

    @objc(write:resolver:rejecter:)
    func write(base64Data: String, resolver resolve: @escaping RCTPromiseResolveBlock, rejecter reject: @escaping RCTPromiseRejectBlock) {
        guard let data = Data(base64Encoded: base64Data) else {
            reject("WRITE_ERROR", "Invalid base64 data", nil)
            return
        }
        
        // 生成递增的序列号和时间戳
        sequenceCounter = sequenceCounter &+ 1
        timestampCounter = timestampCounter &+ UInt32(data.count / 2)
        
        let packet = RTPPacket(payload: data, timestamp: timestampCounter, sequenceNumber: sequenceCounter)
        jitterBuffer.addPacket(packet)
        
        resolve(nil)
    }

    @objc(getPendingBufferCount:rejecter:)
    func getPendingBufferCount(resolver resolve: @escaping RCTPromiseResolveBlock, rejecter reject: @escaping RCTPromiseRejectBlock) {
        resolve(jitterBuffer.getBufferSize())
    }

    @objc(getStats:rejecter:)
    func getStats(resolver resolve: @escaping RCTPromiseResolveBlock, rejecter reject: @escaping RCTPromiseRejectBlock) {
        // In a real implementation, we would return a dictionary with more stats
        resolve(jitterBuffer.getBufferSize())
    }

    @objc(stop:rejecter:)
    func stop(resolver resolve: @escaping RCTPromiseResolveBlock, rejecter reject: @escaping RCTPromiseRejectBlock) {
        stopStreaming()
        playerNode.stop()
        audioEngine.stop()
        resolve(nil)
    }
    
    private func startStreaming() {
        isStreaming = true
        audioStreamQueue.async { [weak self] in
            guard let self = self else { return }
            print("iOS Audio streaming started - pure native control")

            while self.isStreaming {
                if let packet = self.jitterBuffer.getPacket() {
                    if let buffer = packet.payload.toPCMBuffer(format: self.audioFormat) {
                        // 直接调度到AVAudioPlayerNode，让其完全控制播放时序
                        self.playerNode.scheduleBuffer(buffer, completionHandler: nil)

                        // 不做任何时序控制，不做任何等待，让AVAudioPlayerNode自己控制一切
                    }
                } else {
                    // 只在没有数据时才等待，且时间极短
                    usleep(1000) // 最小等待时间 1ms
                }
            }
        }
    }

    private func stopStreaming() {
        isStreaming = false
    }
    
    @objc
    static func requiresMainQueueSetup() -> Bool {
        return true
    }
}

extension Data {
    func toPCMBuffer(format: AVAudioFormat) -> AVAudioPCMBuffer? {
        guard let pcmBuffer = AVAudioPCMBuffer(pcmFormat: format, frameCapacity: AVAudioFrameCount(self.count) / (format.streamDescription.pointee.mBytesPerFrame)) else {
            return nil
        }
        pcmBuffer.frameLength = pcmBuffer.frameCapacity
        
        guard let int16ChannelData = pcmBuffer.int16ChannelData else {
            return nil
        }

        self.withUnsafeBytes { (buffer: UnsafeRawBufferPointer) in
            guard let src = buffer.baseAddress?.assumingMemoryBound(to: Int16.self) else {
                return
            }
            int16ChannelData[0].initialize(from: UnsafeBufferPointer(start: src, count: self.count / 2))
        }
        return pcmBuffer
    }
}
