// 支付配置
export const PAYMENT_CONFIG = {
  // 微信支付配置
  WECHAT: {
    APP_ID: 'wx1234567890abcdef', // 微信开放平台App ID（需要替换为真实值）
    UNIVERSAL_LINK: 'https://your-domain.com/app/', // iOS Universal Link（需要替换为真实域名）
  },
  
  // 支付宝配置
  ALIPAY: {
    // 支付宝不需要额外配置，使用服务端返回的订单信息即可
    // 确保后端已正确配置支付宝App ID和密钥
  }
};

// 配置验证
export const validatePaymentConfig = () => {
  const errors: string[] = [];
  
  // 检查微信配置
  if (!PAYMENT_CONFIG.WECHAT.APP_ID || PAYMENT_CONFIG.WECHAT.APP_ID === 'wx1234567890abcdef') {
    errors.push('请配置真实的微信App ID');
  }
  
  if (!PAYMENT_CONFIG.WECHAT.UNIVERSAL_LINK || PAYMENT_CONFIG.WECHAT.UNIVERSAL_LINK.includes('your-domain.com')) {
    errors.push('请配置真实的iOS Universal Link');
  }
  
  if (errors.length > 0) {
    console.warn('⚠️ 支付配置警告:', errors);
    return false;
  }
  
  console.log('✅ 支付配置验证通过');
  return true;
};