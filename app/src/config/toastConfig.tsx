import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { theme } from '../styles/theme';

// 自定义Toast配置，参考业界主流设计
export const toastConfig = {
  // 成功提示
  success: ({ text1, text2 }: any) => (
    <View style={[styles.toastContainer, styles.successToast]}>
      <Icon name="check-circle" size={20} color={theme.colors.success} style={styles.icon} />
      <View style={styles.textContainer}>
        <Text style={[styles.text1, styles.successText]}>{text1}</Text>
        {text2 ? <Text style={[styles.text2, styles.successText]}>{text2}</Text> : null}
      </View>
    </View>
  ),
  
  // 错误提示
  error: ({ text1, text2 }: any) => (
    <View style={[styles.toastContainer, styles.errorToast]}>
      <Icon name="error" size={20} color={theme.colors.error} style={styles.icon} />
      <View style={styles.textContainer}>
        <Text style={[styles.text1, styles.errorText]}>{text1}</Text>
        {text2 ? <Text style={[styles.text2, styles.errorText]}>{text2}</Text> : null}
      </View>
    </View>
  ),
  
  // 信息提示
  info: ({ text1, text2 }: any) => (
    <View style={[styles.toastContainer, styles.infoToast]}>
      <Icon name="info" size={20} color={theme.colors.secondary} style={styles.icon} />
      <View style={styles.textContainer}>
        <Text style={[styles.text1, styles.infoText]}>{text1}</Text>
        {text2 ? <Text style={[styles.text2, styles.infoText]}>{text2}</Text> : null}
      </View>
    </View>
  ),
  
  // 警告提示
  warning: ({ text1, text2 }: any) => (
    <View style={[styles.toastContainer, styles.warningToast]}>
      <Icon name="warning" size={20} color={theme.colors.warning} style={styles.icon} />
      <View style={styles.textContainer}>
        <Text style={[styles.text1, styles.warningText]}>{text1}</Text>
        {text2 ? <Text style={[styles.text2, styles.warningText]}>{text2}</Text> : null}
      </View>
    </View>
  ),
};

const styles = StyleSheet.create({
  toastContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    marginHorizontal: theme.spacing.md,
    borderRadius: theme.borderRadius.lg,
    backgroundColor: theme.colors.surface,
    borderWidth: 1,
    minHeight: 56,
    // 添加阴影效果
    ...theme.shadows.md,
  },
  
  icon: {
    marginRight: theme.spacing.sm,
  },
  
  textContainer: {
    flex: 1,
  },
  
  text1: {
    fontSize: theme.fontSize.base,
    fontWeight: theme.fontWeight.medium,
    lineHeight: 20,
  },
  
  text2: {
    fontSize: theme.fontSize.sm,
    marginTop: 2,
    lineHeight: 16,
  },
  
  // 成功样式
  successToast: {
    borderColor: theme.colors.success,
    backgroundColor: '#f0fdf4', // 浅绿色背景
  },
  successText: {
    color: theme.colors.success,
  },
  
  // 错误样式
  errorToast: {
    borderColor: theme.colors.error,
    backgroundColor: '#fef2f2', // 浅红色背景
  },
  errorText: {
    color: theme.colors.error,
  },
  
  // 信息样式
  infoToast: {
    borderColor: theme.colors.secondary,
    backgroundColor: '#f0f9ff', // 浅蓝色背景
  },
  infoText: {
    color: theme.colors.secondary,
  },
  
  // 警告样式
  warningToast: {
    borderColor: theme.colors.warning,
    backgroundColor: '#fffbeb', // 浅橙色背景
  },
  warningText: {
    color: theme.colors.warning,
  },
});

// Toast显示的默认配置
export const defaultToastProps = {
  position: 'top' as const,
  topOffset: 60, // 距离顶部60px，避免遮挡状态栏和导航栏
  visibilityTime: 3000, // 显示3秒
  autoHide: true,
  swipeable: true, // 支持滑动关闭
};