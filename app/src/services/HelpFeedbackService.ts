import { BASE_URL } from '../utils/config';

export interface FAQ {
  id: number;
  question: string;
  answer: string;
  category: string;
  view_count: number;
  created_at: string;
  updated_at: string;
}

export interface HelpArticle {
  id: number;
  title: string;
  content: string;
  category: string;
  tags?: string;
  view_count: number;
  sort_order: number;
  status: number;
  created_at: string;
  updated_at: string;
}

export interface Feedback {
  id: number;
  user_id: number;
  type: string;
  content: string;
  contact_email?: string;
  status: number;
  admin_reply?: string;
  processed_at?: string;
  created_at: string;
  updated_at: string;
}

export interface CreateFeedbackRequest {
  type: 'suggestion' | 'bug' | 'other';
  content: string;
  contact_email?: string;
}

class HelpFeedbackService {
  private async getAuthHeaders(): Promise<HeadersInit> {
    const token = await this.getStoredToken();
    return {
      'Content-Type': 'application/json',
      'Authorization': token ? `Bearer ${token}` : '',
    };
  }

  // FAQ相关方法
  async getFAQs(category?: string): Promise<{ faqs: FAQ[] }> {
    try {
      const url = new URL(`${BASE_URL}/api/v1/help/faqs`);
      if (category) {
        url.searchParams.append('category', category);
      }

      const response = await fetch(url.toString());
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data.data;
    } catch (error) {
      console.error('获取FAQ失败:', error);
      throw error;
    }
  }

  async getFAQCategories(): Promise<{ categories: string[] }> {
    try {
      const response = await fetch(`${BASE_URL}/api/v1/help/faq-categories`);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data.data;
    } catch (error) {
      console.error('获取FAQ分类失败:', error);
      throw error;
    }
  }

  async incrementFAQViewCount(id: number): Promise<void> {
    try {
      const response = await fetch(`${BASE_URL}/api/v1/help/faqs/${id}/view`, {
        method: 'POST',
      });
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
    } catch (error) {
      console.error('增加FAQ查看次数失败:', error);
      // 这个错误不需要抛出，因为不影响用户体验
    }
  }

  // 帮助文章相关方法
  async getHelpArticles(category?: string): Promise<{ articles: HelpArticle[] }> {
    try {
      const url = new URL(`${BASE_URL}/api/v1/help/articles`);
      if (category) {
        url.searchParams.append('category', category);
      }

      const response = await fetch(url.toString());
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data.data;
    } catch (error) {
      console.error('获取帮助文章失败:', error);
      throw error;
    }
  }

  async getHelpArticleDetail(id: number): Promise<HelpArticle> {
    try {
      const response = await fetch(`${BASE_URL}/api/v1/help/articles/${id}`);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data.data;
    } catch (error) {
      console.error('获取帮助文章详情失败:', error);
      throw error;
    }
  }

  async getHelpCategories(): Promise<{ categories: string[] }> {
    try {
      const response = await fetch(`${BASE_URL}/api/v1/help/categories`);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data.data;
    } catch (error) {
      console.error('获取帮助文章分类失败:', error);
      throw error;
    }
  }

  // 反馈相关方法
  async createFeedback(feedbackData: CreateFeedbackRequest): Promise<Feedback> {
    try {
      const response = await fetch(`${BASE_URL}/api/v1/feedback/create`, {
        method: 'POST',
        headers: await this.getAuthHeaders(),
        body: JSON.stringify(feedbackData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data.data;
    } catch (error) {
      console.error('创建反馈失败:', error);
      throw error;
    }
  }

  async getUserFeedbacks(page: number = 1, pageSize: number = 10): Promise<{
    feedbacks: Feedback[];
    total: number;
    page: number;
    page_size: number;
  }> {
    try {
      const url = new URL(`${BASE_URL}/api/v1/feedback/list`);
      url.searchParams.append('page', page.toString());
      url.searchParams.append('page_size', pageSize.toString());

      const response = await fetch(url.toString(), {
        headers: await this.getAuthHeaders(),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data.data;
    } catch (error) {
      console.error('获取用户反馈失败:', error);
      throw error;
    }
  }

  async getFeedbackDetail(id: number): Promise<Feedback> {
    try {
      const response = await fetch(`${BASE_URL}/api/v1/feedback/${id}`, {
        headers: await this.getAuthHeaders(),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data.data;
    } catch (error) {
      console.error('获取反馈详情失败:', error);
      throw error;
    }
  }

  // 获取token的辅助方法
  async getStoredToken(): Promise<string | null> {
    try {
      const AsyncStorage = require('@react-native-async-storage/async-storage').default;
      const token = await AsyncStorage.getItem('token');
      return token;
    } catch (error) {
      console.error('获取存储的token失败:', error);
      return null;
    }
  }

  // 更新认证头的方法
  async updateAuthHeaders(): Promise<HeadersInit> {
    const token = await this.getStoredToken();
    return {
      'Content-Type': 'application/json',
      'Authorization': token ? `Bearer ${token}` : '',
    };
  }
}

export default new HelpFeedbackService();