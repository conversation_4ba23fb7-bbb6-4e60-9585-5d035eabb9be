import { VoiceProcessor } from '@picovoice/react-native-voice-processor';
import { PermissionsAndroid, Platform } from 'react-native';
import InCallManager from 'react-native-incall-manager';
import { AUDIO_CONFIG } from '../utils/config';
import WebSocketService from './WebSocketService';
import logger from '../utils/logger';


interface AudioInputConfig {
  sampleRate: number;
  frameLength: number;
  enableAEC: boolean;
  enableAGC: boolean;
  enableNS: boolean;
}

class EnhancedAudioInputService {
  private isRecording = false;
  private isMuted = false;
  private stats = {
    startAttempts: 0,
    chunksSent: 0,
    errors: 0,
    lastError: '',
  };
  private frameListener: ((frame: number[]) => void) | null = null;
  private errorListener: ((error: any) => void) | null = null;
  private isInCallManagerInitialized = false;
  private voiceProcessor = VoiceProcessor.instance;

  private audioBuffer: Int16Array[] = [];
  private readonly BUFFER_SIZE = 8;        // 🚀 减少到8帧，降低延迟
  private readonly MAX_BUFFER_TIME = 200;  // 🚀 减少到200ms，更快响应
  private lastSendTime = 0;

  private readonly VAD_THRESHOLD = 100; // 进一步降低阈值，检测更轻微的语音
  private silenceTimer: any = null;
  private readonly SILENCE_TIMEOUT = 2000; // 增加到2秒，给用户更多时间说话
  private streamEnded = true;

  private config: AudioInputConfig = {
    sampleRate: AUDIO_CONFIG.sendSampleRate,
    frameLength: AUDIO_CONFIG.chunkSize,
    enableAEC: true,
    enableAGC: true,
    enableNS: true,
  };

  async initialize(): Promise<boolean> {
    try {
      const hasPermission = await this.requestPermissions();
      if (!hasPermission) {
        logger.error('AudioInput', 'Permissions not granted');
        return false;
      }

      if (this.config.enableAEC) {
        await this.initializeInCallManager();
      }

      this.setupVoiceProcessorListeners();
      logger.info('AudioInput', 'Initialized successfully');
      return true;
    } catch (error) {
      console.error('EnhancedAudioInputService: Initialization failed:', error);
      return false;
    }
  }

  private async requestPermissions(): Promise<boolean> {
    if (Platform.OS === 'android') {
      try {
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.RECORD_AUDIO,
          {
            title: '录音权限',
            message: '应用需要录音权限来进行面试',
            buttonNeutral: '稍后询问',
            buttonNegative: '取消',
            buttonPositive: '确定',
          }
        );
        return granted === PermissionsAndroid.RESULTS.GRANTED;
      } catch (err) {
        console.warn('EnhancedAudioInputService: Permission request error:', err);
        return false;
      }
    }

    try {
      return await this.voiceProcessor.hasRecordAudioPermission();
    } catch (error) {
      console.error('EnhancedAudioInputService: iOS permission check failed:', error);
      return false;
    }
  }

  private async initializeInCallManager(): Promise<boolean> {
    if (this.isInCallManagerInitialized) {
      return true;
    }

    if (!InCallManager) {
      console.log('EnhancedAudioInputService: InCallManager not available');
      return false;
    }

    try {
      console.log('EnhancedAudioInputService: Initializing InCallManager...');
      await new Promise(resolve => setTimeout(resolve, 500));
      InCallManager.start({ media: 'audio', auto: true, ringback: '' });
      await new Promise(resolve => setTimeout(resolve, 300));
      if (InCallManager.setForceSpeakerphoneOn) {
        InCallManager.setForceSpeakerphoneOn(true);
      }
      this.isInCallManagerInitialized = true;
      console.log('EnhancedAudioInputService: InCallManager initialized');
      return true;
    } catch (error) {
      console.error('EnhancedAudioInputService: InCallManager initialization failed:', error);
      return false;
    }
  }

  private calculateRMS(frame: Int16Array): number {
    let sumOfSquares = 0;
    for (let i = 0; i < frame.length; i++) {
      sumOfSquares += frame[i] * frame[i];
    }
    return Math.sqrt(sumOfSquares / frame.length);
  }

  private setupVoiceProcessorListeners() {
    if (this.frameListener) this.voiceProcessor.removeFrameListener(this.frameListener);
    if (this.errorListener) this.voiceProcessor.removeErrorListener(this.errorListener);

    this.frameListener = (frame: number[]) => {
      if (this.isMuted || !this.isRecording) return;

      const int16Frame = new Int16Array(frame);
      // 禁用客户端VAD，完全依赖服务器端VAD
      // 持续发送所有音频数据，让服务器端处理语音检测
      this.audioBuffer.push(int16Frame);

      const now = Date.now();
      const timeSinceLastSend = now - this.lastSendTime;

      // 🚀 根据连接模式动态调整发送策略
      const isProxyMode = WebSocketService.isUsingProxy();
      const shouldSend = this.audioBuffer.length >= this.BUFFER_SIZE ||
        timeSinceLastSend >= this.MAX_BUFFER_TIME ||
        (isProxyMode && this.audioBuffer.length >= 2 && timeSinceLastSend >= 50) || // proxy模式：2帧且50ms
        (!isProxyMode && this.audioBuffer.length >= 4 && timeSinceLastSend >= 100); // direct模式：4帧且100ms

      if (shouldSend && WebSocketService.isConnected() && WebSocketService.isSetupComplete()) {
        this.sendBufferedAudio();
        this.lastSendTime = now;
      }
    };

    this.errorListener = (error: any) => {
      console.error('EnhancedAudioInputService: Voice processor error:', error);
    };

    this.voiceProcessor.addFrameListener(this.frameListener);
    this.voiceProcessor.addErrorListener(this.errorListener);
  }

  private sendBufferedAudio() {
    if (this.audioBuffer.length === 0) {
      return;
    }

    try {
      const totalLength = this.audioBuffer.reduce((sum, buffer) => sum + buffer.length, 0);
      const mergedAudio = new Int16Array(totalLength);
      let offset = 0;

      for (const buffer of this.audioBuffer) {
        mergedAudio.set(buffer, offset);
        offset += buffer.length;
      }

      const frameCount = this.audioBuffer.length;
      WebSocketService.sendAudioChunk(mergedAudio.buffer);
      this.stats.chunksSent++;
      this.audioBuffer = [];
      // 发送缓冲音频日志已移除，减少噪音
    } catch (error) {
      console.error('EnhancedAudioInputService: 发送缓冲音频失败:', error);
      this.stats.errors++;
      this.stats.lastError = `sendBuffered: ${error}`;
      this.audioBuffer = [];
    }
  }

  async startRecording(): Promise<boolean> {
    if (this.isRecording) {
      console.log('EnhancedAudioInputService: Already recording');
      return true;
    }

    try {
      const initialized = await this.initialize();
      if (!initialized) {
        return false;
      }

      console.log('EnhancedAudioInputService: Starting recording...');
      const hasPermission = await this.voiceProcessor.hasRecordAudioPermission();
      if (!hasPermission) {
        console.error('EnhancedAudioInputService: No recording permission');
        return false;
      }

      this.stats.startAttempts++;
      await this.voiceProcessor.start(this.config.frameLength, this.config.sampleRate);
      this.isRecording = true;
      console.log(`EnhancedAudioInputService: Recording started at ${this.config.sampleRate}Hz`);
      return true;
    } catch (error: any) {
      this.stats.errors++;
      this.stats.lastError = `start: ${error.message}`;
      console.error('EnhancedAudioInputService: Failed to start recording:', error);
      return false;
    }
  }

  async stopRecording(): Promise<void> {
    if (!this.isRecording) {
      console.log('EnhancedAudioInputService: Not recording');
      return;
    }

    try {
      console.log('EnhancedAudioInputService: Stopping recording...');
      if (this.audioBuffer.length > 0 && WebSocketService.isConnected() && WebSocketService.isSetupComplete()) {
        console.log('EnhancedAudioInputService: 发送剩余缓冲音频数据');
        this.sendBufferedAudio();
      }

      await this.voiceProcessor.stop();
      if (this.frameListener) {
        this.voiceProcessor.removeFrameListener(this.frameListener);
      }
      if (this.errorListener) {
        this.voiceProcessor.removeErrorListener(this.errorListener);
      }

      this.audioBuffer = [];
      this.lastSendTime = 0;
      if (this.silenceTimer) {
        clearTimeout(this.silenceTimer);
        this.silenceTimer = null;
      }
      this.streamEnded = true;

      this.isRecording = false;
      this.isMuted = false;
      console.log('EnhancedAudioInputService: Recording stopped');
    } catch (error) {
      console.error('EnhancedAudioInputService: Error stopping recording:', error);
    }
  }

  setMuted(muted: boolean): boolean {
    this.isMuted = muted;
    console.log(`EnhancedAudioInputService: Microphone ${muted ? 'muted' : 'unmuted'}`);
    return true;
  }

  isMicrophoneMuted(): boolean {
    return this.isMuted;
  }

  isRecordingActive(): boolean {
    return this.isRecording;
  }

  getServiceName(): string {
    return 'EnhancedAudioInputService (VoiceProcessor)';
  }

  getStats() {
    return {
      ...this.stats,
      isRecording: this.isRecording,
      isMuted: this.isMuted,
    };
  }

  async cleanup(): Promise<boolean> {
    try {
      console.log('EnhancedAudioInputService: Cleaning up...');
      if (this.isRecording) {
        await this.stopRecording();
      }

      if (this.isInCallManagerInitialized && InCallManager) {
        try {
          InCallManager.stop();
          console.log('EnhancedAudioInputService: InCallManager stopped');
        } catch (error) {
          console.warn('EnhancedAudioInputService: Error stopping InCallManager:', error);
        }
        this.isInCallManagerInitialized = false;
      }

      console.log('EnhancedAudioInputService: Cleanup complete');
      return true;
    } catch (error) {
      console.error('EnhancedAudioInputService: Cleanup error:', error);
      return false;
    }
  }
}

export default new EnhancedAudioInputService();
