import { BASE_URL } from '../utils/config';

export interface UserInfo {
  id: number;
  phone: string;
  nickname: string;
  balance_count: number;
  balance_duration: number;
  paid_total_duration: number;
  paid_used_duration: number;
  paid_remaining_duration: number;
  trial_total_duration: number;
  trial_used_duration: number;
  trial_remaining_duration: number;
  total_remaining_duration: number;
  free_trial_count: number;
  ab_test_group: string;
}

class UserService {
  private getAuthHeaders(token: string) {
    return {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
    };
  }

  // 获取用户信息
  async getUserInfo(token: string): Promise<UserInfo> {
    console.log('[DEBUG] UserService.getUserInfo - 开始请求用户信息');
    const response = await fetch(`${BASE_URL}/api/v1/user/info`, {
      method: 'GET',
      headers: this.getAuthHeaders(token),
    });
    
    const data = await response.json();
    console.log('[DEBUG] UserService.getUserInfo - 后端响应:', JSON.stringify(data, null, 2));
    
    if (data.code === 200) {
      console.log('[DEBUG] UserService.getUserInfo - 返回的用户数据:', JSON.stringify(data.data, null, 2));
      return data.data;
    }
    throw new Error(data.message || '获取用户信息失败');
  }

  // 更新用户信息
  async updateUserInfo(token: string, userInfo: Partial<UserInfo>): Promise<UserInfo> {
    const response = await fetch(`${BASE_URL}/api/v1/user/update`, {
      method: 'PUT',
      headers: this.getAuthHeaders(token),
      body: JSON.stringify(userInfo),
    });
    
    const data = await response.json();
    if (data.code === 200) {
      return data.data;
    }
    throw new Error(data.message || '更新用户信息失败');
  }
}

export default new UserService();