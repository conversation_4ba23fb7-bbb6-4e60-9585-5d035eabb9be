/**
 * 智能音频服务选择器
 * 根据设备能力自动选择最佳的音频播放方案
 */

import { Platform, NativeModules } from 'react-native';
import EnhancedMemoryAudioOutputService, { EnhancedMemoryAudioOutputService as EnhancedMemoryAudioOutputServiceClass } from './EnhancedMemoryAudioOutputService';
import { AUDIO_CONFIG } from '../utils/config';

export type AudioServiceType = 'enhanced-memory' | 'disabled';

export interface AudioServiceInfo {
  type: AudioServiceType;
  name: string;
  description: string;
  service: any;
  priority: number;
  supported: boolean;
  features: string[];
  performance: {
    latency: string;
    quality: string;
    compatibility: string;
  };
  limitations: string[];
}

export class SmartAudioServiceSelector {
  private static instance: SmartAudioServiceSelector;
  private selectedService: AudioServiceInfo | null = null;
  private availableServices: AudioServiceInfo[] = [];

  private constructor() {}

  static getInstance(): SmartAudioServiceSelector {
    if (!SmartAudioServiceSelector.instance) {
      SmartAudioServiceSelector.instance = new SmartAudioServiceSelector();
    }
    return SmartAudioServiceSelector.instance;
  }

  // 检测并选择最佳音频服务
  async selectBestAudioService(): Promise<AudioServiceInfo> {
    console.log('SmartAudioServiceSelector: 开始检测最佳音频服务...');

    // 获取所有可用服务
    this.availableServices = await this.detectAvailableServices();
    
    // 按优先级排序
    this.availableServices.sort((a, b) => b.priority - a.priority);
    
    // 选择第一个支持的服务
    const bestService = this.availableServices.find(service => service.supported);
    
    if (bestService) {
      this.selectedService = bestService;
      console.log(`SmartAudioServiceSelector: 选择了 ${bestService.name}`);

      // 初始化选择的音频服务
      if (bestService.service) {
        try {
          // 确保传递正确的24kHz采样率配置
          const initConfig = {
            sampleRate: AUDIO_CONFIG.receiveSampleRate, // 24000Hz
            channels: AUDIO_CONFIG.channels, // 1
            volumeGain: 1.5
          };
          console.log('SmartAudioServiceSelector: 初始化音频服务，配置:', initConfig);
          await bestService.service.init(initConfig);
          console.log('SmartAudioServiceSelector: EnhancedMemoryAudioOutputService 初始化成功');
        } catch (error: any) {
          console.error(`SmartAudioServiceSelector: ${bestService.name} 初始化失败:`, error);
          // 初始化失败，更新服务状态并记录限制
          bestService.supported = false; 
          bestService.limitations.push(`初始化失败: ${error.message}`);
        }
      }

      return bestService;
    } else {
      // 如果没有支持的服务，返回禁用状态
      const disabledService = this.createDisabledService();
      this.selectedService = disabledService;
      console.log('SmartAudioServiceSelector: 所有音频服务都不支持，音频功能已禁用');
      return disabledService;
    }
  }

  // 检测所有可用的音频服务
  private async detectAvailableServices(): Promise<AudioServiceInfo[]> {
    const services: AudioServiceInfo[] = [];

    // 增强版内存音频流服务
    const enhancedMemoryService = await this.detectEnhancedMemoryService();
    services.push(enhancedMemoryService);

    return services;
  }

  // 检测增强版内存音频流服务
  private async detectEnhancedMemoryService(): Promise<AudioServiceInfo> {
    try {
      const supported = EnhancedMemoryAudioOutputServiceClass.isSupported();
      const hasEnhancedModule = !!(NativeModules.EnhancedAudioStreamPlayer && 
                                  typeof NativeModules.EnhancedAudioStreamPlayer.init === 'function');
      const hasStandardModule = !!(NativeModules.AudioStreamPlayer && 
                                  typeof NativeModules.AudioStreamPlayer.init === 'function');

      let features = [
        '✅ 超低延迟（<50ms）',
        '✅ 无需临时文件',
        '✅ 高性能',
        '✅ 完全控制音频流'
      ];

      let description = '智能音频播放服务：';
      if (hasEnhancedModule) {
        features.unshift('✅ 音量增强（2x）', '✅ 动态音量调节');
        description += '优先使用增强版模块，支持音量增强';
      } else if (hasStandardModule) {
        description += '使用标准模块，提供基础音频播放';
      }

      return {
        type: 'enhanced-memory',
        name: '智能音频播放服务',
        description,
        service: supported ? EnhancedMemoryAudioOutputService : null,
        priority: 110,
        supported,
        features,
        performance: {
          latency: '<50ms',
          quality: 'excellent',
          compatibility: hasEnhancedModule ? 'enhanced-native' : 'standard-native'
        },
        limitations: supported ? [] : ['没有可用的音频播放模块']
      };
    } catch (error: any) {
      console.error('SmartAudioServiceSelector: Audio Service 检测失败:', error);
      return {
        type: 'enhanced-memory',
        name: '智能音频播放服务',
        description: '检测失败',
        service: null,
        priority: 110,
        supported: false,
        features: [],
        performance: {
          latency: 'unknown',
          quality: 'unknown',
          compatibility: 'unknown'
        },
        limitations: [`检测失败: ${error.message}`]
      };
    }
  }



  // 创建禁用服务
  private createDisabledService(): AudioServiceInfo {
    return {
      type: 'disabled',
      name: '音频播放已禁用',
      description: '设备不支持音频播放',
      service: null,
      priority: 0,
      supported: false,
      features: [
        '❌ 音频播放功能不可用',
        '✅ 其他功能正常工作',
        '✅ 文本对话仍然可用'
      ],
      performance: {
        latency: 'N/A',
        quality: 'disabled',
        compatibility: 'none'
      },
      limitations: [
        '原生音频模块不可用',
        '设备音频功能异常',
        '建议检查设备音频设置'
      ]
    };
  }

  // 获取当前选择的服务
  getCurrentService(): AudioServiceInfo | null {
    return this.selectedService;
  }

  // 获取所有可用服务
  getAllServices(): AudioServiceInfo[] {
    return this.availableServices;
  }

  // 强制选择特定服务
  async forceSelectService(type: AudioServiceType): Promise<AudioServiceInfo | null> {
    const service = this.availableServices.find(s => s.type === type);
    if (service && service.supported) {
      this.selectedService = service;
      console.log(`SmartAudioServiceSelector: 强制选择了 ${service.name}`);
      return service;
    } else {
      console.warn(`SmartAudioServiceSelector: 无法强制选择 ${type}，服务不支持或不存在`);
      return null;
    }
  }

  // 生成服务选择报告
  generateSelectionReport(): string {
    let report = '\n🎵 音频服务选择报告\n';
    report += '='.repeat(50) + '\n\n';

    if (this.selectedService) {
      report += `✅ 当前选择: ${this.selectedService.name}\n`;
      report += `📝 类型: ${this.selectedService.type}\n`;
      report += `📊 优先级: ${this.selectedService.priority}\n`;
      report += `🎯 描述: ${this.selectedService.description}\n\n`;

      report += '🔧 功能特性:\n';
      this.selectedService.features.forEach(feature => {
        report += `  ${feature}\n`;
      });
      report += '\n';

      report += '⚡ 性能指标:\n';
      report += `  延迟: ${this.selectedService.performance.latency}\n`;
      report += `  质量: ${this.selectedService.performance.quality}\n`;
      report += `  兼容性: ${this.selectedService.performance.compatibility}\n\n`;

      if (this.selectedService.limitations.length > 0) {
        report += '⚠️ 限制:\n';
        this.selectedService.limitations.forEach(limitation => {
          report += `  - ${limitation}\n`;
        });
        report += '\n';
      }
    }

    if (this.availableServices.length > 0) {
      report += '📋 所有可用服务:\n';
      this.availableServices.forEach((service, index) => {
        const status = service.supported ? '✅' : '❌';
        const current = service === this.selectedService ? ' (当前)' : '';
        report += `  ${index + 1}. ${status} ${service.name}${current}\n`;
        report += `     优先级: ${service.priority}, 延迟: ${service.performance.latency}\n`;
      });
    }

    report += '\n' + '='.repeat(50) + '\n';
    return report;
  }

  // 获取简化状态
  getSimpleStatus() {
    if (!this.selectedService) {
      return {
        type: 'unknown',
        name: '未选择',
        supported: false,
        latency: 'unknown'
      };
    }

    return {
      type: this.selectedService.type,
      name: this.selectedService.name,
      supported: this.selectedService.supported,
      latency: this.selectedService.performance.latency
    };
  }

  // 重置选择
  reset(): void {
    this.selectedService = null;
    this.availableServices = [];
  }
}

// 便捷函数
export const selectBestAudioService = async () => {
  const selector = SmartAudioServiceSelector.getInstance();
  const service = await selector.selectBestAudioService();
  const report = selector.generateSelectionReport();
  
  console.log(report);
  
  return service;
};

export default SmartAudioServiceSelector;
