// services/WebSocketService.ts
// Enhanced WebSocket Service for Gemini Live API

import { Buffer } from 'buffer';
import { getPreferredVoice as getValidatedPreferredVoice } from '../utils/voiceUtils';
import { BASE_URL, AUDIO_CONFIG } from '../utils/config';
import logger from '../utils/logger';

// 连接模式枚举
export enum ConnectionMode {
  AUTO = 'auto',      // 自动检测
  PROXY = 'proxy',    // 强制使用代理
  DIRECT = 'direct'   // 强制直连
}

interface WebSocketCallbacks {
  onMessage?: (message: any) => void;
  onStatusUpdate?: (status: string) => void;
  onError?: (error: string) => void;
  onInterruption?: () => void;
  onTurnComplete?: () => void;
  onTranscript?: (transcript: { text: string; isFinal: boolean; type: string }) => void;
}

class WebSocketService {
  private static instance: WebSocketService;
  private ws: WebSocket | null = null;
  private callbacks: WebSocketCallbacks = {};
  private setupCompleted = false;
  private hasLoggedServerContentJson = false;
  private heartbeatInterval: any = null;

  private isProxyConnection = false;
  private stats = {
    audioChunksSent: 0,
    audioMessagesReceived: 0,
  };

  private connectionMode: ConnectionMode = ConnectionMode.AUTO;
  private geminiConfig: any = null;
  private prompts: { [key: string]: string } = {};

  private constructor() { }

  public static getInstance(): WebSocketService {
    if (!WebSocketService.instance) {
      WebSocketService.instance = new WebSocketService();
    }
    return WebSocketService.instance;
  }

  private async getSystemInstruction(mode: string, customPrompt?: string): Promise<string> {
    // 如果有自定义提示词（模拟面试），直接使用
    if (customPrompt) {
      return customPrompt;
    }

    if (mode === 'leisure') {
      return "You are my helpful assistant.";
    }

    if (this.prompts[mode]) {
      return this.prompts[mode];
    }

    try {
      const response = await fetch(`${BASE_URL}/api/v1/prompts?mode=${mode}`);
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to fetch prompt: ${response.status} ${errorText}`);
      }
      const result = await response.json();
      if (result.code === 200 && result.data?.prompt) {
        const promptText = result.data.prompt as string;
        this.prompts[mode] = promptText;
        return promptText;
      } else {
        throw new Error(`Invalid prompt response for mode "${mode}": ${result.message || 'No prompt data'}`);
      }
    } catch (error) {
      console.error(`WebSocketService: Error fetching prompt for mode "${mode}":`, error);
      console.warn(`WebSocketService: Using default fallback prompt for mode "${mode}".`);
      return 'You are a helpful assistant. Please respond concisely and professionally.';
    }
  }

  setCallbacks(callbacks: WebSocketCallbacks) {
    this.callbacks = { ...this.callbacks, ...callbacks };
  }

  setConnectionMode(mode: ConnectionMode) {
    this.connectionMode = mode;
  }

  getCurrentMode(): ConnectionMode {
    return this.connectionMode;
  }

  isUsingProxy(): boolean {
    return this.isProxyConnection;
  }

  private determineConnectionMethod(): boolean {
    if (!this.geminiConfig) {
      return true; // Default to proxy
    }
    const backendMode = this.geminiConfig.connection_mode;
    switch (backendMode) {
      case 'proxy':
        return true;
      case 'direct':
        return false;
      case 'auto':
      default:
        return this.detectChinaNetwork();
    }
  }

  private detectChinaNetwork(): boolean {
    try {
      const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
      const isChinaTimezone = timezone.includes('Asia/Shanghai') ||
        timezone.includes('Asia/Beijing') ||
        timezone.includes('Asia/Chongqing');
      const nav = (global as any).navigator;
      const language = nav.language || (nav.languages && nav.languages[0]) || '';
      const isChineseLanguage = language.startsWith('zh');
      return isChinaTimezone && isChineseLanguage;
    } catch (error) {
      console.warn('WebSocketService: Network detection failed, defaulting to proxy:', error);
      return true;
    }
  }

  getCurrentConfig(): any {
    return this.geminiConfig;
  }

  private async getPreferredVoice(): Promise<string> {
    try {
      return await getValidatedPreferredVoice();
    } catch (error) {
      console.error('WebSocketService: Failed to get preferred voice, using default:', error);
      return "Aoede";
    }
  }

  async connect(apiKey?: string, mode: string = 'technical_interview', customPrompt?: string) {
    this.stats = { audioChunksSent: 0, audioMessagesReceived: 0 }; // Reset stats on new connection

    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      console.log('WebSocketService: Already connected');
      return;
    }

    this.hasLoggedServerContentJson = false;
    const actualApiKey = apiKey;

    if (!actualApiKey) {
      console.error('WebSocketService: API_KEY is missing!');
      this.callbacks.onError?.('API Key is missing.');
      return;
    }

    try {
      const { store } = require('../store');
      this.geminiConfig = store.getState().user.geminiConfig;
      if (!this.geminiConfig) {
        throw new Error('Gemini config not found in Redux store');
      }
    } catch (error: any) {
      console.error('WebSocketService: Failed to get Gemini config, using default:', error);
      this.geminiConfig = {
        connection_mode: 'proxy',
        model: 'models/gemini-live-2.5-flash-preview'
      };
    }

    const shouldUseProxy = this.determineConnectionMethod();
    this.isProxyConnection = shouldUseProxy;
    let url: string;

    if (shouldUseProxy) {
      const baseUrl = BASE_URL.replace('http://', 'ws://').replace('https://', 'wss://');
      const keyParam = actualApiKey.startsWith('auth_tokens/') ? 'access_token' : 'key';
      let urlParams = `${keyParam}=${actualApiKey}`;
      if (actualApiKey.startsWith('auth_tokens/')) {
        try {
          const { store } = require('../store');
          const userID = store.getState().user.userInfo?.id;
          if (userID) {
            urlParams += `&user_id=${userID}`;
          } else {
            console.warn('WebSocketService: User ID not found for temporary token.');
          }
        } catch (error) {
          console.error('WebSocketService: Failed to get user ID:', error);
        }
      }
      url = `${baseUrl}/api/v1/gemini/ws-proxy?${urlParams}`;
      console.log(`WebSocketService: Connecting via proxy. Mode: ${this.geminiConfig?.connection_mode}`);
    } else {
      const websocketHost = this.geminiConfig?.websocket_host || 'generativelanguage.googleapis.com';
      const apiVersion = this.geminiConfig?.api_version || 'v1alpha';
      let method = 'BidiGenerateContent';
      let keyName = 'key';
      if (actualApiKey.startsWith('auth_tokens/')) {
        method = 'BidiGenerateContentConstrained';
        keyName = 'access_token';
      }
      url = `wss://${websocketHost}/ws/google.ai.generativelanguage.${apiVersion}.GenerativeService.${method}?${keyName}=${actualApiKey}`;
      console.log(`WebSocketService: Connecting directly to Google API. Mode: ${this.geminiConfig?.connection_mode}`);
    }

    this.ws = new WebSocket(url);

    this.ws.onopen = async () => {
      const connectionType = this.isProxyConnection ? 'proxy' : 'direct';
      console.log(`WebSocketService: Connection established (${connectionType})`);
      await this.sendInitialSetup(mode, customPrompt);
      this.callbacks.onStatusUpdate?.('connected');
      this.startHeartbeat();
    };

    this.ws.onclose = (event) => {
      const code = event.code || 1006;
      const reason = event.reason || 'No reason provided';
      console.log(`WebSocketService: Connection closed. Code: ${code}, Reason: ${reason}`);
      this.ws = null;
      this.callbacks.onStatusUpdate?.('disconnected');
    };

    this.ws.onerror = (error: any) => {
      const errorMessage = error?.message || 'Unknown WebSocket error';
      console.error(`WebSocketService: WebSocket Error: ${errorMessage}`);
      this.callbacks.onError?.(errorMessage);
      this.callbacks.onStatusUpdate?.('error');
    };

    this.ws.onmessage = (event) => {
      this.handleMessage(event);
    };
  }

  disconnect() {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
    this.stopHeartbeat();
    this.setupCompleted = false;
    this.hasLoggedServerContentJson = false;
    console.log('WebSocketService: Disconnected and state reset.');
  }

  private async sendInitialSetup(mode: string, customPrompt?: string) {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
      console.error('WebSocketService: Cannot send setup - not connected');
      return false;
    }

    const modelName = this.geminiConfig?.model || 'models/gemini-live-2.5-flash-preview';
    const voiceName = await this.getPreferredVoice();

    try {
      const setupMessage = {
        setup: {
          model: modelName,
          systemInstruction: { parts: [{ text: await this.getSystemInstruction(mode, customPrompt) }] },
          generationConfig: {
            speechConfig: {
              languageCode: "cmn-CN",
              voiceConfig: { prebuiltVoiceConfig: { voiceName: voiceName } }
            },
            mediaResolution: "MEDIA_RESOLUTION_LOW",
            responseModalities: ["AUDIO"]
          },
          // 启用输入音频转录功能（用户语音转文字）
          inputAudioTranscription: {},
          // 启用输出音频转录功能（AI回复转文字）
          outputAudioTranscription: {},
          tools: [{ googleSearch: {} }],
          proactivity: { proactiveAudio: false },
          //sessionResumption: { transparent: true },
          contextWindowCompression: { triggerTokens: "8000", slidingWindow: { targetTokens: "4000" } },
          realtimeInputConfig: {
            automaticActivityDetection: {
              disabled: false,
              startOfSpeechSensitivity: "START_SENSITIVITY_HIGH",
              endOfSpeechSensitivity: "END_SENSITIVITY_HIGH",
              prefixPaddingMs: 20,
              silenceDurationMs: 1000,
            }
          }
        }
      };
      console.log(`WebSocketService: Sending setup message with model: ${modelName}`);
      this.ws.send(JSON.stringify(setupMessage));
      return true;
    } catch (error) {
      console.error('WebSocketService: Error sending setup:', error);
      return false;
    }
  }



  private handleMessage(event: { data?: any }) {
    try {
      if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
        return;
      }
      if (event.data instanceof ArrayBuffer || event.data instanceof Blob) {
        this.handleBinaryData(event.data);
      } else if (typeof event.data === 'string') {
        // 异步解析JSON，避免阻塞WebSocket接收线程
        this.parseJsonAsync(event.data);
      }
    } catch (error) {
      console.error('WebSocketService: Error handling message:', error);
    }
  }

  private parseJsonAsync(jsonString: string) {
    // 使用setImmediate优先处理JSON解析
    if (typeof setImmediate !== 'undefined') {
      setImmediate(() => {
        try {
          const message = JSON.parse(jsonString);
          this.handleJsonMessage(message);
        } catch (error) {
          console.error('WebSocketService: Error parsing JSON:', error);
        }
      });
    } else {
      setTimeout(() => {
        try {
          const message = JSON.parse(jsonString);
          this.handleJsonMessage(message);
        } catch (error) {
          console.error('WebSocketService: Error parsing JSON:', error);
        }
      }, 0);
    }
  }

  private handleBinaryData(data: ArrayBuffer | Blob) {
    if (data instanceof Blob) {
      const reader = new FileReader();
      reader.onload = () => {
        this.processBinaryData(reader.result as ArrayBuffer);
      };
      reader.readAsArrayBuffer(data);
    } else {
      this.processBinaryData(data);
    }
  }

  private processBinaryData(data: ArrayBuffer) {
    const bytes = new Uint8Array(data);
    const looksLikeJson = bytes.length > 0 && (bytes[0] === 123 || bytes[0] === 91);
    if (looksLikeJson) {
      const jsonText = Buffer.from(bytes).toString('utf-8');
      try {
        const jsonData = JSON.parse(jsonText);
        this.handleJsonMessage(jsonData);
      } catch (error) {
        console.error('WebSocketService: Error parsing binary JSON:', error);
      }
    } else {
      this.callbacks.onMessage?.({ type: 'raw-pcm', data });
    }
  }

  private handleJsonMessage(message: any) {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
      return;
    }

    if (message.setupComplete !== undefined) {
      console.log('WebSocketService: Setup complete');
      this.setupCompleted = true;
      return;
    }

    // Handle session resumption updates
    if (message.sessionResumptionUpdate !== undefined) {
      console.log('WebSocketService: Session resumption update received:', message.sessionResumptionUpdate);
      if (message.sessionResumptionUpdate.newHandle) {
        console.log('WebSocketService: New session handle available for resumption');
        // You can store the newHandle for session resumption if needed
        // this.sessionHandle = message.sessionResumptionUpdate.newHandle;
      }
      return;
    }

    if (!message.serverContent && !message.setupComplete && !message.error && !message.sessionResumptionUpdate) {
      console.warn('WebSocketService: Received unknown message format:', JSON.stringify(message, null, 2));
    }

    if (message.serverContent) {
      if (!this.hasLoggedServerContentJson) {
        this.hasLoggedServerContentJson = true;
      }

      if (message.serverContent.inputTranscription?.text) {
        const { text, is_final: isFinal = false } = message.serverContent.inputTranscription;
        setTimeout(() => this.callbacks.onTranscript?.({ text, isFinal, type: 'user' }), 0);
      }

      if (message.serverContent.outputTranscription?.text) {
        const { text, is_final: isFinal = false } = message.serverContent.outputTranscription;
        setTimeout(() => this.callbacks.onTranscript?.({ text, isFinal, type: 'assistant' }), 0);
      }

      if (message.serverContent.modelTurn?.parts) {
        for (const part of message.serverContent.modelTurn.parts) {
          if (part.text) {
            setTimeout(() => this.callbacks.onTranscript?.({ text: part.text, isFinal: true, type: 'assistant' }), 0);
          }
          if (part.inlineData?.mimeType && part.inlineData?.data) {
            this.stats.audioMessagesReceived++; // 每个包含音频数据的part都计数

            // 直接传递给原生层，无需JavaScript队列缓冲
            const receiveTime = Date.now();
            logger.info('WebSocketService', `🎵 音频包直接传递，大小: ${part.inlineData.data.length}字节`);

            try {
              // 直接同步调用，让原生层处理所有缓冲
              this.callbacks.onMessage?.({
                type: 'audio',
                data: part.inlineData.data, // 直接传递base64字符串
                mimeType: part.inlineData.mimeType
              });

              const processTime = Date.now() - receiveTime;
              logger.info('WebSocketService', `✅ 音频包直接传递完成，耗时: ${processTime}ms`);
            } catch (error: any) {
              logger.error('WebSocketService', 'Error in direct audio transfer:', error);
            }
          }
        }
      }

      if (message.serverContent.interrupted) {
        console.log('WebSocketService: Conversation interrupted');
        this.callbacks.onInterruption?.();
      }

      if (message.serverContent.turnComplete) {
        console.log('WebSocketService: Turn complete');
        this.callbacks.onTurnComplete?.();
      }
    }


    if (message.error) {
      console.error('WebSocketService: Server error:', message.error);
      this.callbacks.onError?.(message.error.message || 'Server error');
    }
  }

  sendAudioChunk(audioData: ArrayBuffer) {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
      logger.warn('WebSocketService', 'Cannot send audio - not connected');
      return;
    }
    try {
      const base64Audio = Buffer.from(audioData).toString('base64');
      const message = {
        realtimeInput: {
          audio: {
            mimeType: `audio/pcm;rate=${AUDIO_CONFIG.sendSampleRate}`,
            data: base64Audio
          }
        }
      };
      this.ws.send(JSON.stringify(message));
      this.stats.audioChunksSent++;
    } catch (error: any) {
      logger.error('WebSocketService', 'Error sending audio:', error);
    }
  }

  sendAudioStreamEnd() {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
      console.warn('WebSocketService: Cannot send audio stream end - not connected');
      return;
    }
    try {
      const message = { realtimeInput: { audioStreamEnd: true } };
      console.log('WebSocketService: Sending audio stream end signal.');
      this.ws.send(JSON.stringify(message));

      // 重置音频统计，为下次对话做准备
      this.stats.audioChunksSent = 0;
      this.stats.audioMessagesReceived = 0;
    } catch (error: any) {
      console.error('WebSocketService: Error sending audio stream end:', error);
    }
  }

  sendControlMessage(message: object) {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
      console.error('WebSocketService: Cannot send control message - not connected');
      return;
    }
    try {
      console.log('WebSocketService: Sending control message:', message);
      this.ws.send(JSON.stringify(message));
    } catch (error: any) {
      console.error('WebSocketService: Error sending control message:', error);
    }
  }

  sendTextInput(text: string) {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
      console.error('WebSocketService: Cannot send text - not connected');
      return;
    }
    const textMessage = {
      clientContent: {
        turns: [{ role: 'USER', parts: [{ text }] }],
        turnComplete: true,
      },
    };
    this.ws.send(JSON.stringify(textMessage));
  }

  private startHeartbeat() {
    if (!this.isProxyConnection) {
      return;
    }
    this.stopHeartbeat();
    this.heartbeatInterval = setInterval(() => {
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        this.ws.send(JSON.stringify({ type: 'heartbeat', timestamp: Date.now() }));
      }
    }, 30000); // 30s
  }

  private stopHeartbeat() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
  }



  isConnected(): boolean {
    return this.ws !== null && this.ws.readyState === WebSocket.OPEN;
  }

  isSetupComplete(): boolean {
    return this.setupCompleted;
  }

  getAudioStats() {
    return {
      sent: this.stats.audioChunksSent,
      received: this.stats.audioMessagesReceived,
    };
  }

  resetAudioStats() {
    logger.info('WebSocketService', '重置音频统计信息');
    this.stats.audioChunksSent = 0;
    this.stats.audioMessagesReceived = 0;
  }
}

export default WebSocketService.getInstance();
