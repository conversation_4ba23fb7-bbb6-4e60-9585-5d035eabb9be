/**
 * 增强版内存音频流播放服务 - 支持音量增益和自适应抖动缓冲
 */
import { NativeModules, Platform } from 'react-native';
import { AUDIO_CONFIG } from '../utils/config';
import { Buffer } from 'buffer';
import logger from '../utils/logger';


// 扩展原生模块的接口定义，以便TypeScript识别新方法
interface AudioPlayerModule {
  init(config: any): Promise<void>;
  write(base64Data: string): Promise<void>;
  stop(): Promise<void>;
  setVolumeGain?(gain: number): Promise<void>;
  getPendingBufferCount?(): Promise<number>; // AJB buffer size
  getStats?(): Promise<any>; // 详细统计信息
}

const EnhancedAudioStreamPlayer = NativeModules.EnhancedAudioStreamPlayer as AudioPlayerModule;
const AudioStreamPlayer = NativeModules.AudioStreamPlayer as AudioPlayerModule;

interface EnhancedAudioOutputConfig {
  sampleRate: number;
  channels: number;
  volumeGain: number; // 音量增益倍数，1.0为原始音量，2.0为2倍音量
}

class EnhancedMemoryAudioOutputService {
  private isInitialized = false;
  private useEnhancedModule = false;

  private stats = {
    initCount: 0,
    chunksReceived: 0,
    errors: 0,
    lastError: '',
    messagesReceived: 0,
  };

  private config: EnhancedAudioOutputConfig = {
    sampleRate: AUDIO_CONFIG.receiveSampleRate,
    channels: AUDIO_CONFIG.channels,
    volumeGain: 1.5, // 默认2倍音量增益
  };

  async init(config?: Partial<EnhancedAudioOutputConfig>): Promise<void> {
    if (this.isInitialized) {
      return;
    }
    if (config) {
      this.config = { ...this.config, ...config };
    }

    try {
      const player = Platform.OS === 'ios' ? AudioStreamPlayer : EnhancedAudioStreamPlayer;
      if (player && typeof player.init === 'function') {
        logger.info('AudioOutput', `初始化音频播放模块，配置: ${JSON.stringify(this.config)}`);
        logger.info('AudioOutput', `关键：确保使用采样率 ${this.config.sampleRate}Hz (24kHz) 进行播放`);
        await player.init(this.config);
        this.useEnhancedModule = true; // Both modules now support similar features
      } else {
        throw new Error('没有可用的音频播放模块');
      }

      this.isInitialized = true;
      this.stats.initCount++;
    } catch (error: any) {
      this.stats.errors++;
      this.stats.lastError = `init: ${error.message}`;
      logger.error('AudioOutput', '初始化失败:', error);
      throw error;
    }
  }

  playChunk(chunk: string): void {
    this.stats.chunksReceived++;
    this.stats.messagesReceived++;

    if (!this.isInitialized) {
      this.stats.errors++;
      this.stats.lastError = 'playChunk: Not initialized';
      logger.error('AudioOutput', '服务未初始化，但不能丢弃音频数据！需要缓存或重新初始化');
      // 不能直接返回丢弃数据，应该尝试重新初始化或缓存
      // TODO: 实现重新初始化或缓存机制
      throw new Error('AudioOutput service not initialized - cannot drop audio data');
    }

    // 优化：直接使用base64字符串，避免解码→编码循环
    if (typeof chunk !== 'string') {
      throw new Error('Invalid chunk format: expected base64 string');
    }

    const dataSize = Math.floor(chunk.length * 3 / 4); // base64解码后的大概大小
    logger.audioInfo('AudioOutput', `🚀 优化路径：直接使用base64数据，长度=${chunk.length}, 估算二进制大小=${dataSize}字节`);

    const player = Platform.OS === 'ios' ? AudioStreamPlayer : EnhancedAudioStreamPlayer;

    player.write(chunk).then(() => {
      logger.audioSuccess('AudioOutput', '音频块已成功发送到AJB');
    }).catch(error => {
      this.stats.errors++;
      this.stats.lastError = `write: ${error.message}`;
      logger.audioError('AudioOutput', '播放失败:', error);
    });
  }

  async setVolumeGain(gain: number): Promise<void> {
    try {
      this.config.volumeGain = gain;
      if (this.isInitialized && this.useEnhancedModule) {
        const player = Platform.OS === 'ios' ? AudioStreamPlayer : EnhancedAudioStreamPlayer;
        if (player.setVolumeGain) {
          await player.setVolumeGain(gain);
          logger.info('AudioOutput', '音量增益已更新为:', gain);
        }
      }
    } catch (error: any) {
      logger.error('AudioOutput', '设置音量增益失败:', error);
    }
  }



  immediateStop(): void {
    logger.info('AudioOutput', 'Immediate stop requested. Flushing native buffers.');
    this.stop();
  }

  async stop(): Promise<void> {
    if (!this.isInitialized) {
      return;
    }

    try {
      const player = Platform.OS === 'ios' ? AudioStreamPlayer : EnhancedAudioStreamPlayer;
      await player.stop();
      this.isInitialized = false;
      logger.info('AudioOutput', '音频播放已停止');
    } catch (error: any) {
      this.stats.errors++;
      this.stats.lastError = `stop: ${error.message}`;
      logger.error('AudioOutput', '停止播放失败:', error);
    }
  }

  resetStats(): void {
    logger.info('AudioOutput', '重置统计信息');
    this.stats = {
      initCount: this.stats.initCount,
      chunksReceived: 0,
      errors: 0,
      lastError: '',
      messagesReceived: 0,
    };
  }

  static isSupported(): boolean {
    const player = Platform.OS === 'ios' ? AudioStreamPlayer : EnhancedAudioStreamPlayer;
    return !!(player && typeof player.init === 'function');
  }

  async getStats(): Promise<any> {
    let nativeStats: any = {
      nativeBufferCount: 'N/A',
      jitter: 'N/A',
      packetLossRate: 'N/A',
    };

    if (this.isInitialized) {
      try {
        const player = Platform.OS === 'ios' ? AudioStreamPlayer : EnhancedAudioStreamPlayer;
        if (player.getPendingBufferCount) {
          nativeStats.nativeBufferCount = await player.getPendingBufferCount();
        }
        // 暂时移除getStats调用，因为接口定义不完整
      } catch (e) {
        logger.error('AudioOutput', 'getStats failed to get native stats', e);
        nativeStats.nativeBufferCount = 'Error';
      }
    }

    return {
      ...this.stats,
      isInitialized: this.isInitialized,
      ...nativeStats,
      volumeGain: this.config.volumeGain,
      moduleType: 'AJB',
      chunksPlayed: this.stats.chunksReceived, // 假设接收到的都被播放了
      pendingQueueSize: nativeStats.nativeBufferCount,
    };
  }
}

const enhancedMemoryAudioOutputService = new EnhancedMemoryAudioOutputService();

// 导出实例和类
export default enhancedMemoryAudioOutputService;
export { EnhancedMemoryAudioOutputService };
