import { BASE_URL } from '../utils/config';
import { Platform, Alert, DeviceEventEmitter } from 'react-native';
import * as WeChat from 'react-native-wechat-lib';
import Alipay from '@uiw/react-native-alipay';
import { PAYMENT_CONFIG } from '../config/payment';

export interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  duration: number; // 时长（秒）
  type: number; // 2: 时长包
  status: number;
}

export interface Order {
  id: number;
  order_no: string;
  user_id: number;
  product_id: string;
  amount: number;
  status: number; // 0: 待支付, 1: 已支付, 2: 已关闭
  pay_type?: string;
  created_at: string;
  paid_at?: string;
  product?: Product;
}

export interface PaymentMethod {
  type: string;
  name: string;
  icon: string;
  description: string;
  enabled: boolean;
}

export interface WeChatPayResponse {
  prepay_id: string;
  code_url?: string;        // 二维码URL，移动端不需要
  pay_sign: string;
  timestamp: string;
  nonce_str: string;
  package: string;
  sign_type: string;
  app_id: string;          // 移动端需要的参数
  partner_id: string;      // 商户号
}

export interface AlipayResponse {
  order_string: string;    // 移动端支付宝SDK需要的订单信息字符串
}

export interface MobilePaymentResult {
  success: boolean;
  message?: string;
  errorCode?: string;
}

export interface PaymentStatus {
  orderNo: string;
  status: 'pending' | 'processing' | 'success' | 'failed' | 'cancelled';
  message?: string;
  timestamp: number;
}

class PaymentService {
  private isWeChatInstalled = false;
  private paymentStatusListeners: Map<string, (status: PaymentStatus) => void> = new Map();

  constructor() {
    this.initializeMobilePayment();
  }

  private async initializeMobilePayment() {
    try {
      // 检查配置是否有效
      if (!PAYMENT_CONFIG.WECHAT.APP_ID || PAYMENT_CONFIG.WECHAT.APP_ID === 'wx1234567890abcdef') {
        console.warn('⚠️ 微信支付配置无效，请配置真实的APP_ID');
        this.isWeChatInstalled = false;
        return;
      }
      
      // 初始化微信SDK
      console.log('正在初始化微信SDK，APP_ID:', PAYMENT_CONFIG.WECHAT.APP_ID);
      await WeChat.registerApp(PAYMENT_CONFIG.WECHAT.APP_ID, PAYMENT_CONFIG.WECHAT.UNIVERSAL_LINK);
      this.isWeChatInstalled = await WeChat.isWXAppInstalled();
      console.log('微信SDK初始化完成，微信安装状态:', this.isWeChatInstalled);
    } catch (error) {
      console.error('微信SDK初始化失败:', error);
      this.isWeChatInstalled = false;
    }
  }

  private getAuthHeaders(token: string) {
    return {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
    };
  }

  // 支付状态监控
  addPaymentStatusListener(orderNo: string, callback: (status: PaymentStatus) => void) {
    this.paymentStatusListeners.set(orderNo, callback);
  }

  removePaymentStatusListener(orderNo: string) {
    this.paymentStatusListeners.delete(orderNo);
  }

  private notifyPaymentStatus(status: PaymentStatus) {
    const listener = this.paymentStatusListeners.get(status.orderNo);
    if (listener) {
      listener(status);
    }
    // 发送全局事件
    DeviceEventEmitter.emit('PaymentStatusChanged', status);
  }

  // 获取商品列表
  async getProducts(token: string): Promise<Product[]> {
    const response = await fetch(`${BASE_URL}/api/v1/product/list`, {
      method: 'GET',
      headers: this.getAuthHeaders(token),
    });
    
    const data = await response.json();
    if (data.code === 200) {
      return data.data || [];
    }
    throw new Error(data.message || '获取商品列表失败');
  }

  // 创建订单
  async createOrder(token: string, productId: string): Promise<Order> {
    const response = await fetch(`${BASE_URL}/api/v1/order/create`, {
      method: 'POST',
      headers: this.getAuthHeaders(token),
      body: JSON.stringify({ product_id: productId }),
    });
    
    const data = await response.json();
    if (data.code === 200) {
      return data.data;
    }
    throw new Error(data.message || '创建订单失败');
  }

  // 获取支付方式
  async getPaymentMethods(token: string): Promise<PaymentMethod[]> {
    const response = await fetch(`${BASE_URL}/api/v1/payment/methods`, {
      method: 'GET',
      headers: this.getAuthHeaders(token),
    });
    
    const data = await response.json();
    if (data.code === 200) {
      return data.data || [];
    }
    throw new Error(data.message || '获取支付方式失败');
  }

  // 创建微信支付
  async createWeChatPayment(token: string, orderNo: string): Promise<WeChatPayResponse> {
    const response = await fetch(`${BASE_URL}/api/v1/payment/wechat/pay`, {
      method: 'POST',
      headers: this.getAuthHeaders(token),
      body: JSON.stringify({ order_no: orderNo }),
    });
    
    const data = await response.json();
    if (data.code === 200) {
      return data.data;
    }
    throw new Error(data.message || '创建微信支付失败');
  }

  // 创建支付宝App支付
  async createAlipayPayment(token: string, orderNo: string, returnUrl?: string): Promise<AlipayResponse> {
    const response = await fetch(`${BASE_URL}/api/v1/payment/alipay/pay`, {
      method: 'POST',
      headers: this.getAuthHeaders(token),
      body: JSON.stringify({ 
        order_no: orderNo,
        return_url: returnUrl || '' // App支付不需要返回URL
      }),
    });
    
    const data = await response.json();
    if (data.code === 200) {
      return data.data;
    }
    throw new Error(data.message || '创建支付宝App支付失败');
  }

  // 查询订单状态
  async getOrderStatus(token: string, orderNo: string): Promise<Order> {
    const response = await fetch(`${BASE_URL}/api/v1/order/${orderNo}/status`, {
      method: 'GET',
      headers: this.getAuthHeaders(token),
    });
    
    const data = await response.json();
    if (data.code === 200) {
      return data.data;
    }
    throw new Error(data.message || '查询订单状态失败');
  }

  // 取消订单
  async cancelOrder(token: string, orderNo: string): Promise<void> {
    const response = await fetch(`${BASE_URL}/api/v1/order/${orderNo}/cancel`, {
      method: 'POST',
      headers: this.getAuthHeaders(token),
    });
    
    const data = await response.json();
    if (data.code !== 200) {
      throw new Error(data.message || '取消订单失败');
    }
  }

  // 获取用户订单列表
  async getUserOrders(token: string, page: number = 1, pageSize: number = 20): Promise<{orders: Order[], total: number}> {
    const response = await fetch(`${BASE_URL}/api/v1/order/list?page=${page}&page_size=${pageSize}`, {
      method: 'GET',
      headers: this.getAuthHeaders(token),
    });
    
    const data = await response.json();
    if (data.code === 200) {
      // 根据实际API响应结构解析数据
      const responseData = data.data || {};
      return {
        orders: responseData.list || [],
        total: responseData.total || 0
      };
    }
    throw new Error(data.message || '获取订单列表失败');
  }

  // 移动端支付方法
  async checkWeChatInstalled(): Promise<boolean> {
    try {
      // 先检查微信SDK是否正确初始化
      if (!PAYMENT_CONFIG.WECHAT.APP_ID || PAYMENT_CONFIG.WECHAT.APP_ID === 'wx1234567890abcdef') {
        console.error('微信APP_ID未配置或使用默认值');
        return false;
      }
      
      const isInstalled = await WeChat.isWXAppInstalled();
      console.log('微信安装状态检查结果:', isInstalled);
      return isInstalled;
    } catch (error) {
      console.error('检查微信安装状态失败:', error);
      // 如果检查失败，可能是SDK配置问题
      return false;
    }
  }

  async payWithWeChat(token: string, orderNo: string): Promise<MobilePaymentResult> {
    try {
      this.notifyPaymentStatus({
        orderNo,
        status: 'processing',
        message: '正在调起微信支付...',
        timestamp: Date.now()
      });

      // 检查微信是否安装和配置
      const isInstalled = await this.checkWeChatInstalled();
      if (!isInstalled) {
        let errorMessage = '请先安装微信客户端';
        
        // 检查是否是配置问题
        if (!PAYMENT_CONFIG.WECHAT.APP_ID || PAYMENT_CONFIG.WECHAT.APP_ID === 'wx1234567890abcdef') {
          errorMessage = '微信支付配置错误，请联系客服';
          console.error('微信支付配置错误: APP_ID未正确配置');
        }
        
        const result = {
          success: false,
          message: errorMessage
        };
        this.notifyPaymentStatus({
          orderNo,
          status: 'failed',
          message: result.message,
          timestamp: Date.now()
        });
        return result;
      }

      // 获取微信支付参数
      const paymentData: WeChatPayResponse = await this.createWeChatPayment(token, orderNo);
      
      // 调起微信支付 (适配新版本 API)
      const payResult = await WeChat.pay({
        partnerId: paymentData.partner_id,
        prepayId: paymentData.prepay_id,
        nonceStr: paymentData.nonce_str,
        timeStamp: paymentData.timestamp,
        package: paymentData.package,
        sign: paymentData.pay_sign,
      });

      if (payResult.errCode === 0) {
        const result = {
          success: true,
          message: '支付成功'
        };
        this.notifyPaymentStatus({
          orderNo,
          status: 'success',
          message: result.message,
          timestamp: Date.now()
        });
        return result;
      } else {
        const result = {
          success: false,
          message: this.getWeChatErrorMessage(payResult.errCode),
          errorCode: payResult.errCode.toString()
        };
        this.notifyPaymentStatus({
          orderNo,
          status: payResult.errCode === -2 ? 'cancelled' : 'failed',
          message: result.message,
          timestamp: Date.now()
        });
        return result;
      }
    } catch (error) {
      console.error('微信支付失败:', error);
      const result = {
        success: false,
        message: error instanceof Error ? error.message : '微信支付失败'
      };
      this.notifyPaymentStatus({
        orderNo,
        status: 'failed',
        message: result.message,
        timestamp: Date.now()
      });
      return result;
    }
  }

  async payWithAlipay(token: string, orderNo: string): Promise<MobilePaymentResult> {
    try {
      this.notifyPaymentStatus({
        orderNo,
        status: 'processing',
        message: '正在调起支付宝支付...',
        timestamp: Date.now()
      });

      // 获取支付宝支付参数
      const paymentData: AlipayResponse = await this.createAlipayPayment(token, orderNo);
      
      // 调起支付宝支付
      const payResult = await Alipay.alipay(paymentData.order_string);
      
      if (payResult.resultStatus === '9000') {
        const result = {
          success: true,
          message: '支付成功'
        };
        this.notifyPaymentStatus({
          orderNo,
          status: 'success',
          message: result.message,
          timestamp: Date.now()
        });
        return result;
      } else {
        const result = {
          success: false,
          message: this.getAlipayErrorMessage(payResult.resultStatus),
          errorCode: payResult.resultStatus
        };
        this.notifyPaymentStatus({
          orderNo,
          status: payResult.resultStatus === '6001' ? 'cancelled' : 'failed',
          message: result.message,
          timestamp: Date.now()
        });
        return result;
      }
    } catch (error) {
      console.error('支付宝支付失败:', error);
      const result = {
        success: false,
        message: error instanceof Error ? error.message : '支付宝支付失败'
      };
      this.notifyPaymentStatus({
        orderNo,
        status: 'failed',
        message: result.message,
        timestamp: Date.now()
      });
      return result;
    }
  }

  private getWeChatErrorMessage(errCode: number): string {
    switch (errCode) {
      case -1:
        return '支付失败';
      case -2:
        return '用户取消支付';
      case -3:
        return '发送失败';
      case -4:
        return '授权失败';
      case -5:
        return '微信不支持';
      default:
        return `支付失败，错误码：${errCode}`;
    }
  }

  private getAlipayErrorMessage(resultStatus: string): string {
    switch (resultStatus) {
      case '4000':
        return '订单支付失败';
      case '5000':
        return '重复请求';
      case '6001':
        return '用户中途取消';
      case '6002':
        return '网络连接出错';
      case '6004':
        return '支付结果未知，请查询商户订单列表中订单的支付状态';
      default:
        return `支付失败，状态码：${resultStatus}`;
    }
  }
}

export default new PaymentService();