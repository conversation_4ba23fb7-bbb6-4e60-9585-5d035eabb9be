// 主题配置文件
export const theme = {
  colors: {
    primary: '#6366f1', // 现代紫色
    primaryDark: '#4f46e5',
    primaryLight: '#8b5cf6',
    secondary: '#06b6d4', // 青色
    success: '#10b981', // 绿色
    warning: '#f59e0b', // 橙色
    error: '#ef4444', // 红色
    
    // 背景色
    background: '#f8fafc',
    surface: '#ffffff',
    surfaceSecondary: '#f1f5f9',
    
    // 文字颜色
    textPrimary: '#1e293b',
    textSecondary: '#64748b',
    textTertiary: '#94a3b8',
    textInverse: '#ffffff',
    
    // 边框和分割线
    border: '#e2e8f0',
    borderLight: '#f1f5f9',
    
    // 渐变色
    gradientPrimary: ['#6366f1', '#8b5cf6'],
    gradientSecondary: ['#06b6d4', '#0891b2'],
    gradientSuccess: ['#10b981', '#059669'],
    gradientWarning: ['#f59e0b', '#d97706'],
    gradientBackground: ['#f8fafc', '#e2e8f0'],
  },
  
  spacing: {
    xs: 4,
    sm: 8,
    md: 16,
    lg: 24,
    xl: 32,
    xxl: 48,
  },
  
  borderRadius: {
    sm: 8,
    md: 12,
    lg: 16,
    xl: 24,
    full: 9999,
  },
  
  fontSize: {
    xs: 12,
    sm: 14,
    base: 16,
    lg: 18,
    xl: 20,
    '2xl': 24,
    '3xl': 28,
    '4xl': 32,
  },
  
  fontWeight: {
    normal: '400',
    medium: '500',
    semibold: '600',
    bold: '700',
  } as const,
  
  shadows: {
    sm: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.05,
      shadowRadius: 2,
      elevation: 2,
    },
    md: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.1,
      shadowRadius: 6,
      elevation: 4,
    },
    lg: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 8 },
      shadowOpacity: 0.15,
      shadowRadius: 12,
      elevation: 8,
    },
  },
};

export type Theme = typeof theme;
