import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { BASE_URL } from '../utils/config';
import { ConnectionMode } from '../services/WebSocketService';

interface PrivacySettings {
  dataCollection?: boolean;
  voiceRecording?: boolean;
  analyticsTracking?: boolean;
  personalizedAds?: boolean;
  shareWithThirdParty?: boolean;
}

interface UserInfo {
  id: number | null;
  phone: string;
  nickname: string;
  balance_count: number;
  balance_duration: number; // 剩余时长（秒）- 保持兼容性
  paid_total_duration: number; // 付费总时长（秒）
  paid_used_duration: number; // 已使用付费时长（秒）
  paid_remaining_duration: number; // 剩余付费时长（秒）
  trial_total_duration: number; // 试用总时长（秒）
  trial_used_duration: number; // 已使用试用时长（秒）
  trial_remaining_duration: number; // 剩余试用时长（秒）
  total_remaining_duration: number; // 总剩余时长（秒）
  ab_test_group: string;
  privacySettings?: PrivacySettings;
}

interface GeminiConfig {
  connection_mode: ConnectionMode;
  model: string;
  websocket_host: string;
  api_version: string;
}

interface UserState {
  token: string;
  userInfo: UserInfo;
  isLoggedIn: boolean;
  isGettingToken: boolean;
  isInitialized: boolean;
  ephemeralToken: string;
  geminiConfig: GeminiConfig | null;
  selectedMode: string; // 用户选择的面试职业模式
}

const initialState: UserState = {
  token: '',
  userInfo: {
    id: null,
    phone: '',
    nickname: '',
    balance_count: 0,
    balance_duration: 0, // 剩余时长（秒）- 保持兼容性
    paid_total_duration: 0, // 付费总时长
    paid_used_duration: 0, // 已使用付费时长
    paid_remaining_duration: 0, // 剩余付费时长
    trial_total_duration: 0, // 试用总时长(秒) - 将在登录后从后端获取
    trial_used_duration: 0, // 已使用试用时长
    trial_remaining_duration: 0, // 剩余试用时长 - 将在登录后从后端获取
    total_remaining_duration: 0, // 总剩余时长 - 将在登录后从后端获取
    ab_test_group: '',
    privacySettings: {
      dataCollection: true,
      voiceRecording: true,
      analyticsTracking: false,
      personalizedAds: false,
      shareWithThirdParty: false,
    }
  },
  isLoggedIn: false,
  isGettingToken: false,
  isInitialized: false,
  ephemeralToken: '',
  geminiConfig: null,
  selectedMode: 'technical_interview' // 默认选择技术面试
};

// 异步操作
export const login = createAsyncThunk(
  'user/login',
  async ({ phone, code }: { phone: string; code: string }) => {
    const response = await fetch(`${BASE_URL}/api/v1/auth/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ phone, code })
    });
    const data = await response.json();
    
    if (data.code === 200) {
      await AsyncStorage.setItem('token', data.data.token);
      await AsyncStorage.setItem('userInfo', JSON.stringify(data.data.user));
      return data.data;
    }
    throw new Error(data.message);
  }
);

export const sendSmsCode = createAsyncThunk(
  'user/sendSmsCode',
  async (phone: string) => {
    console.log('发送短信验证码到:', phone);
    console.log('请求URL:', `${BASE_URL}/api/v1/auth/sms`);
    
    try {
      const response = await fetch(`${BASE_URL}/api/v1/auth/sms`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ phone, type: 2 })
      });
      
      console.log('响应状态:', response.status);
      const data = await response.json();
      console.log('响应数据:', data);
      
      if (data.code === 200) {
        return data;
      }
      throw new Error(data.message);
    } catch (error) {
      console.error('网络请求错误:', error);
      throw error;
    }
  }
);

// 请求去重机制
let pendingTokenRequest: Promise<any> | null = null;

export const fetchEphemeralToken = createAsyncThunk(
  'user/fetchEphemeralToken',
  async ({ token }: { token: string }, { rejectWithValue }) => {
    console.log('fetchEphemeralToken: 开始获取临时令牌');
    
    // 如果已有正在进行的请求，返回该请求
    if (pendingTokenRequest) {
      console.log('fetchEphemeralToken: 检测到重复请求，返回现有请求');
      try {
        return await pendingTokenRequest;
      } catch (error) {
        // 如果现有请求失败，清除并继续新请求
        pendingTokenRequest = null;
        throw error;
      }
    }
    
    try {
      // 创建新的请求并缓存
      pendingTokenRequest = fetch(`${BASE_URL}/api/v1/gemini/ephemeral-token`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          expire_minutes: 30,
          uses: 1
        })
      });
      
      const response = await pendingTokenRequest;
      console.log('fetchEphemeralToken: 响应状态', response.status);
      const data = await response.json();
      console.log('fetchEphemeralToken: 响应数据', data);
      
      if (data.code === 200) {
        const tokenData = data.data;
        console.log('fetchEphemeralToken: 获取到令牌', {
          tokenLength: tokenData.token?.length || 0,
          remainingMinutes: tokenData.remaining_minutes || 0
        });
        
        const result = { 
          token: tokenData.token, 
          remaining_seconds: tokenData.remaining_seconds,
          remaining_minutes: tokenData.remaining_minutes,
          gemini_config: tokenData.gemini_config // 添加Gemini配置
        };
        
        // 请求成功，清除缓存
        pendingTokenRequest = null;
        return result;
      } else if (data.code === 403) {
        // 时长不足的情况
        pendingTokenRequest = null;
        return rejectWithValue({ 
          message: data.message || '时长不足',
          code: 'INSUFFICIENT_TIME'
        });
      }
      pendingTokenRequest = null;
      throw new Error(data.message);
    } catch (error) {
      console.error('fetchEphemeralToken: 请求失败', error);
      pendingTokenRequest = null; // 请求失败，清除缓存
      return rejectWithValue({ 
        message: error instanceof Error ? error.message : '网络错误',
        code: 'NETWORK_ERROR'
      });
    }
  }
);

// 刷新用户信息
export const refreshUserInfo = createAsyncThunk(
  'user/refreshUserInfo',
  async (token: string) => {
    const response = await fetch(`${BASE_URL}/api/v1/user/info`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    const data = await response.json();
    if (data.code === 200) {
      console.log('刷新用户信息成功:', data.data);
      return data.data;
    }
    throw new Error(data.message);
  }
);

const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    setToken: (state, action: PayloadAction<string>) => {
      state.token = action.payload;
      state.isLoggedIn = !!action.payload;
    },
    setUserInfo: (state, action: PayloadAction<Partial<UserInfo>>) => {
      const newUserInfo = { ...state.userInfo, ...action.payload };
      
      // 确保正确计算总剩余时长
      // 如果后端已经计算了total_remaining_duration，直接使用；否则前端计算
      if (newUserInfo.total_remaining_duration === undefined) {
        const trialRemaining = newUserInfo.trial_remaining_duration || 0;
        const paidRemaining = newUserInfo.paid_remaining_duration || newUserInfo.balance_duration || 0;
        newUserInfo.total_remaining_duration = trialRemaining + paidRemaining;
      }
      
      state.userInfo = newUserInfo;
    },
    logout: (state) => {
      state.token = '';
      state.userInfo = initialState.userInfo;
      state.isLoggedIn = false;
      state.ephemeralToken = '';
      AsyncStorage.multiRemove(['token', 'userInfo']);
    },
    setInitialized: (state, action: PayloadAction<boolean>) => {
      state.isInitialized = action.payload;
    },
    setSelectedMode: (state, action: PayloadAction<string>) => {
      state.selectedMode = action.payload;
    }
  },
  extraReducers: (builder) => {
    builder
      .addCase(login.fulfilled, (state, action) => {
        state.token = action.payload.token;
        const userInfo = action.payload.user;
        
        console.log('[DEBUG] login.fulfilled - 接收到的用户信息:', JSON.stringify(userInfo, null, 2));
        
        // 如果后端已经计算了total_remaining_duration，直接使用；否则前端计算
        if (userInfo.total_remaining_duration === undefined) {
          const trialRemaining = userInfo.trial_remaining_duration || 0;
          const paidRemaining = userInfo.paid_remaining_duration || userInfo.balance_duration || 0;
          userInfo.total_remaining_duration = trialRemaining + paidRemaining;
          console.log('[DEBUG] login.fulfilled - 前端计算总剩余时长:', userInfo.total_remaining_duration);
        } else {
          console.log('[DEBUG] login.fulfilled - 使用后端计算的总剩余时长:', userInfo.total_remaining_duration);
        }
        
        state.userInfo = userInfo;
        state.isLoggedIn = true;
      })
      .addCase(fetchEphemeralToken.pending, (state) => {
        state.isGettingToken = true;
      })
      .addCase(fetchEphemeralToken.fulfilled, (state, action) => {
        state.isGettingToken = false;
        state.ephemeralToken = action.payload.token;
        // 保存Gemini配置
        if (action.payload.gemini_config) {
          state.geminiConfig = action.payload.gemini_config;
          console.log('✅ 保存Gemini配置到Redux store:', action.payload.gemini_config);
        }
      })
      .addCase(fetchEphemeralToken.rejected, (state) => {
        state.isGettingToken = false;
      })
      .addCase(refreshUserInfo.fulfilled, (state, action) => {
        const userInfo = action.payload;
        
        // 如果后端已经计算了total_remaining_duration，直接使用；否则前端计算
        if (userInfo.total_remaining_duration === undefined) {
          const trialRemaining = userInfo.trial_remaining_duration || 0;
          const paidRemaining = userInfo.paid_remaining_duration || userInfo.balance_duration || 0;
          userInfo.total_remaining_duration = trialRemaining + paidRemaining;
        }
        
        state.userInfo = userInfo;
      });
  }
});

export const { setToken, setUserInfo, logout, setInitialized, setSelectedMode } = userSlice.actions;
export type { PrivacySettings };
export default userSlice.reducer;
