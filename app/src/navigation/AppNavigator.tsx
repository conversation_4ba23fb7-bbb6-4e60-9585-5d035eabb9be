import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { TouchableOpacity } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';

import HomeScreen from '../screens/HomeScreen';
import LoginScreen from '../screens/LoginScreen';
import InterviewScreen from '../screens/InterviewScreen';
import InterviewScoreScreen from '../screens/InterviewScoreScreen';
import MockInterviewScreen from '../screens/MockInterviewScreen';
import OrderScreen from '../screens/OrderScreen';
import ProfileScreen from '../screens/ProfileScreen';
import PaymentScreen from '../screens/PaymentScreen';
import VoiceSettingsScreen from '../screens/VoiceSettingsScreen';
import PrivacySettingsScreen from '../screens/PrivacySettingsScreen';
import AboutScreen from '../screens/AboutScreen';
import HelpFeedbackScreen from '../screens/HelpFeedbackScreen';
import SettingsModal from '../components/SettingsModal';

export type RootStackParamList = {
  Main: undefined;
  Login: undefined;
  Interview: { headerStyle?: object };
  InterviewScore: { scoreData: any };
  Payment: undefined;
  Order: undefined;
  VoiceSettings: undefined;
  PrivacySettings: undefined;
  About: undefined;
  HelpFeedback: undefined;
  Settings: undefined;
};

const Stack = createNativeStackNavigator<RootStackParamList>();
const Tab = createBottomTabNavigator();

const TabNavigator = () => {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName = '';
          
          if (route.name === 'Home') {
            iconName = 'home';
          } else if (route.name === 'MockInterview') {
            iconName = 'psychology';
          } else if (route.name === 'Order') {
            iconName = 'receipt';
          } else if (route.name === 'Profile') {
            iconName = 'person';
          }
          
          return <Icon name={iconName} size={focused ? size + 2 : size} color={color} />;
        },
        tabBarActiveTintColor: '#6366f1',
        tabBarInactiveTintColor: '#94a3b8',
        tabBarStyle: {
          backgroundColor: '#ffffff',
          borderTopWidth: 1,
          borderTopColor: '#e2e8f0',
          paddingBottom: 8,
          paddingTop: 8,
          height: 65,
          shadowColor: '#000',
          shadowOffset: { width: 0, height: -2 },
          shadowOpacity: 0.1,
          shadowRadius: 8,
          elevation: 8,
        },
        tabBarLabelStyle: {
          fontSize: 12,
          fontWeight: '600',
          marginTop: 4,
        },
        headerStyle: {
          backgroundColor: '#6366f1',
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.1,
          shadowRadius: 4,
          elevation: 4,
        },
        headerTintColor: '#fff',
        headerTitleStyle: {
          fontWeight: '700',
          fontSize: 18,
        },
        headerTitleAlign: 'center',
      })}
    >
      <Tab.Screen 
        name="Home" 
        component={HomeScreen} 
        options={{ 
          title: '首页',
          tabBarLabel: '首页'
        }}
      />
      <Tab.Screen 
        name="MockInterview" 
        component={MockInterviewScreen} 
        options={{ 
          title: 'AI模拟面试',
          tabBarLabel: '模拟面试'
        }}
      />
      <Tab.Screen 
        name="Order" 
        component={OrderScreen} 
        options={{ 
          title: '订单记录',
          tabBarLabel: '订单'
        }}
      />
      <Tab.Screen 
        name="Profile" 
        component={ProfileScreen} 
        options={({ navigation }) => ({ 
          title: '个人中心',
          tabBarLabel: '我的',
          headerRight: () => (
            <TouchableOpacity
              style={{
                marginRight: 16,
                width: 36,
                height: 36,
                borderRadius: 18,
                backgroundColor: 'rgba(255, 255, 255, 0.2)',
                justifyContent: 'center',
                alignItems: 'center',
              }}
              onPress={() => {
                // 直接导航到设置页面
                navigation.navigate('Settings');
              }}
              activeOpacity={0.7}
            >
              <Icon name="settings" size={20} color="#fff" />
            </TouchableOpacity>
          )
        })}
      />
    </Tab.Navigator>
  );
};

const AppNavigator = () => {
  return (
    <NavigationContainer>
      <Stack.Navigator
        screenOptions={{
          headerStyle: {
            backgroundColor: '#6366f1',
          },
          headerTintColor: '#fff',
          headerTitleStyle: {
            fontWeight: '700',
            fontSize: 18,
          },
          headerTitleAlign: 'center',
        }}
      >
        <Stack.Screen 
          name="Main" 
          component={TabNavigator} 
          options={{ headerShown: false }}
        />
        <Stack.Screen 
          name="Login" 
          component={LoginScreen} 
          options={{ title: '登录注册' }}
        />
        <Stack.Screen 
          name="Interview" 
          component={InterviewScreen} 
          options={({ route }) => ({
            title: '面试',
            headerStyle: {
              backgroundColor: '#1e3c72',
              ...((route.params as { headerStyle?: object })?.headerStyle || {}),
            },
          })}
        />
        <Stack.Screen 
          name="InterviewScore" 
          component={InterviewScoreScreen} 
          options={{
            title: '面试评分',
            headerStyle: {
              backgroundColor: '#10b981',
            },
          }}
        />
        <Stack.Screen 
          name="Payment" 
          component={PaymentScreen} 
          options={{ title: '购买套餐' }}
        />
        <Stack.Screen 
          name="Order" 
          component={OrderScreen} 
          options={{ title: '订单记录' }}
        />
        <Stack.Screen 
          name="VoiceSettings" 
          component={VoiceSettingsScreen} 
          options={{ title: '语音设置' }}
        />
        <Stack.Screen 
          name="PrivacySettings" 
          component={PrivacySettingsScreen} 
          options={{ title: '隐私设置' }}
        />
        <Stack.Screen 
          name="About" 
          component={AboutScreen} 
          options={{ title: '关于我们' }}
        />
        <Stack.Screen 
          name="HelpFeedback" 
          component={HelpFeedbackScreen} 
          options={{ title: '帮助与反馈' }}
        />
        <Stack.Screen 
          name="Settings" 
          component={({ navigation }) => {
            const [visible, setVisible] = React.useState(true);
            return (
              <SettingsModal
                visible={visible}
                onClose={() => {
                  setVisible(false);
                  navigation.goBack();
                }}
                navigation={navigation}
              />
            );
          }}
          options={{ 
            presentation: 'transparentModal',
            headerShown: false,
            animation: 'slide_from_bottom'
          }}
        />
      </Stack.Navigator>
    </NavigationContainer>
  );
};

export default AppNavigator;
