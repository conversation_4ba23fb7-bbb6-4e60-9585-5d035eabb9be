import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import HelpFeedbackService, { FAQ, HelpArticle, Feedback } from '../services/HelpFeedbackService';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RouteProp } from '@react-navigation/native';
import { RootStackParamList } from '../navigation/AppNavigator';

type HelpFeedbackScreenNavigationProp = NativeStackNavigationProp<
  RootStackParamList,
  'HelpFeedback'
>;

type Props = {
  navigation: HelpFeedbackScreenNavigationProp;
};

const HelpFeedbackScreen: React.FC<Props> = ({ navigation }) => {
  const [activeTab, setActiveTab] = useState<'help' | 'faq' | 'feedback'>('help');
  const [faqs, setFaqs] = useState<FAQ[]>([]);
  const [helpArticles, setHelpArticles] = useState<HelpArticle[]>([]);
  const [userFeedbacks, setUserFeedbacks] = useState<Feedback[]>([]);
  const [loading, setLoading] = useState(false);
  const [expandedFAQ, setExpandedFAQ] = useState<number | null>(null);

  useEffect(() => {
    loadInitialData();
  }, [loadInitialData]);

  const loadInitialData = useCallback(async () => {
    setLoading(true);
    try {
      await Promise.all([
        loadFAQs(),
        loadHelpArticles(),
        loadUserFeedbacks(),
      ]);
    } catch (error) {
      console.error('加载数据失败:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  const loadFAQs = async () => {
    try {
      const result = await HelpFeedbackService.getFAQs();
      setFaqs(result.faqs);
    } catch (error) {
      console.error('加载FAQ失败:', error);
      setFaqs([
        {
          id: 1,
          question: '如何开始使用面试助手？',
          answer: '1. 首先注册并登录账户\n2. 购买时长套餐或使用免费试用\n3. 进入面试页面，点击开始面试\n4. 戴上耳机，开始模拟面试',
          category: '使用指南',
          view_count: 156,
          created_at: '2024-01-15T10:00:00Z',
          updated_at: '2024-01-15T10:00:00Z',
        },
        {
          id: 2,
          question: '面试过程中遇到问题怎么办？',
          answer: '如果在面试过程中遇到技术问题：\n1. 检查网络连接\n2. 确保麦克风权限已开启\n3. 重启应用重新尝试\n4. 联系客服获取帮助',
          category: '故障排除',
          view_count: 89,
          created_at: '2024-01-16T10:00:00Z',
          updated_at: '2024-01-16T10:00:00Z',
        },
      ]);
    }
  };

  const loadHelpArticles = async () => {
    try {
      const result = await HelpFeedbackService.getHelpArticles();
      setHelpArticles(result.articles);
    } catch (error) {
      console.error('加载帮助文章失败:', error);
      setHelpArticles([
        {
          id: 1,
          title: '面试技巧指南',
          content: '本文将为您介绍面试中的常见技巧和注意事项...',
          category: '面试技巧',
          view_count: 234,
          sort_order: 1,
          status: 1,
          created_at: '2024-01-10T10:00:00Z',
          updated_at: '2024-01-10T10:00:00Z',
        },
        {
          id: 2,
          title: '技术面试准备',
          content: '技术面试需要准备的知识点和常见问题...',
          category: '技术面试',
          view_count: 189,
          sort_order: 2,
          status: 1,
          created_at: '2024-01-12T10:00:00Z',
          updated_at: '2024-01-12T10:00:00Z',
        },
      ]);
    }
  };

  const loadUserFeedbacks = async () => {
    try {
      const result = await HelpFeedbackService.getUserFeedbacks();
      setUserFeedbacks(result.feedbacks);
    } catch (error) {
      console.error('加载用户反馈失败:', error);
      setUserFeedbacks([]);
    }
  };

  const renderTabButton = (tab: 'help' | 'faq' | 'feedback', title: string) => (
    <TouchableOpacity
      style={[styles.tabButton, activeTab === tab && styles.activeTabButton]}
      onPress={() => setActiveTab(tab)}
    >
      <Text style={[styles.tabText, activeTab === tab && styles.activeTabText]}>
        {title}
      </Text>
    </TouchableOpacity>
  );

  const renderHelpContent = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>帮助文章</Text>
      {helpArticles.length > 0 ? (
        helpArticles.map((article) => (
          <View key={article.id} style={styles.itemCard}>
            <View style={styles.itemHeader}>
              <View style={styles.itemInfo}>
                <Text style={styles.itemTitle}>{article.title}</Text>
                <Text style={styles.itemMeta}>
                  {article.category} • {article.view_count} 次查看
                </Text>
              </View>
              <Icon name="chevron-right" size={24} color="#999" />
            </View>
          </View>
        ))
      ) : (
        <View style={styles.emptyContainer}>
          <Icon name="help-outline" size={40} color="#007AFF" />
          <Text style={styles.emptyText}>暂无帮助文章{'\n'}敬请期待更多内容</Text>
        </View>
      )}
    </View>
  );

  const renderFAQContent = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>常见问题</Text>
      {faqs.length > 0 ? (
        faqs.map((faq) => (
          <View key={faq.id} style={styles.itemCard}>
            <TouchableOpacity
              style={styles.itemHeader}
              onPress={() => setExpandedFAQ(expandedFAQ === faq.id ? null : faq.id)}
            >
              <View style={styles.itemInfo}>
                <Text style={styles.itemTitle}>{faq.question}</Text>
                <Text style={styles.itemMeta}>
                  {faq.category} • {faq.view_count} 次查看
                </Text>
              </View>
              <Icon 
                name={expandedFAQ === faq.id ? "expand-less" : "expand-more"} 
                size={24} 
                color="#999" 
              />
            </TouchableOpacity>
            {expandedFAQ === faq.id && (
              <View style={styles.expandedContent}>
                <Text style={styles.expandedText}>{faq.answer}</Text>
              </View>
            )}
          </View>
        ))
      ) : (
        <View style={styles.emptyContainer}>
          <Icon name="quiz" size={40} color="#007AFF" />
          <Text style={styles.emptyText}>暂无常见问题{'\n'}我们正在整理相关内容</Text>
        </View>
      )}
    </View>
  );

  const renderFeedbackContent = () => (
    <View style={styles.section}>
      <View style={styles.feedbackHeader}>
        <Text style={styles.sectionTitle}>我的反馈</Text>
        <TouchableOpacity
          style={styles.newFeedbackButton}
          onPress={() => {/* TODO: 实现反馈功能 */}}
        >
          <Text style={styles.newFeedbackButtonText}>新建反馈</Text>
        </TouchableOpacity>
      </View>
      
      {userFeedbacks.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Icon name="feedback" size={40} color="#007AFF" />
          <Text style={styles.emptyText}>暂无反馈记录{'\n'}点击上方按钮提交您的建议或问题</Text>
        </View>
      ) : (
        userFeedbacks.map((feedback) => (
          <View key={feedback.id} style={styles.itemCard}>
            <View style={styles.itemHeader}>
              <View style={styles.itemInfo}>
                <Text style={styles.itemTitle}>
                  {feedback.type === 'bug' ? '问题反馈' : feedback.type === 'suggestion' ? '建议' : '其他'}
                </Text>
                <Text style={styles.itemMeta}>
                  {new Date(feedback.created_at).toLocaleDateString()}
                </Text>
              </View>
              <View style={[styles.statusBadge, { 
                backgroundColor: feedback.status === 1 ? '#FF9500' : '#34C759' 
              }]}>
                <Text style={styles.statusText}>
                  {feedback.status === 1 ? '处理中' : '已处理'}
                </Text>
              </View>
            </View>
            <Text style={styles.feedbackContent}>{feedback.content}</Text>
            {feedback.admin_reply && (
              <View style={styles.replyContainer}>
                <Text style={styles.replyLabel}>客服回复：</Text>
                <Text style={styles.replyText}>{feedback.admin_reply}</Text>
              </View>
            )}
          </View>
        ))
      )}
    </View>
  );

  return (
    <ScrollView style={styles.container}>

      <View style={styles.tabContainer}>
        {renderTabButton('help', '帮助文章')}
        {renderTabButton('faq', '常见问题')}
        {renderTabButton('feedback', '我的反馈')}
      </View>

      {loading && activeTab !== 'feedback' ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#007AFF" />
          <Text style={styles.loadingText}>正在加载数据...</Text>
        </View>
      ) : (
        <>
          {activeTab === 'help' && renderHelpContent()}
          {activeTab === 'faq' && renderFAQContent()}
          {activeTab === 'feedback' && renderFeedbackContent()}
        </>
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  tabButton: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    borderRadius: 8,
    marginHorizontal: 4,
  },
  activeTabButton: {
    backgroundColor: '#007AFF',
  },
  tabText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#666',
  },
  activeTabText: {
    color: '#fff',
  },
  section: {
    marginTop: 20,
    paddingHorizontal: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 15,
  },
  itemCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 2,
    borderColor: 'transparent',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  itemHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  itemInfo: {
    flex: 1,
  },
  itemTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  itemMeta: {
    fontSize: 14,
    color: '#666',
  },
  expandedContent: {
    marginTop: 12,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  expandedText: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  emptyContainer: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginTop: 12,
    lineHeight: 20,
  },
  loadingContainer: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  loadingText: {
    fontSize: 14,
    color: '#666',
    marginTop: 12,
  },
  feedbackHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  newFeedbackButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
  },
  newFeedbackButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '500',
  },
  feedbackContent: {
    fontSize: 14,
    color: '#666',
    marginTop: 8,
    lineHeight: 20,
  },
  replyContainer: {
    marginTop: 12,
    padding: 12,
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
  },
  replyLabel: {
    fontSize: 12,
    fontWeight: '600',
    color: '#007AFF',
    marginBottom: 4,
  },
  replyText: {
    fontSize: 14,
    color: '#333',
    lineHeight: 20,
  },
});

export default HelpFeedbackScreen;