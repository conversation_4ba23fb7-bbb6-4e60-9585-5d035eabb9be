import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Switch,
  Alert,
  Dimensions,
  Platform,
} from 'react-native';
import { useSelector, useDispatch } from 'react-redux';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { RootState } from '../store';
import { setUserInfo, PrivacySettings } from '../store/userSlice';
import Card from '../components/Card';
import Button from '../components/Button';
import { theme } from '../styles/theme';

const PrivacySettingsScreen = ({ navigation }: any) => {
  const dispatch = useDispatch();
  const { userInfo } = useSelector((state: RootState) => state.user);
  
  const [privacySettings, setPrivacySettings] = useState<PrivacySettings>({
    dataCollection: userInfo.privacySettings?.dataCollection ?? true,
    voiceRecording: userInfo.privacySettings?.voiceRecording ?? true,
    analyticsTracking: userInfo.privacySettings?.analyticsTracking ?? false,
    personalizedAds: userInfo.privacySettings?.personalizedAds ?? false,
    shareWithThirdParty: userInfo.privacySettings?.shareWithThirdParty ?? false,
  });

  const [hasChanges, setHasChanges] = useState(false);

  useEffect(() => {
    // 检查是否有更改
    const originalSettings = userInfo.privacySettings || {};
    const hasChanged = Object.keys(privacySettings).some(
      key => privacySettings[key as keyof PrivacySettings] !== originalSettings[key as keyof PrivacySettings]
    );
    setHasChanges(hasChanged);
  }, [privacySettings, userInfo.privacySettings]);

  const handleSettingChange = (key: keyof PrivacySettings, value: boolean) => {
    // 对于关键设置，显示确认对话框
    if ((key === 'voiceRecording' || key === 'dataCollection') && !value) {
      Alert.alert(
        '重要提醒',
        key === 'voiceRecording' 
          ? '关闭语音录制将影响面试功能的正常使用，确定要关闭吗？'
          : '关闭数据收集可能会影响应用的个性化体验，确定要关闭吗？',
        [
          { text: '取消' },
          { 
            text: '确定', 
            onPress: () => {
              setPrivacySettings(prev => ({ ...prev, [key]: value }));
            }
          }
        ]
      );
    } else {
      setPrivacySettings(prev => ({ ...prev, [key]: value }));
    }
  };

  const saveSettings = async () => {
    try {
      // 这里应该调用API保存设置
      // await savePrivacySettings(privacySettings);
      
      // 更新本地状态
      dispatch(setUserInfo({
        ...userInfo,
        privacySettings: privacySettings
      }));

      Alert.alert('成功', '隐私设置已保存');
      setHasChanges(false);
    } catch (error) {
      Alert.alert('错误', '保存设置失败，请重试');
    }
  };

  const resetToDefaults = () => {
    Alert.alert(
      '重置设置',
      '确定要重置为默认隐私设置吗？',
      [
        { text: '取消' },
        { 
          text: '确定', 
          onPress: () => {
            setPrivacySettings({
              dataCollection: true,
              voiceRecording: true,
              analyticsTracking: false,
              personalizedAds: false,
              shareWithThirdParty: false,
            });
          }
        }
      ]
    );
  };


  const privacyOptions = [
    {
      section: '数据收集',
      items: [
        {
          key: 'dataCollection' as keyof PrivacySettings,
          title: '基础数据收集',
          description: '收集使用数据以改善应用体验',
          icon: 'data-usage',
          color: theme.colors.primary,
        },
        {
          key: 'voiceRecording' as keyof PrivacySettings,
          title: '语音录制',
          description: '允许录制语音用于面试练习',
          icon: 'mic',
          color: theme.colors.success,
        },
        {
          key: 'analyticsTracking' as keyof PrivacySettings,
          title: '分析追踪',
          description: '收集匿名使用统计数据',
          icon: 'analytics',
          color: theme.colors.info,
        },
      ]
    },
    {
      section: '广告与分享',
      items: [
        {
          key: 'personalizedAds' as keyof PrivacySettings,
          title: '个性化广告',
          description: '基于您的兴趣显示相关广告',
          icon: 'ads-click',
          color: theme.colors.warning,
        },
        {
          key: 'shareWithThirdParty' as keyof PrivacySettings,
          title: '第三方数据分享',
          description: '与合作伙伴分享匿名数据',
          icon: 'share',
          color: theme.colors.secondary,
        },
      ]
    }
  ];

  return (
    <View style={styles.container}>
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Privacy Settings - Single Card */}
          <Card style={styles.mainCard}>
            {/* Header */}
            <View style={styles.cardHeader}>
              <Icon name="privacy-tip" size={28} color={theme.colors.primary} />
              <Text style={styles.cardTitle}>隐私设置</Text>
            </View>
            <Text style={styles.cardDescription}>
              我们致力于保护您的隐私。您可以随时调整这些设置来控制我们如何收集和使用您的数据。
            </Text>

            {/* All Privacy Settings */}
            {privacyOptions.map((section, sectionIndex) => (
              <View key={sectionIndex} style={styles.section}>
                <Text style={styles.sectionTitle}>{section.section}</Text>
                {section.items.map((item, itemIndex) => (
                  <View
                    key={item.key}
                    style={[
                      styles.settingItem,
                      itemIndex < section.items.length - 1 && styles.settingItemBorder
                    ]}
                  >
                    <View style={[styles.iconContainer, { backgroundColor: `${item.color}15` }]}>
                      <Icon name={item.icon} size={24} color={item.color} />
                    </View>
                    <View style={styles.textContainer}>
                      <Text style={styles.settingTitle}>{item.title}</Text>
                      <Text style={styles.settingDescription}>{item.description}</Text>
                    </View>
                    <Switch
                      value={privacySettings[item.key]}
                      onValueChange={(value) => handleSettingChange(item.key, value)}
                      trackColor={{ false: theme.colors.borderLight, true: `${item.color}50` }}
                      thumbColor={privacySettings[item.key] ? item.color : theme.colors.textSecondary}
                      style={{
                        transform: isSmallScreen ? [{ scaleX: 0.9 }, { scaleY: 0.9 }] : undefined,
                      }}
                    />
                  </View>
                ))}
                {sectionIndex < privacyOptions.length - 1 && <View style={styles.sectionDivider} />}
              </View>
            ))}

            {/* Reset Button */}
            <View style={styles.actionSection}>
              <Button
                title="重置为默认设置"
                onPress={resetToDefaults}
                variant="secondary"
                size="md"
                style={styles.actionButton}
              />
            </View>
          </Card>

          {/* Save Button */}
          {hasChanges && (
            <View style={styles.saveSection}>
              <Button
                title="保存设置"
                onPress={saveSettings}
                variant="primary"
                size="lg"
                style={styles.saveButton}
              />
            </View>
          )}
        </ScrollView>
    </View>
  );
};

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');
const isSmallScreen = screenWidth < 375;
const isTablet = screenWidth > 768;
const isLandscape = screenWidth > screenHeight;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    flex: 1,
    paddingHorizontal: isSmallScreen ? theme.spacing.md : theme.spacing.lg,
    paddingBottom: Platform.OS === 'ios' ? theme.spacing.xl : theme.spacing.lg,
  },
  mainCard: {
    marginBottom: theme.spacing.lg,
    marginHorizontal: isTablet ? theme.spacing.xl : 0,
    marginTop: theme.spacing.lg,
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.md,
  },
  cardTitle: {
    fontSize: isSmallScreen ? 18 : 20,
    fontWeight: '600',
    color: '#333',
    marginLeft: theme.spacing.sm,
    flex: 1,
    flexWrap: 'wrap',
  },
  cardDescription: {
    fontSize: isSmallScreen ? 14 : 16,
    color: '#666',
    lineHeight: isSmallScreen ? 20 : 22,
    marginBottom: theme.spacing.lg,
  },
  section: {
    marginBottom: theme.spacing.md,
  },
  sectionTitle: {
    fontSize: isSmallScreen ? 16 : 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: theme.spacing.md,
    marginTop: theme.spacing.sm,
  },
  sectionDivider: {
    height: 1,
    backgroundColor: '#e0e0e0',
    marginVertical: theme.spacing.lg,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: isSmallScreen ? theme.spacing.sm : theme.spacing.md,
    paddingHorizontal: 0,
    minHeight: isSmallScreen ? 64 : 72,
  },
  settingItemBorder: {
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  iconContainer: {
    width: isSmallScreen ? 40 : 48,
    height: isSmallScreen ? 40 : 48,
    borderRadius: isSmallScreen ? 20 : 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: isSmallScreen ? theme.spacing.sm : theme.spacing.md,
  },
  textContainer: {
    flex: 1,
    marginRight: isSmallScreen ? theme.spacing.sm : theme.spacing.md,
    paddingRight: theme.spacing.xs,
  },
  settingTitle: {
    fontSize: isSmallScreen ? 14 : 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: theme.spacing.xs,
    lineHeight: isSmallScreen ? 18 : 20,
  },
  settingDescription: {
    fontSize: isSmallScreen ? 12 : 14,
    color: '#666',
    lineHeight: isSmallScreen ? 16 : 18,
    flexWrap: 'wrap',
  },
  actionSection: {
    marginTop: theme.spacing.lg,
    paddingTop: theme.spacing.lg,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  actionButton: {
    minHeight: isSmallScreen ? 44 : 48,
  },
  saveSection: {
    paddingVertical: isSmallScreen ? theme.spacing.md : theme.spacing.lg,
    paddingHorizontal: isSmallScreen ? theme.spacing.sm : theme.spacing.md,
    marginHorizontal: isTablet ? theme.spacing.xl : 0,
    backgroundColor: '#fff',
    borderRadius: 12,
    marginBottom: theme.spacing.lg,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  saveButton: {
    width: '100%',
    minHeight: isSmallScreen ? 48 : 52,
  },
});

export default PrivacySettingsScreen;