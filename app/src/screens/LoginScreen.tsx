import React, { useState } from 'react';
import { View, Text, TextInput, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { useDispatch } from 'react-redux';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { showToast } from '../utils/toast';
import { AppDispatch } from '../store';
import { login, sendSmsCode } from '../store/userSlice';
import GradientBackground from '../components/GradientBackground';
import Card from '../components/Card';
import Button from '../components/Button';
import { theme } from '../styles/theme';

const LoginScreen = ({ navigation }: any) => {
  const dispatch = useDispatch<AppDispatch>();
  const [phone, setPhone] = useState('');
  const [code, setCode] = useState('');
  const [countdown, setCountdown] = useState(0);

  const handleSendCode = async () => {
    if (!phone || phone.length !== 11) {
      showToast.error('请输入正确的手机号');
      return;
    }

    try {
      await dispatch(sendSmsCode(phone)).unwrap();
      showToast.success('验证码已发送', '请查收短信并输入验证码');
      
      setCountdown(60);
      const timer = setInterval(() => {
        setCountdown(prev => {
          if (prev <= 1) {
            clearInterval(timer);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    } catch (error: any) {
      showToast.error(error.message || '发送验证码失败');
    }
  };

  const handleLogin = async () => {
    if (!phone || !code) {
      showToast.error('请输入手机号和验证码');
      return;
    }

    try {
      await dispatch(login({ phone, code })).unwrap();
      showToast.success('登录成功', '欢迎回来！');
      navigation.goBack();
    } catch (error: any) {
      showToast.error(error.message || '登录失败');
    }
  };

  return (
    <View style={styles.background}>
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false} contentContainerStyle={styles.scrollContent}>
        <View style={styles.content}>
          {/* Header Section */}
          <View style={styles.header}>
            <Icon name="person-pin" size={48} color={theme.colors.primary} />
            <Text style={styles.title}>欢迎回来</Text>
            <Text style={styles.subtitle}>登录以继续您的AI面试之旅</Text>
          </View>

          {/* Login Form Card */}
          <Card style={styles.formCard} shadow="lg">
            <View style={styles.inputContainer}>
              <View style={styles.inputWrapper}>
                <Icon name="phone" size={20} color={theme.colors.textSecondary} style={styles.inputIcon} />
                <TextInput
                  style={styles.input}
                  placeholder="请输入手机号"
                  placeholderTextColor={theme.colors.textTertiary}
                  value={phone}
                  onChangeText={setPhone}
                  keyboardType="numeric"
                  maxLength={11}
                />
              </View>
              
              <View style={styles.codeContainer}>
                <View style={[styles.inputWrapper, styles.codeInputWrapper]}>
                  <Icon name="security" size={20} color={theme.colors.textSecondary} style={styles.inputIcon} />
                  <TextInput
                    style={[styles.input, styles.codeInput]}
                    placeholder="请输入验证码"
                    placeholderTextColor={theme.colors.textTertiary}
                    value={code}
                    onChangeText={setCode}
                    keyboardType="numeric"
                    maxLength={6}
                  />
                </View>
                <Button
                  title={countdown > 0 ? `${countdown}s` : '获取验证码'}
                  onPress={handleSendCode}
                  variant="primary"
                  size="sm"
                  disabled={countdown > 0}
                  style={styles.codeButton}
                  textStyle={{ ...styles.codeButtonText, color: '#ffffff' }}
                />
              </View>
              
              <TouchableOpacity
                style={styles.loginButton}
                onPress={handleLogin}
                activeOpacity={0.8}
              >
                <Text style={styles.loginButtonText}>登录 / 注册</Text>
              </TouchableOpacity>
            </View>

            {/* Additional Info */}
            <View style={styles.infoSection}>
              <Text style={styles.infoText}>
                登录即表示您同意我们的服务条款和隐私政策
              </Text>
            </View>
          </Card>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  background: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  container: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    justifyContent: 'center',
    paddingHorizontal: theme.spacing.md,
  },
  content: {
    paddingVertical: theme.spacing.xl,
  },
  header: {
    alignItems: 'center',
    marginBottom: theme.spacing.xl,
  },
  title: {
    fontSize: theme.fontSize['3xl'],
    fontWeight: '600',
    textAlign: 'center',
    marginTop: theme.spacing.md,
    marginBottom: theme.spacing.sm,
    color: theme.colors.textPrimary,
  },
  subtitle: {
    fontSize: theme.fontSize.base,
    textAlign: 'center',
    color: theme.colors.textSecondary,
    maxWidth: '80%',
  },
  formCard: {
    marginBottom: theme.spacing.lg,
  },
  inputContainer: {
    gap: theme.spacing.lg,
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.surfaceSecondary,
    borderRadius: theme.borderRadius.lg,
    paddingHorizontal: theme.spacing.md,
    borderWidth: 1,
    borderColor: theme.colors.border,
    height: 50,
  },
  inputIcon: {
    marginRight: theme.spacing.sm,
  },
  input: {
    flex: 1,
    fontSize: theme.fontSize.base,
    color: theme.colors.textPrimary,
  },
  codeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: theme.spacing.sm,
  },
  codeInputWrapper: {
    flex: 1,
  },
  codeInput: {
    flex: 1,
  },
  codeButton: {
    height: 50,
    justifyContent: 'center',
    minWidth: 110,
    backgroundColor: theme.colors.primary,
  },
  codeButtonText: {
    fontSize: theme.fontSize.sm,
    fontWeight: '500',
  },
  loginButton: {
    marginTop: theme.spacing.sm,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.primary,
    borderRadius: theme.borderRadius.lg,
  },
  loginButtonText: {
    fontSize: theme.fontSize.base,
    fontWeight: '600',
    color: '#ffffff',
  },
  infoSection: {
    marginTop: theme.spacing.lg,
    paddingTop: theme.spacing.md,
    borderTopWidth: 1,
    borderTopColor: theme.colors.borderLight,
  },
  infoText: {
    fontSize: theme.fontSize.xs,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    lineHeight: 18,
  },
});

export default LoginScreen;
