import React from 'react';
import { View, Text, StyleSheet, Al<PERSON>, ScrollView } from 'react-native';
import { useSelector, useDispatch } from 'react-redux';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { RootState } from '../store';
import { logout } from '../store/userSlice';
import Card from '../components/Card';
import Button from '../components/Button';
import GradientBackground from '../components/GradientBackground';
import { theme } from '../styles/theme';

const ProfileScreen = ({ navigation }: any) => {
  const dispatch = useDispatch();
  const { isLoggedIn, userInfo } = useSelector((state: RootState) => state.user);

  const handleLogout = () => {
    Alert.alert(
      '退出登录',
      '确定要退出登录吗？',
      [
        { text: '取消' },
        { 
          text: '确定', 
          onPress: () => dispatch(logout())
        }
      ]
    );
  };

  if (!isLoggedIn) {
    return (
      <GradientBackground colors={theme.colors.gradientPrimary}>
        <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
          <View style={styles.notLoggedInContainer}>
            <View style={styles.iconContainer}>
              <Icon name="person" size={80} color={theme.colors.textInverse} />
            </View>
            <Text style={styles.notLoggedInTitle}>未登录</Text>
            <Text style={styles.notLoggedInSubtitle}>请先登录以查看个人信息</Text>
            <Button
              title="登录/注册"
              onPress={() => navigation.navigate('Login')}
              variant="secondary"
              size="lg"
              style={styles.loginButton}
            />
          </View>
        </ScrollView>
      </GradientBackground>
    );
  }

  return (
    <GradientBackground colors={theme.colors.gradientPrimary}>
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        {/* User Profile Header */}
        <View style={styles.profileHeader}>
          <View style={styles.avatarContainer}>
            <Icon name="person" size={80} color={theme.colors.textInverse} />
          </View>
          <Text style={styles.userName}>{userInfo.phone}</Text>
          <Text style={styles.userNickname}>{userInfo.nickname || '未设置昵称'}</Text>
        </View>

        {/* Account Balance Card */}
        <Card style={styles.balanceCard} shadow="lg">
          <View style={styles.cardHeader}>
            <Icon name="account-balance" size={28} color={theme.colors.warning} />
            <Text style={styles.cardTitle}>账户余额</Text>
          </View>
          
          <View style={styles.balanceGrid}>
            <View style={styles.balanceItem}>
              <Text style={styles.balanceValue}>
                {Math.floor((userInfo.total_remaining_duration || 0) / 60)}
              </Text>
              <Text style={styles.balanceLabel}>总剩余(分钟)</Text>
            </View>
            <View style={styles.balanceItem}>
              <Text style={styles.balanceValue}>
                {Math.floor((userInfo.trial_remaining_duration || 0) / 60)}
              </Text>
              <Text style={styles.balanceLabel}>试用剩余(分钟)</Text>
            </View>
          </View>

          <View style={styles.usageInfo}>
            <View style={styles.usageRow}>
              <Text style={styles.usageLabel}>试用时长:</Text>
              <Text style={styles.usageText}>
                {Math.floor((userInfo.trial_used_duration || 0) / 60)} / {Math.floor((userInfo.trial_total_duration || 0) / 60)} 分钟
              </Text>
            </View>
            <View style={styles.usageRow}>
              <Text style={styles.usageLabel}>付费时长:</Text>
              <Text style={styles.usageText}>
                {Math.floor((userInfo.paid_used_duration || 0) / 60)} / {Math.floor((userInfo.paid_total_duration || 0) / 60)} 分钟
              </Text>
            </View>
          </View>
        </Card>

        {/* Quick Actions */}
        <View style={styles.actionsSection}>
          <Text style={styles.sectionTitle}>快捷操作</Text>
          <View style={styles.actionsGrid}>
            <Card style={styles.actionCard}>
              <Icon name="shopping-cart" size={32} color={theme.colors.success} />
              <Text style={styles.actionTitle}>购买套餐</Text>
              <Button
                title="立即购买"
                onPress={() => navigation.navigate('Payment')}
                variant="success"
                size="sm"
                style={styles.actionButton}
              />
            </Card>
            
            <Card style={styles.actionCard}>
              <Icon name="receipt" size={32} color={theme.colors.secondary} />
              <Text style={styles.actionTitle}>订单记录</Text>
              <Button
                title="查看订单"
                onPress={() => navigation.navigate('Order')}
                variant="secondary"
                size="sm"
                style={styles.actionButton}
              />
            </Card>
          </View>
        </View>

      </ScrollView>
    </GradientBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: theme.spacing.md,
  },
  notLoggedInContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: theme.spacing.lg,
    paddingTop: theme.spacing.xxl,
    paddingBottom: theme.spacing.xl,
    minHeight: 400,
  },
  iconContainer: {
    marginBottom: theme.spacing.lg,
  },
  notLoggedInTitle: {
    fontSize: theme.fontSize['2xl'],
    fontWeight: theme.fontWeight.bold as 'bold',
    color: theme.colors.textInverse,
    marginBottom: theme.spacing.sm,
  },
  notLoggedInSubtitle: {
    fontSize: theme.fontSize.base,
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
    marginBottom: theme.spacing.xl,
  },
  profileHeader: {
    alignItems: 'center',
    paddingTop: theme.spacing.xl,
    paddingBottom: theme.spacing.xl,
  },
  avatarContainer: {
    marginBottom: theme.spacing.md,
  },
  userName: {
    fontSize: theme.fontSize.xl,
    fontWeight: theme.fontWeight.bold as 'bold',
    color: theme.colors.textInverse,
    marginBottom: theme.spacing.xs,
  },
  userNickname: {
    fontSize: theme.fontSize.base,
    color: 'rgba(255, 255, 255, 0.8)',
  },
  balanceCard: {
    marginBottom: theme.spacing.lg,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.lg,
  },
  cardTitle: {
    fontSize: theme.fontSize.lg,
    fontWeight: theme.fontWeight.bold as 'bold',
    color: theme.colors.textPrimary,
    marginLeft: theme.spacing.sm,
  },
  balanceGrid: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: theme.spacing.lg,
  },
  balanceItem: {
    alignItems: 'center',
  },
  balanceValue: {
    fontSize: theme.fontSize['2xl'],
    fontWeight: theme.fontWeight.bold as 'bold',
    color: theme.colors.primary,
  },
  balanceLabel: {
    fontSize: theme.fontSize.sm,
    color: theme.colors.textSecondary,
    marginTop: theme.spacing.xs,
  },
  usageInfo: {
    paddingTop: theme.spacing.md,
    borderTopWidth: 1,
    borderTopColor: theme.colors.borderLight,
  },
  usageRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: theme.spacing.sm,
  },
  usageLabel: {
    fontSize: theme.fontSize.sm,
    color: theme.colors.textSecondary,
  },
  usageText: {
    fontSize: theme.fontSize.sm,
    color: theme.colors.textPrimary,
    fontWeight: theme.fontWeight.medium as '500',
  },
  actionsSection: {
    marginBottom: theme.spacing.lg,
  },
  sectionTitle: {
    fontSize: theme.fontSize.lg,
    fontWeight: theme.fontWeight.bold as 'bold',
    color: theme.colors.textInverse,
    textAlign: 'center',
    marginBottom: theme.spacing.md,
  },
  actionsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  actionCard: {
    width: '48%',
    alignItems: 'center',
    padding: theme.spacing.lg,
  },
  actionTitle: {
    fontSize: theme.fontSize.base,
    fontWeight: theme.fontWeight.semibold as '600',
    color: theme.colors.textPrimary,
    marginTop: theme.spacing.sm,
    marginBottom: theme.spacing.md,
  },
  actionButton: {
    width: '100%',
  },
  settingsCard: {
    marginBottom: theme.spacing.xl,
  },
  loginButton: {
    width: '100%',
  },
  logoutButton: {
    marginTop: theme.spacing.sm,
  },
  settingsButton: {
    marginBottom: theme.spacing.sm,
  },
});

export default ProfileScreen;
