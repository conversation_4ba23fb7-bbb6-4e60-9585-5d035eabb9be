import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, Alert, TouchableOpacity, Modal, FlatList } from 'react-native';
import { useSelector, useDispatch } from 'react-redux';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { RootState, AppDispatch } from '../store';
import { fetchEphemeralToken } from '../store/userSlice';
import GradientBackground from '../components/GradientBackground';
import Card from '../components/Card';
import Button from '../components/Button';
import { theme } from '../styles/theme';
import { BASE_URL } from '../utils/config';

// 职业选项
const professions = [
  { id: 'technical_interview', title: '技术岗', icon: 'computer', color: '#4A90E2' },
  { id: 'hr_interview', title: '人力资源', icon: 'people', color: '#50E3C2' },
  { id: 'sales_interview', title: '销售岗', icon: 'trending-up', color: '#F5A623' },
  { id: 'marketing_interview', title: '市场营销', icon: 'campaign', color: '#BD10E0' },
  { id: 'finance_interview', title: '金融岗', icon: 'account-balance', color: '#7ED321' },
  { id: 'legal_interview', title: '法律岗', icon: 'gavel', color: '#9B9B9B' },
  { id: 'medical_interview', title: '医疗岗', icon: 'local-hospital', color: '#D0021B' },
  { id: 'education_interview', title: '教育岗', icon: 'school', color: '#417505' },
  { id: 'architecture_interview', title: '建筑设计', icon: 'architecture', color: '#B8E986' },
  { id: 'psychology_interview', title: '心理咨询', icon: 'psychology', color: '#F8E71C' },
];

// 难度选项
const difficulties = [
  { 
    id: 'junior', 
    title: '初级', 
    description: '适合应届生和1-2年经验',
    icon: 'star-border',
    color: '#10b981'
  },
  { 
    id: 'intermediate', 
    title: '中级', 
    description: '适合3-5年工作经验',
    icon: 'star-half',
    color: '#f59e0b'
  },
  { 
    id: 'senior', 
    title: '高级', 
    description: '适合5年以上资深人员',
    icon: 'star',
    color: '#ef4444'
  }
];

const MockInterviewScreen = ({ navigation }: any) => {
  const dispatch = useDispatch<AppDispatch>();
  const { isLoggedIn, token } = useSelector((state: RootState) => state.user);
  
  const [selectedProfession, setSelectedProfession] = useState<string>('');
  const [selectedDifficulty, setSelectedDifficulty] = useState<string>('');
  const [isStarting, setIsStarting] = useState(false);
  const [showProfessionModal, setShowProfessionModal] = useState(false);
  const [showDifficultyModal, setShowDifficultyModal] = useState(false);

  // 检查登录状态
  const checkLoginStatus = () => {
    if (!isLoggedIn || !token) {
      Alert.alert(
        '请先登录',
        '使用模拟面试功能需要先登录账号',
        [
          { text: '取消', style: 'cancel' },
          { text: '去登录', onPress: () => navigation.navigate('Login') }
        ]
      );
      return false;
    }
    return true;
  };

  // 开始模拟面试
  const startMockInterview = async () => {
    if (!checkLoginStatus()) return;
    
    if (!selectedProfession || !selectedDifficulty) {
      Alert.alert('提示', '请选择面试职业和难度级别');
      return;
    }

    setIsStarting(true);
    
    try {
      console.log('开始模拟面试:', { profession: selectedProfession, difficulty: selectedDifficulty });
      
      // 1. 获取临时令牌 (参考首页的方式)
      const tokenResult = await dispatch(fetchEphemeralToken({ token })).unwrap();
      console.log('获取到的令牌结果:', tokenResult);
      if (!tokenResult?.token) {
        throw new Error('获取临时令牌失败');
      }

      // 2. 获取模拟面试提示词
      const promptResponse = await fetch(
        `${BASE_URL}/api/v1/prompts/mock-interview?profession=${selectedProfession}&difficulty=${selectedDifficulty}`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      if (!promptResponse.ok) {
        throw new Error('获取面试提示词失败');
      }

      const promptData = await promptResponse.json();
      if (promptData.code !== 200) {
        throw new Error(promptData.message || '获取面试提示词失败');
      }

      // 3. 跳转到面试页面
      navigation.navigate('Interview', {
        domain: selectedProfession,
        mode: 'mock_interview',
        customPrompt: promptData.data.prompt,
        difficulty: selectedDifficulty,
        isMockInterview: true,
        timeLimit: 45 * 60, // 45分钟
        headerStyle: {
          backgroundColor: '#2d5aa0', // 模拟面试使用不同的头部颜色
        }
      });

    } catch (error: any) {
      console.error('启动模拟面试失败:', error);
      Alert.alert(
        '启动失败',
        error.message || '启动模拟面试失败，请重试',
        [{ text: '确定' }]
      );
    } finally {
      setIsStarting(false);
    }
  };

  // 获取选中的职业信息
  const getSelectedProfession = () => {
    return professions.find(p => p.id === selectedProfession);
  };

  // 获取选中的难度信息
  const getSelectedDifficulty = () => {
    return difficulties.find(d => d.id === selectedDifficulty);
  };

  // 渲染下拉框选项
  const renderDropdownItem = ({ item, onSelect, type }: { item: any, onSelect: (id: string) => void, type: 'profession' | 'difficulty' }) => (
    <TouchableOpacity
      style={styles.dropdownItem}
      onPress={() => {
        onSelect(item.id);
        if (type === 'profession') {
          setShowProfessionModal(false);
        } else {
          setShowDifficultyModal(false);
        }
      }}
    >
      <View style={styles.dropdownItemContent}>
        <View style={[styles.dropdownIcon, { backgroundColor: item.color }]}>
          <Icon name={item.icon} size={20} color="#fff" />
        </View>
        <View style={styles.dropdownTextContainer}>
          <Text style={styles.dropdownTitle}>{item.title}</Text>
          {item.description && (
            <Text style={styles.dropdownDescription}>{item.description}</Text>
          )}
        </View>
      </View>
    </TouchableOpacity>
  );

  return (
    <GradientBackground colors={theme.colors.gradientPrimary}>
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        {/* 页面标题 */}
        <View style={styles.header}>
          <View style={styles.headerIconContainer}>
            <Icon name="psychology" size={48} color={theme.colors.textInverse} />
          </View>
          <Text style={styles.headerTitle}>AI模拟面试</Text>
          <Text style={styles.headerSubtitle}>AI扮演面试官角色，完整模拟真实面试场景</Text>
          
          {/* 功能对比提示 */}
          <View style={styles.comparisonTip}>
            <View style={styles.tipRow}>
              <Icon name="psychology" size={16} color={theme.colors.warning} />
              <Text style={styles.tipText}>模拟面试：AI当面试官，练习+评分</Text>
            </View>
            <View style={styles.tipRow}>
              <Icon name="support-agent" size={16} color={theme.colors.primary} />
              <Text style={styles.tipText}>实时助手：真实面试时的AI辅导</Text>
            </View>
          </View>
        </View>

        {/* 职业选择 */}
        <Card style={styles.card}>
          <View style={styles.sectionHeader}>
            <Icon name="work" size={20} color={theme.colors.primary} />
            <Text style={styles.sectionTitle}>选择面试职业</Text>
          </View>
          <TouchableOpacity
            style={styles.dropdown}
            onPress={() => setShowProfessionModal(true)}
          >
            <View style={styles.dropdownContent}>
              {selectedProfession ? (
                <View style={styles.selectedItem}>
                  <View style={[styles.selectedIcon, { backgroundColor: getSelectedProfession()?.color }]}>
                    <Icon name={getSelectedProfession()?.icon || 'work'} size={20} color="#fff" />
                  </View>
                  <Text style={styles.selectedText}>{getSelectedProfession()?.title}</Text>
                </View>
              ) : (
                <Text style={styles.placeholderText}>请选择面试职业</Text>
              )}
              <Icon name="keyboard-arrow-down" size={24} color={theme.colors.textSecondary} />
            </View>
          </TouchableOpacity>
        </Card>

        {/* 难度选择 */}
        <Card style={styles.card}>
          <View style={styles.sectionHeader}>
            <Icon name="trending-up" size={20} color={theme.colors.primary} />
            <Text style={styles.sectionTitle}>选择难度级别</Text>
          </View>
          <TouchableOpacity
            style={styles.dropdown}
            onPress={() => setShowDifficultyModal(true)}
          >
            <View style={styles.dropdownContent}>
              {selectedDifficulty ? (
                <View style={styles.selectedItem}>
                  <View style={[styles.selectedIcon, { backgroundColor: getSelectedDifficulty()?.color }]}>
                    <Icon name={getSelectedDifficulty()?.icon || 'star'} size={20} color="#fff" />
                  </View>
                  <View style={styles.selectedTextContainer}>
                    <Text style={styles.selectedText}>{getSelectedDifficulty()?.title}</Text>
                    <Text style={styles.selectedDescription}>{getSelectedDifficulty()?.description}</Text>
                  </View>
                </View>
              ) : (
                <Text style={styles.placeholderText}>请选择难度级别</Text>
              )}
              <Icon name="keyboard-arrow-down" size={24} color={theme.colors.textSecondary} />
            </View>
          </TouchableOpacity>
        </Card>

        {/* 模拟面试特色 */}
        <Card style={styles.card}>
          <View style={styles.sectionHeader}>
            <Icon name="star" size={20} color={theme.colors.warning} />
            <Text style={styles.sectionTitle}>模拟面试特色</Text>
          </View>
          <View style={styles.highlightContainer}>
            <View style={styles.highlightItem}>
              <View style={styles.highlightIcon}>
                <Icon name="person" size={20} color={theme.colors.warning} />
              </View>
              <View style={styles.highlightContent}>
                <Text style={styles.highlightTitle}>AI扮演面试官</Text>
                <Text style={styles.highlightDesc}>完全模拟真实面试场景，AI提问您回答</Text>
              </View>
            </View>
            <View style={styles.highlightItem}>
              <View style={styles.highlightIcon}>
                <Icon name="assessment" size={20} color={theme.colors.success} />
              </View>
              <View style={styles.highlightContent}>
                <Text style={styles.highlightTitle}>专业评分报告</Text>
                <Text style={styles.highlightDesc}>面试结束后获得详细的表现评估</Text>
              </View>
            </View>
            <View style={styles.highlightItem}>
              <View style={styles.highlightIcon}>
                <Icon name="trending-up" size={20} color={theme.colors.primary} />
              </View>
              <View style={styles.highlightContent}>
                <Text style={styles.highlightTitle}>技能提升</Text>
                <Text style={styles.highlightDesc}>通过反复练习提高面试表现</Text>
              </View>
            </View>
          </View>
        </Card>

        {/* 面试说明 */}
        <Card style={styles.card}>
          <View style={styles.sectionHeader}>
            <Icon name="info" size={20} color={theme.colors.primary} />
            <Text style={styles.sectionTitle}>面试说明</Text>
          </View>
          <View style={styles.infoContainer}>
            <View style={styles.infoItem}>
              <Icon name="schedule" size={16} color={theme.colors.textSecondary} />
              <Text style={styles.infoText}>面试时长：45分钟</Text>
            </View>
            <View style={styles.infoItem}>
              <Icon name="record-voice-over" size={16} color={theme.colors.textSecondary} />
              <Text style={styles.infoText}>支持语音对话</Text>
            </View>
            <View style={styles.infoItem}>
              <Icon name="assessment" size={16} color={theme.colors.textSecondary} />
              <Text style={styles.infoText}>AI将根据您的表现给出评价</Text>
            </View>
          </View>
        </Card>

        {/* 开始按钮 */}
        <View style={styles.startButtonContainer}>
          <Button
            title={isStarting ? "正在准备..." : "🎯 开始模拟面试"}
            onPress={startMockInterview}
            disabled={!selectedProfession || !selectedDifficulty || isStarting}
            style={[
              styles.startButton,
              (!selectedProfession || !selectedDifficulty || isStarting) && styles.disabledButton
            ]}
            textStyle={styles.startButtonText}
          />
        </View>
      </ScrollView>

      {/* 职业选择模态框 */}
      <Modal
        visible={showProfessionModal}
        transparent
        animationType="slide"
        onRequestClose={() => setShowProfessionModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>选择面试职业</Text>
              <TouchableOpacity
                onPress={() => setShowProfessionModal(false)}
                style={styles.closeButton}
              >
                <Icon name="close" size={24} color={theme.colors.textSecondary} />
              </TouchableOpacity>
            </View>
            <FlatList
              data={professions}
              keyExtractor={(item) => item.id}
              renderItem={({ item }) => renderDropdownItem({ 
                item, 
                onSelect: setSelectedProfession, 
                type: 'profession' 
              })}
              showsVerticalScrollIndicator={false}
            />
          </View>
        </View>
      </Modal>

      {/* 难度选择模态框 */}
      <Modal
        visible={showDifficultyModal}
        transparent
        animationType="slide"
        onRequestClose={() => setShowDifficultyModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>选择难度级别</Text>
              <TouchableOpacity
                onPress={() => setShowDifficultyModal(false)}
                style={styles.closeButton}
              >
                <Icon name="close" size={24} color={theme.colors.textSecondary} />
              </TouchableOpacity>
            </View>
            <FlatList
              data={difficulties}
              keyExtractor={(item) => item.id}
              renderItem={({ item }) => renderDropdownItem({ 
                item, 
                onSelect: setSelectedDifficulty, 
                type: 'difficulty' 
              })}
              showsVerticalScrollIndicator={false}
            />
          </View>
        </View>
      </Modal>
    </GradientBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: theme.spacing.md,
  },
  header: {
    alignItems: 'center',
    paddingTop: theme.spacing.xxl,
    paddingBottom: theme.spacing.xl,
  },
  headerIconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: theme.spacing.md,
  },
  headerTitle: {
    fontSize: theme.fontSize['3xl'],
    fontWeight: theme.fontWeight.bold,
    color: theme.colors.textInverse,
    marginTop: theme.spacing.sm,
    textAlign: 'center',
  },
  headerSubtitle: {
    fontSize: theme.fontSize.base,
    color: 'rgba(255, 255, 255, 0.8)',
    marginTop: theme.spacing.sm,
    textAlign: 'center',
    lineHeight: 24,
  },
  comparisonTip: {
    marginTop: theme.spacing.lg,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: theme.borderRadius.md,
    padding: theme.spacing.md,
    gap: theme.spacing.sm,
  },
  tipRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  tipText: {
    fontSize: theme.fontSize.sm,
    color: 'rgba(255, 255, 255, 0.9)',
    marginLeft: theme.spacing.sm,
    fontWeight: theme.fontWeight.medium,
  },
  highlightContainer: {
    gap: theme.spacing.lg,
  },
  highlightItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  highlightIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(99, 102, 241, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: theme.spacing.md,
  },
  highlightContent: {
    flex: 1,
  },
  highlightTitle: {
    fontSize: theme.fontSize.base,
    fontWeight: theme.fontWeight.semibold,
    color: theme.colors.textPrimary,
    marginBottom: theme.spacing.xs,
  },
  highlightDesc: {
    fontSize: theme.fontSize.sm,
    color: theme.colors.textSecondary,
    lineHeight: 20,
  },
  card: {
    marginBottom: theme.spacing.lg,
    padding: theme.spacing.lg,
    ...theme.shadows.md,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.lg,
  },
  sectionTitle: {
    fontSize: theme.fontSize.lg,
    fontWeight: theme.fontWeight.semibold,
    color: theme.colors.textPrimary,
    marginLeft: theme.spacing.sm,
  },
  // 下拉框样式
  dropdown: {
    backgroundColor: theme.colors.surface,
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: theme.borderRadius.md,
    padding: theme.spacing.md,
    minHeight: 56,
    ...theme.shadows.sm,
  },
  dropdownContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  selectedItem: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  selectedIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: theme.spacing.md,
  },
  selectedText: {
    fontSize: theme.fontSize.base,
    fontWeight: theme.fontWeight.medium,
    color: theme.colors.textPrimary,
  },
  selectedTextContainer: {
    flex: 1,
  },
  selectedDescription: {
    fontSize: theme.fontSize.sm,
    color: theme.colors.textSecondary,
    marginTop: theme.spacing.xs,
  },
  placeholderText: {
    fontSize: theme.fontSize.base,
    color: theme.colors.textTertiary,
    flex: 1,
  },
  // 模态框样式
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: theme.colors.surface,
    borderTopLeftRadius: theme.borderRadius.xl,
    borderTopRightRadius: theme.borderRadius.xl,
    maxHeight: '70%',
    paddingBottom: theme.spacing.xl,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: theme.spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  modalTitle: {
    fontSize: theme.fontSize.lg,
    fontWeight: theme.fontWeight.semibold,
    color: theme.colors.textPrimary,
  },
  closeButton: {
    padding: theme.spacing.sm,
  },
  dropdownItem: {
    paddingHorizontal: theme.spacing.lg,
    paddingVertical: theme.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.borderLight,
  },
  dropdownItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  dropdownIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: theme.spacing.md,
  },
  dropdownTextContainer: {
    flex: 1,
  },
  dropdownTitle: {
    fontSize: theme.fontSize.base,
    fontWeight: theme.fontWeight.medium,
    color: theme.colors.textPrimary,
  },
  dropdownDescription: {
    fontSize: theme.fontSize.sm,
    color: theme.colors.textSecondary,
    marginTop: theme.spacing.xs,
  },
  // 面试说明样式
  infoContainer: {
    gap: theme.spacing.md,
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: theme.spacing.xs,
  },
  infoText: {
    fontSize: theme.fontSize.base,
    color: theme.colors.textSecondary,
    marginLeft: theme.spacing.md,
    flex: 1,
  },
  // 开始按钮样式
  startButtonContainer: {
    paddingVertical: theme.spacing.xl,
    paddingBottom: theme.spacing.xxl,
  },
  startButton: {
    backgroundColor: theme.colors.warning,
    paddingVertical: theme.spacing.lg,
    borderRadius: theme.borderRadius.md,
    ...theme.shadows.md,
  },
  disabledButton: {
    backgroundColor: theme.colors.textTertiary,
    ...theme.shadows.sm,
  },
  startButtonText: {
    fontSize: theme.fontSize.lg,
    fontWeight: theme.fontWeight.semibold,
    color: theme.colors.textInverse,
    textAlign: 'center',
  },
});

export default MockInterviewScreen;