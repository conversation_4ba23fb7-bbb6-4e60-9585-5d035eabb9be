import React, { useEffect, useState, useCallback, useRef } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Alert, ActivityIndicator, AppState, ScrollView, Clipboard } from 'react-native';
import { useSelector, useDispatch } from 'react-redux';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { RootState, AppDispatch } from '../store';
import { refreshUserInfo } from '../store/userSlice';
import WebSocketService from '../services/WebSocketService';
import EnhancedAudioInputService from '../services/EnhancedAudioInputService';
import { SmartAudioServiceSelector } from '../services/SmartAudioServiceSelector';
import { BASE_URL, AUDIO_CONFIG } from '../utils/config';
import GradientBackground from '../components/GradientBackground';
import Card from '../components/Card';
import DebugLogController from '../components/DebugLogController';

import { theme } from '../styles/theme';
import { getPreferredVoice } from '../utils/voiceUtils';
import logger from '../utils/logger';


const InterviewScreen = ({ route, navigation }: any) => {
  const { 
    domain, 
    mode = 'technical_interview', 
    customPrompt, 
    difficulty, 
    isMockInterview = false, 
    timeLimit 
  } = route.params;
  const log = useCallback((type: 'info' | 'error', message: string, ...optionalParams: any[]) => {
    const timestamp = new Date().toLocaleTimeString();
    const logMessage = `[${timestamp}] [${type.toUpperCase()}] ${message}`;
    if (type === 'error') {
      console.error(logMessage, ...optionalParams);
    } else {
      console.log(logMessage, ...optionalParams);
    }
  }, []);
  const dispatch = useDispatch<AppDispatch>();
  const { ephemeralToken, token, userInfo } = useSelector((state: RootState) => state.user);
  const isCleaning = useRef(false);
  const connectionAttempted = useRef(false); // 防止重复连接
  
  // 面试会话相关状态
  const [sessionId, setSessionId] = useState<string>('');
  const [interviewStartTime, setInterviewStartTime] = useState<Date | null>(null);
  const [currentDuration, setCurrentDuration] = useState<number>(0); // 当前面试时长（秒）
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  
  // Enhanced state management for WebSocket implementation
  const [wsConnected, setWsConnected] = useState(false);
  const [wsRecording, setWsRecording] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [transcriptHistory, setTranscriptHistory] = useState<Array<{
    id: string;
    role: 'user' | 'assistant';
    content: string;
    timestamp: Date;
    isFinal: boolean;
    source: 'input' | 'output' | 'text'; // 区分转录来源
  }>>([]);
  const [showTranscript, setShowTranscript] = useState(false);
  const [showTips, setShowTips] = useState(false);
  const [showDebugController, setShowDebugController] = useState(false);

  const [interviewStarted, setInterviewStarted] = useState(false); // 新增状态，控制面试是否真正开始
  const transcriptScrollViewRef = useRef<ScrollView>(null);

  
  // 动态音频服务
  const [audioOutputService, setAudioOutputService] = useState<any>(null);
  const [audioServiceReady, setAudioServiceReady] = useState(false);
  const [currentPlaybackMethod, setCurrentPlaybackMethod] = useState<any>(null);
  
  // 初始化音频服务
  useEffect(() => {
    const initializeAudioService = async () => {
      try {
        logger.info('InterviewScreen', '初始化音频服务...');
        
        // 使用智能音频服务选择器
        const smartSelector = SmartAudioServiceSelector.getInstance();
        const bestService = await smartSelector.selectBestAudioService();
        
        setAudioOutputService(bestService.service);
        setAudioServiceReady(true);
        setCurrentPlaybackMethod(bestService);

        // 打印服务选择报告
        const selectionReport = smartSelector.generateSelectionReport();
        logger.info('InterviewScreen', '音频服务选择报告:\n' + selectionReport);


        
        
      } catch (error) {
        log('error', 'InterviewScreen: 音频服务初始化失败:', error);
        // 设置为禁用状态
        log('info', 'InterviewScreen: 音频播放功能已禁用');
        setAudioOutputService(null);
        setAudioServiceReady(true);
        setCurrentPlaybackMethod({
          type: 'disabled',
          name: '音频播放已禁用',
          description: '音频服务初始化失败',
          supported: false
        });
      }
    };

    initializeAudioService();
  }, []);

  // 使用传入的模式连接WebSocket
  const connectWebSocket = useCallback(async () => {
    if (!ephemeralToken) {
      log('error', 'InterviewScreen: 没有临时令牌，无法连接');
      return;
    }

    try {
      log('info', `InterviewScreen: 开始连接WebSocket，使用模式: ${mode}`);
      await WebSocketService.connect(ephemeralToken, mode, customPrompt);
    } catch (error) {
      log('error', 'InterviewScreen: WebSocket连接失败:', error);
    }
  }, [ephemeralToken, mode, log]);

  // WebSocket callbacks - 直接传递给原生层
  const handleWebSocketMessage = useCallback((message: any) => {
    if (message.type === 'audio' && message.data) {
      // 简化检查：只要面试开始且服务就绪就直接播放
      if (!interviewStarted) {
        logger.info('InterviewScreen', '面试未开始，忽略音频数据');
        return;
      }

      if (!audioOutputService || !audioServiceReady) {
        logger.warn('InterviewScreen', '音频服务未就绪，丢弃音频数据（原生层会处理缓冲）');
        return;
      }

      // 直接传递给原生层，无需JavaScript层缓冲
      const estimatedSize = Math.floor(message.data.length * 3 / 4);
      logger.info('InterviewScreen', `🚀 直接传递base64音频数据到原生层，长度: ${message.data.length}, 估算大小: ${estimatedSize}字节`);

      try {
        audioOutputService.playChunk(message.data);
        logger.info('InterviewScreen', '音频数据已直接发送到原生层');
      } catch (error) {
        logger.error('InterviewScreen', '传递音频数据到原生层时出错:', error);
      }
    }
  }, [audioOutputService, audioServiceReady, interviewStarted, log]);

  // 停止计时器
  const stopTimer = useCallback(() => {
    if (timerRef.current) {
      log('info', 'InterviewScreen: 停止计时器');
      clearInterval(timerRef.current);
      timerRef.current = null;
    }
  }, [log]);

  const handleStatusUpdate = useCallback((status: string) => {
    log('info', 'InterviewScreen: WebSocket status update:', status);
    setIsLoading(false);
    
    switch (status) {
      case 'connected':
        setWsConnected(true);
        break;
      case 'disconnected':
        setWsConnected(false);
        setWsRecording(false);
        stopTimer(); // 停止计时
        break;
      case 'error':
        setWsConnected(false);
        setWsRecording(false);
        stopTimer(); // 停止计时
        break;
      default:
        // 忽略其他状态
        break;
    }
  }, [log, stopTimer]);

  const handleError = useCallback((errorMsg: string) => {
    log('error', 'InterviewScreen: WebSocket error:', errorMsg); // 确保错误日志被记录
    
    // 停止计时器
    stopTimer();
    
    // 将技术性错误转换为用户友好的提示
    let userFriendlyMessage = '网络连接失败';
    
    if (errorMsg) {
      const lowerErrorMsg = errorMsg.toLowerCase();
      if (lowerErrorMsg.includes('network') || lowerErrorMsg.includes('connection') || 
          lowerErrorMsg.includes('timeout') || lowerErrorMsg.includes('failed to connect')) {
        userFriendlyMessage = '网络连接失败，请检查网络设置';
      } else if (lowerErrorMsg.includes('websocket')) {
        userFriendlyMessage = '连接失败，请稍后重试';
      } else if (lowerErrorMsg.includes('unauthorized') || lowerErrorMsg.includes('token')) {
        userFriendlyMessage = '身份验证失败，请重新登录';
      } else if (lowerErrorMsg.includes('server') || lowerErrorMsg.includes('service')) {
        userFriendlyMessage = '服务暂时不可用，请稍后重试';
      } else {
        userFriendlyMessage = '连接失败，请检查网络后重试';
      }
    }
    
    setWsConnected(false);
    setWsRecording(false);
    setIsLoading(false);
    Alert.alert('连接失败', userFriendlyMessage);
  }, [log, stopTimer]); // 添加log和stopTimer到依赖数组

  const handleTranscript = useCallback(({ text, isFinal, type }: { text: string; isFinal: boolean; type: string }) => {
    log('info', 'InterviewScreen: Received transcript:', text, 'isFinal:', isFinal, 'type:', type);
    
    if (text && text.trim()) {
      setTranscriptHistory(prev => {
        const now = new Date();
        const newEntry = {
          id: `${type}_${now.getTime()}_${Math.random().toString(36).substr(2, 9)}`,
          role: type === 'user' ? 'user' : 'assistant' as 'user' | 'assistant',
          content: text,
          timestamp: now,
          isFinal,
          source: type === 'user' ? 'input' : (type === 'assistant' ? 'output' : 'text') as 'input' | 'output' | 'text'
        };
        
        // 针对AI语音转录的特殊处理逻辑
        if (newEntry.role === 'assistant' && newEntry.source === 'output') {
          // 对于AI的语音输出转录，查找最近的同源非最终条目进行更新
          const lastAssistantOutputIndex = prev.findLastIndex(entry => 
            entry.role === 'assistant' && 
            entry.source === 'output' && 
            !entry.isFinal &&
            (now.getTime() - entry.timestamp.getTime()) <= 10000 // 10秒内
          );
          
          if (lastAssistantOutputIndex !== -1) {
            log('info', 'InterviewScreen: 更新AI语音转录内容');
            const updatedHistory = [...prev];
            updatedHistory[lastAssistantOutputIndex] = {
              ...updatedHistory[lastAssistantOutputIndex],
              content: text,
              timestamp: now,
              isFinal
            };
            return updatedHistory;
          }
        }
        
        // 对于用户输入转录的处理
        if (newEntry.role === 'user' && newEntry.source === 'input') {
          const lastUserInputIndex = prev.findLastIndex(entry => 
            entry.role === 'user' && 
            entry.source === 'input' && 
            !entry.isFinal &&
            (now.getTime() - entry.timestamp.getTime()) <= 5000 // 5秒内
          );
          
          if (lastUserInputIndex !== -1) {
            log('info', 'InterviewScreen: 更新用户输入转录内容');
            const updatedHistory = [...prev];
            updatedHistory[lastUserInputIndex] = {
              ...updatedHistory[lastUserInputIndex],
              content: text,
              timestamp: now,
              isFinal
            };
            return updatedHistory;
          }
        }
        
        // 添加新条目
        log('info', 'InterviewScreen: 添加新转录条目', newEntry);
        return [...prev, newEntry];
      });
      
      // 自动滚动到最新内容
      setTimeout(() => {
        transcriptScrollViewRef.current?.scrollToEnd({ animated: true });
      }, 100);
    }
  }, []);

  // 监听App状态变化，处理异常退出
  useEffect(() => {
    const handleAppStateChange = (nextAppState: string) => {
      log('info', 'InterviewScreen: App状态变化:', nextAppState);
      
      // 注释掉自动清理逻辑，避免因App状态变化导致的意外断开
      // 在局域网环境下，短暂的后台切换不应该断开连接
      // if (nextAppState === 'background' || nextAppState === 'inactive') {
      //   // App进入后台或非活跃状态，保存当前面试状态
      //   if (wsRecording && sessionId) {
      //     log('info', 'InterviewScreen: App进入后台，执行清理并结束面试会话');
      //     cleanup(); // 调用完整的清理函数
      //   }
      // }
      
      // 只在App完全关闭时才清理（这种情况下组件会自动卸载，useEffect的cleanup会处理）
      if (nextAppState === 'background') {
        log('info', 'InterviewScreen: App进入后台，保持连接状态');
      } else if (nextAppState === 'active') {
        log('info', 'InterviewScreen: App回到前台');
        // 可以在这里检查连接状态，如果断开了可以尝试重连
        if (!WebSocketService.isConnected() && interviewStarted) {
          log('info', 'InterviewScreen: 检测到连接断开，尝试重新连接');
          connectWebSocket();
        }
      }
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);
    
    return () => {
      subscription?.remove();
    };
  }, [wsRecording, sessionId, interviewStarted]);

  // Handle back button press
  useEffect(() => {
    const unsubscribe = navigation.addListener('beforeRemove', (e: any) => {
      if (!interviewStarted) {
        return;
      }

      e.preventDefault();

      Alert.alert(
        '确认返回',
        '面试正在进行中，您确定要结束面试并返回吗？',
        [
          { text: '取消', style: 'cancel', onPress: () => {} },
          {
            text: '确定结束',
            style: 'destructive',
            onPress: async () => {
              // 如果是模拟面试且有对话内容，也要进行评分
              if (isMockInterview && transcriptHistory.length > 0) {
                await handleMockInterviewEvaluation();
              } else {
                await cleanup();
                navigation.dispatch(e.data.action);
              }
            },
          },
        ]
      );
    });

    return unsubscribe;
  }, [navigation, interviewStarted]);

  // Connect to WebSocket on mount and clean up on unmount
  useEffect(() => {
    let isMounted = true;
    
    const initConnection = async () => {
      if (isMounted) {
        await connectWebSocket();
      }
    };
    
    initConnection();
    
    return () => {
      isMounted = false;
      cleanup();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Update WebSocket callbacks when they change to avoid stale closures
  useEffect(() => {
    WebSocketService.setCallbacks({
      onMessage: handleWebSocketMessage,
      onStatusUpdate: handleStatusUpdate,
      onError: handleError,
      onTranscript: handleTranscript,
    });
  }, [handleWebSocketMessage, handleStatusUpdate, handleError, handleTranscript]);




  // 开始面试会话
  const startInterviewSession = async () => {
    try {
      const response = await fetch(`${BASE_URL}/api/v1/interview/start`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ domain }),
      });
      
      const data = await response.json();
      if (data.code === 200) {
        setSessionId(data.data.session_id);
        return data.data.session_id;
      } else {
        throw new Error(data.message || '开始面试会话失败');
      }
    } catch (error: any) {
      log('error', '开始面试会话失败:', error);
      throw error;
    }
  };

  // 结束面试会话
  const endInterviewSession = async (sessionId: string) => {
    try {
      const response = await fetch(`${BASE_URL}/api/v1/interview/end`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ session_id: sessionId }),
      });
      
      const data = await response.json();
      if (data.code !== 200) {
        log('error', '结束面试会话失败:', data.message);
      }
    } catch (error: any) {
      log('error', '结束面试会话失败:', error);
    }
  };

  // 开始计时器
  const startTimer = () => {
    const startTime = new Date();
    setInterviewStartTime(startTime);
    setCurrentDuration(0);
    
    // 获取用户总剩余时长（秒）
    const totalRemainingSeconds = userInfo.total_remaining_duration || 0;
    // 获取模拟面试时长限制（秒），如果没有设置则使用用户剩余时长
    const mockInterviewTimeLimit = timeLimit || totalRemainingSeconds;
    
    let hasShownTimeoutAlert = false; // 防止重复弹窗
    let hasShownWarning = false; // 防止重复30秒警告
    let hasShownMockInterviewWarning = false; // 防止重复模拟面试5分钟警告
    
    timerRef.current = setInterval(() => {
      const now = new Date();
      const duration = Math.floor((now.getTime() - startTime.getTime()) / 1000);
      setCurrentDuration(duration);
      
      // 对于模拟面试，优先检查模拟面试时长限制
      if (isMockInterview && timeLimit) {
        // 模拟面试45分钟到时自动结束
        if (duration >= mockInterviewTimeLimit && !hasShownTimeoutAlert) {
          hasShownTimeoutAlert = true;
          log('info', 'InterviewScreen: 模拟面试时长已达45分钟，自动结束');
          
          // 立即停止定时器
          if (timerRef.current) {
            clearInterval(timerRef.current);
            timerRef.current = null;
          }
          
          Alert.alert(
            '模拟面试结束',
            '45分钟模拟面试时间已到，系统将自动结束面试并生成评分报告。',
            [
              {
                text: '确定',
                onPress: async () => {
                  // 模拟面试自动结束时也要进行评分
                  if (transcriptHistory.length > 0) {
                    await handleMockInterviewEvaluation();
                  } else {
                    await cleanup();
                    navigation.goBack();
                  }
                }
              }
            ],
            { cancelable: false }
          );
          return;
        }
        
        // 模拟面试剩余5分钟提醒
        const mockRemainingSeconds = mockInterviewTimeLimit - duration;
        if (mockRemainingSeconds === 300 && !hasShownMockInterviewWarning) { // 5分钟 = 300秒
          hasShownMockInterviewWarning = true;
          Alert.alert(
            '时间提醒',
            '模拟面试还剩5分钟，请注意时间安排。',
            [{ text: '知道了' }]
          );
        }
      }
      
      // 检查是否超过用户剩余时长（这个检查对所有面试都适用）
      if (duration >= totalRemainingSeconds && !hasShownTimeoutAlert) {
        hasShownTimeoutAlert = true;
        log('info', 'InterviewScreen: 用户时长已达上限，自动结束面试');
        
        // 立即停止定时器
        if (timerRef.current) {
          clearInterval(timerRef.current);
          timerRef.current = null;
        }
        
        Alert.alert(
          '时长已用完',
          '您的可用时长已用完，面试将自动结束。',
          [
            {
              text: '确定',
              onPress: async () => {
                // 如果是模拟面试且有对话内容，也要进行评分
                if (isMockInterview && transcriptHistory.length > 0) {
                  await handleMockInterviewEvaluation();
                } else {
                  await cleanup();
                  if (navigation.canGoBack()) {
                    navigation.goBack();
                  } else {
                    navigation.navigate('Home');
                  }
                }
              }
            }
          ],
          { cancelable: false }
        );
        return;
      }
      
      // 在用户剩余30秒时提醒（只提醒一次）
      const remainingSeconds = totalRemainingSeconds - duration;
      if (remainingSeconds === 30 && !hasShownWarning && !isMockInterview) {
        hasShownWarning = true;
        Alert.alert(
          '时长提醒',
          '您的可用时长还剩30秒，请注意时间。',
          [{ text: '知道了' }]
        );
      }
    }, 1000);
  };


  const startInterview = async () => {
    if (!wsConnected) {
      await connectWebSocket();
    }
    
    // 再次检查用户剩余时长（防止在面试页面停留时间过长后时长不足）
    const totalRemainingTime = userInfo.total_remaining_duration || 0;
    if (totalRemainingTime < 60) {
      Alert.alert('时长不足', '您的可用时长不足1分钟，无法开始面试，请购买套餐后继续使用', [
        { text: '确定', onPress: () => {
          if (navigation.canGoBack()) {
            navigation.goBack();
          } else {
            navigation.navigate('Home');
          }
        }}
      ]);
      return;
    }
    
    try {
      setIsLoading(true);


      // 先开始面试会话
      const newSessionId = await startInterviewSession();
      
      // 注册会话ID到WebSocket连接（仅在代理模式下）
      if (wsConnected && newSessionId) {
        // 检查当前连接是否使用代理，只有在代理模式下才发送控制消息
        const isUsingProxy = WebSocketService.isUsingProxy();
        
        if (isUsingProxy) {
          WebSocketService.sendControlMessage({
            type: 'register_session',
            session_id: newSessionId,
          });
          log('info', 'InterviewScreen: 会话ID已注册到WebSocket (代理模式)', newSessionId);
        } else {
          log('info', 'InterviewScreen: 直连模式，跳过会话注册', newSessionId);
        }
      }

      // 确保音频服务已就绪
      if (!audioServiceReady || !audioOutputService) {
        throw new Error('音频服务未就绪');
      }
      
      // 重置音频统计信息，确保新面试的统计数据准确
      if (audioOutputService && audioOutputService.resetStats) {
        audioOutputService.resetStats();
      }
      WebSocketService.resetAudioStats();
      

      const success = await EnhancedAudioInputService.startRecording();
      if (success) {
        setWsRecording(true);
        setInterviewStarted(true); // 面试真正开始

        
        
        // 开始计时
        startTimer();
      } else {

        Alert.alert('错误', '无法启动录音功能');
        // 如果录音失败，结束会话
        await endInterviewSession(newSessionId);
      }
    } catch (error: any) {
      Alert.alert('错误', error.message || '开始面试失败');
    } finally {
      setIsLoading(false);
    }
  };

  const pauseInterview = async () => {
    try {
      setIsLoading(true);

      
      await EnhancedAudioInputService.stopRecording();
      setWsRecording(false);

      
      // 停止计时器但不结束会话（暂停功能）
      stopTimer();
    } catch (error: any) {
      Alert.alert('错误', error.message || '停止录音失败');
    } finally {
      setIsLoading(false);
    }
  };

  const handleMuteToggle = () => {
    const newMutedState = !isMuted;
    setIsMuted(newMutedState);
    EnhancedAudioInputService.setMuted(newMutedState);
  };

  const cleanup = async () => {
    if (isCleaning.current) return;
    isCleaning.current = true;

    try {
      const caller = new Error().stack?.split('\n')[2]?.trim() || 'Unknown caller';
      log('info', `InterviewScreen: cleanup() called by: ${caller}`);

      setInterviewStarted(false);
      setWsRecording(false);
      
      if (audioOutputService) {
        await audioOutputService.stop();
      }
      
      stopTimer();
      
      if (sessionId && wsRecording) {
        await endInterviewSession(sessionId);
        if (token) {
          dispatch(refreshUserInfo(token));
        }
      }
      
      if (wsRecording) {
        await EnhancedAudioInputService.stopRecording();
      }
      
      WebSocketService.disconnect();
      await EnhancedAudioInputService.cleanup();
      connectionAttempted.current = false;
      
      log('info', 'InterviewScreen: Cleanup complete');
    } catch (error) {
      log('error', 'InterviewScreen: Cleanup failed:', error);
    } finally {
      isCleaning.current = false;
    }
  };

  const showDebugInfo = async () => {
    try {
      if (!audioServiceReady) {
        Alert.alert('调试信息', '音频服务尚未准备就绪，请稍后再试。');
        return;
      }

      const inputStats = EnhancedAudioInputService.getStats();
      const outputStats = audioOutputService?.getStats ? await audioOutputService.getStats() : null;
      const wsConfig = WebSocketService.getCurrentConfig();
      const currentVoice = await getPreferredVoice();
      const isConnected = WebSocketService.isConnected();
      const isProxy = WebSocketService.isUsingProxy();
      const audioStats = WebSocketService.getAudioStats();

      const connectionDuration = interviewStartTime ?
        Math.floor((Date.now() - interviewStartTime.getTime()) / 1000) : 0;

      // 关键统计信息
      const wsReceivedMessages = audioStats.received;
      const receivedChunks = outputStats?.chunksReceived ?? 0;
      const ajbBufferSize = outputStats?.nativeBufferCount ?? 0;
      const errors = outputStats?.errors ?? 0;
      
      // AJB状态分析
      let ajbStatus = '';
      if (ajbBufferSize === 0) {
        ajbStatus = '🔴 缓冲区为空';
      } else if (ajbBufferSize <= 5) {
        ajbStatus = '🟢 缓冲区正常';
      } else {
        ajbStatus = '🟡 缓冲区偏高';
      }

      let debugMessage = `
--- 🔌 连接状态 ---
- 连接状态: ${isConnected ? '✅ 已连接' : '❌ 未连接'}
- 连接时长: ${Math.floor(connectionDuration/60)}分${connectionDuration%60}秒
- 面试状态: ${interviewStarted ? '进行中' : '未开始/已结束'}
- 录音状态: ${wsRecording ? '录音中' : '未录音'}
- 静音状态: ${inputStats.isMuted ? '🔇 静音' : '🔊 正常'}

--- 🔧 配置信息 ---
- 连接模式: ${wsConfig?.connection_mode || '未配置'}
- 使用代理: ${isProxy ? '是' : '否'}
- 当前语音: ${currentVoice}
- 音频输入服务: ${EnhancedAudioInputService.getServiceName()}
- 音频输出服务: ${currentPlaybackMethod?.name || '未知'}
- 音频输出模块: ${outputStats?.moduleType || 'N/A'}

--- 🎵 音频配置 ---
- 发送采样率: ${AUDIO_CONFIG.sendSampleRate}Hz
- 接收采样率: ${AUDIO_CONFIG.receiveSampleRate}Hz  
- 声道数: ${AUDIO_CONFIG.channels}
- 音频格式: ${AUDIO_CONFIG.format}

--- 📊 AJB 统计 ---
- AJB 缓冲区大小: ${ajbBufferSize}
- AJB 状态: ${ajbStatus}
- 抖动: ${outputStats?.jitter ?? 'N/A'}
- 丢包率: ${outputStats?.packetLossRate ?? 'N/A'}

--- 📊 音频流诊断 ---
- 音频包发送: ${audioStats.sent}
- 音频消息接收: ${wsReceivedMessages}
- 音频块接收: ${receivedChunks}
- 错误次数: ${errors}
`;

      Alert.alert(
        '🔍 实时调试信息',
        debugMessage.trim(),
        [
          {
            text: '复制',
            onPress: () => {
              Clipboard.setString(debugMessage.trim());
              Alert.alert('✅ 已复制', '调试信息已复制到剪贴板');
            }
          },
          {
            text: '关闭',
            style: 'cancel'
          }
        ]
      );
    } catch (error) {
      console.error("生成调试信息时出错:", error);
      Alert.alert('调试错误', `生成调试信息时发生错误: ${error instanceof Error ? error.message : String(error)}`);
    }
  };

  const endInterview = () => {
    const durationText = currentDuration > 0 ? `\n本次面试时长：${Math.floor(currentDuration / 60)}分${currentDuration % 60}秒` : '';
    
    Alert.alert(
      '结束面试',
      `确定要结束本次面试吗？${durationText}`,
      [
        { text: '取消' },
        { 
          text: '确定结束', 
          onPress: async () => {
            // 立即停止音频播放，不等待其他清理操作
            if (audioOutputService && audioOutputService.immediateStop) {
              audioOutputService.immediateStop();
            }
            
            // 如果是模拟面试，进行评分
            if (isMockInterview && transcriptHistory.length > 0) {
              await handleMockInterviewEvaluation();
            } else {
              await cleanup();
              if (navigation.canGoBack()) {
                navigation.goBack();
              } else {
                navigation.navigate('Home');
              }
            }
          }
        }
      ]
    );
  };

  // 处理模拟面试评分
  const handleMockInterviewEvaluation = async () => {
    try {

      
      // 构建对话记录
      const transcript = transcriptHistory
        .filter(item => item.isFinal)
        .map(item => `${item.role === 'user' ? '求职者' : '面试官'}: ${item.content}`)
        .join('\n');

      // 更智能的对话内容判断
      const finalTranscripts = transcriptHistory.filter(item => item.isFinal);
      const userMessages = finalTranscripts.filter(item => item.role === 'user');
      const assistantMessages = finalTranscripts.filter(item => item.role === 'assistant');
      
      // 检查对话质量的多个维度
      const hasMinimumLength = transcript.length >= 50;
      const hasUserInput = userMessages.length >= 2; // 至少2次用户发言
      const hasAssistantResponse = assistantMessages.length >= 2; // 至少2次AI回复
      const hasInteraction = userMessages.length > 0 && assistantMessages.length > 0; // 有交互
      
      if (!hasMinimumLength || !hasUserInput || !hasAssistantResponse || !hasInteraction) {
        let message = '面试对话内容不足，无法进行有效评分：\n';
        if (!hasMinimumLength) message += '• 对话内容太短\n';
        if (!hasUserInput) message += '• 用户发言次数不足\n';
        if (!hasAssistantResponse) message += '• AI回复次数不足\n';
        if (!hasInteraction) message += '• 缺少有效交互\n';
        message += '\n建议进行更长时间的面试练习。';
        
        Alert.alert(
          '对话内容不足',
          message,
          [
            {
              text: '确定',
              onPress: async () => {
                await cleanup();
                navigation.goBack();
              }
            }
          ]
        );
        return;
      }

      // 调用评分API
      const response = await fetch(`${BASE_URL}/api/v1/mock-interview/evaluate`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          session_id: sessionId,
          transcript: transcript,
          profession: domain,
          difficulty: difficulty,
          duration: Math.floor(currentDuration / 60), // 转换为分钟
        }),
      });

      if (!response.ok) {
        throw new Error('评分请求失败');
      }

      const result = await response.json();
      if (result.code !== 200) {
        throw new Error(result.message || '评分失败');
      }

      // 清理资源
      await cleanup();

      // 跳转到评分页面
      navigation.replace('InterviewScore', {
        scoreData: result.data,
      });

    } catch (error: any) {
      console.error('模拟面试评分失败:', error);
      Alert.alert(
        '评分失败',
        '生成评分报告时出现错误，但面试已正常结束。',
        [
          {
            text: '确定',
            onPress: async () => {
              await cleanup();
              navigation.goBack();
            }
          }
        ]
      );
    }
  };



  return (
    <GradientBackground colors={['#1e3c72', '#2a5298']}>
      <View style={styles.container}>
        {/* 右上角按钮组 */}
        <View style={styles.topRightButtons}>
          <TouchableOpacity 
            style={styles.debugIcon}
            onPress={() => setShowDebugController(true)}
          >
            <Icon name="bug-report" size={20} color={theme.colors.primary} />
          </TouchableOpacity>
          <TouchableOpacity 
            style={styles.tipsIcon}
            onPress={() => setShowTips(!showTips)}
          >
            <Icon name="lightbulb" size={20} color={theme.colors.warning} />
          </TouchableOpacity>
        </View>

        {/* 提示弹窗 */}
        {showTips && (
          <View style={styles.tipsOverlay}>
            <Card style={styles.tipsPopup} shadow="lg">
              <View style={styles.tipsHeader}>
                <Icon name="lightbulb" size={18} color={theme.colors.warning} />
                <Text style={styles.tipsTitle}>使用提示</Text>
                <TouchableOpacity onPress={() => setShowTips(false)}>
                  <Icon name="close" size={18} color={theme.colors.textSecondary} />
                </TouchableOpacity>
              </View>
              <View style={styles.tipsList}>
                <View style={styles.tipItem}>
                  <Icon name="volume-up" size={14} color={theme.colors.textSecondary} />
                  <Text style={styles.tipText}>保持安静的环境</Text>
                </View>
                <View style={styles.tipItem}>
                  <Icon name="mic" size={14} color={theme.colors.textSecondary} />
                  <Text style={styles.tipText}>清晰地表达想法</Text>
                </View>
                <View style={styles.tipItem}>
                  <Icon name="android" size={14} color={theme.colors.textSecondary} />
                  <Text style={styles.tipText}>AI实时回答建议</Text>
                </View>
              </View>
            </Card>
          </View>
        )}

        {/* 调试日志控制器 */}
        <DebugLogController
          visible={showDebugController}
          onClose={() => setShowDebugController(false)}
        />

        <ScrollView style={styles.scrollContainer} showsVerticalScrollIndicator={false}>
          {/* Status Card */}
          <Card style={styles.statusCard} shadow="md">
            <View style={styles.statusHeader}>
              <Icon name={isMockInterview ? "psychology" : "support-agent"} size={18} color={theme.colors.success} />
              <Text style={styles.statusTitle}>
                {isMockInterview ? "模拟面试状态" : "实时助手状态"}
              </Text>
              <View style={[styles.modeBadge, { backgroundColor: isMockInterview ? theme.colors.warning : theme.colors.primary }]}>
                <Text style={styles.modeBadgeText}>
                  {isMockInterview ? "练习模式" : "辅助模式"}
                </Text>
              </View>
            </View>
            
            <View style={styles.statusRow}>
              <Text style={styles.statusLabel}>状态:</Text>
              <Text style={styles.statusValue}>
                {!audioServiceReady ? '音频初始化中' : 
                 interviewStarted ? '进行中' : '准备开始'}
              </Text>
            </View>
            
            <View style={styles.statusRow}>
              <Text style={styles.statusLabel}>连接:</Text>
              <View style={styles.statusIndicator}>
                <View style={[styles.statusDot, { backgroundColor: wsConnected ? theme.colors.success : theme.colors.error }]} />
                <Text style={styles.statusValue}>{wsConnected ? '已连接' : '未连接'}</Text>
              </View>
            </View>
            
            
            {wsRecording && (
              <View style={styles.statusRow}>
                <Text style={styles.statusLabel}>已用时长:</Text>
                <Text style={styles.timerText}>
                  {Math.floor(currentDuration / 60)}分{currentDuration % 60}秒
                </Text>
              </View>
            )}
            
            {wsRecording && isMockInterview && timeLimit && (
              <View style={styles.statusRow}>
                <Text style={styles.statusLabel}>模拟面试剩余:</Text>
                <Text style={[
                  styles.timerText,
                  (timeLimit - currentDuration) <= 300 && styles.warningText // 5分钟内显示警告色
                ]}>
                  {Math.max(0, Math.floor((timeLimit - currentDuration) / 60))}分
                  {Math.max(0, (timeLimit - currentDuration) % 60)}秒
                </Text>
              </View>
            )}
            
            {wsRecording && (
              <View style={styles.statusRow}>
                <Text style={styles.statusLabel}>{isMockInterview ? '账户剩余' : '剩余时长'}:</Text>
                <Text style={[
                  styles.timerText,
                  (userInfo.total_remaining_duration - currentDuration) <= 60 && styles.warningText
                ]}>
                  {Math.max(0, Math.floor((userInfo.total_remaining_duration - currentDuration) / 60))}分
                  {Math.max(0, (userInfo.total_remaining_duration - currentDuration) % 60)}秒
                </Text>
              </View>
            )}
            
            {isLoading && (
              <View style={styles.loadingRow}>
                <ActivityIndicator size="small" color={theme.colors.primary} />
                <Text style={styles.loadingText}>处理中...</Text>
              </View>
            )}
          </Card>



          {/* Control Panel */}
          <Card style={styles.controlCard} shadow="md">
            <View style={styles.controlHeader}>
              <Icon name="control-camera" size={18} color={theme.colors.primary} />
              <Text style={styles.controlTitle}>面试控制</Text>
            </View>
            
            <View style={styles.controlGrid}>
              {!interviewStarted ? (
                <TouchableOpacity 
                  style={[styles.controlButton, styles.startButton, isLoading && styles.disabledButton]}
                  onPress={startInterview}
                  disabled={isLoading || !wsConnected}
                >
                  <Icon name="play-arrow" size={16} color="white" />
                  <Text style={styles.controlButtonText}>
                    {isLoading ? '准备中' : '开始'}
                  </Text>
                </TouchableOpacity>
              ) : (
                <TouchableOpacity 
                  style={[styles.controlButton, styles.pauseButton, isLoading && styles.disabledButton]}
                  onPress={pauseInterview}
                  disabled={isLoading}
                >
                  <Icon name="pause" size={16} color="white" />
                  <Text style={styles.controlButtonText}>暂停</Text>
                </TouchableOpacity>
              )}
              
              <TouchableOpacity 
                style={[styles.controlButton, styles.transcriptButton]}
                onPress={() => setShowTranscript(!showTranscript)}
              >
                <Icon name={showTranscript ? "visibility-off" : "visibility"} size={16} color="white" />
                <Text style={styles.controlButtonText}>
                  {showTranscript ? '隐藏' : '转录'}
                </Text>
              </TouchableOpacity>
              

              
              {wsRecording && (
                <TouchableOpacity 
                  style={[styles.controlButton, styles.muteButton]}
                  onPress={handleMuteToggle}
                >
                  <Icon name={isMuted ? "mic-off" : "mic"} size={16} color="white" />
                  <Text style={styles.controlButtonText}>
                    {isMuted ? '取消静音' : '静音'}
                  </Text>
                </TouchableOpacity>
              )}
              
              <TouchableOpacity 
                style={[styles.controlButton, styles.endButton]}
                onPress={endInterview}
              >
                <Icon name="stop" size={16} color="white" />
                <Text style={styles.controlButtonText}>结束</Text>
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={[styles.controlButton, styles.debugButton]}
                onPress={showDebugInfo}
              >
                <Icon name="bug-report" size={16} color="white" />
                <Text style={styles.controlButtonText}>调试</Text>
              </TouchableOpacity>
            </View>
          </Card>

          {/* Enhanced Transcript Section */}
          {showTranscript && (
            <Card style={styles.transcriptCard} shadow="md">
              <View style={styles.transcriptHeader}>
                <Icon name="record-voice-over" size={18} color={theme.colors.secondary} />
                <Text style={styles.transcriptTitle}>实时转录</Text>
                <Text style={styles.transcriptCount}>({transcriptHistory.length}条)</Text>
              </View>
              
              <ScrollView 
                ref={transcriptScrollViewRef}
                style={styles.transcriptScrollView}
                showsVerticalScrollIndicator={true}
                nestedScrollEnabled={true}
                contentContainerStyle={styles.transcriptScrollContent}
              >
                {transcriptHistory.length === 0 ? (
                  <Text style={styles.emptyTranscript}>暂无转录内容</Text>
                ) : (
                  transcriptHistory.map((item) => (
                    <View key={item.id} style={[
                      styles.transcriptItem,
                      item.role === 'user' ? styles.userTranscript : styles.assistantTranscript
                    ]}>
                      <View style={styles.transcriptItemHeader}>
                        <Text style={styles.transcriptRole}>
                          {item.role === 'user' ? '👤 用户' : '🤖 助手'}
                          {item.source === 'output' && ' (语音)'}
                          {item.source === 'text' && ' (文本)'}
                        </Text>
                        <Text style={styles.transcriptTime}>
                          {item.timestamp.toLocaleTimeString()}
                        </Text>
                      </View>
                      <Text style={[
                        styles.transcriptText,
                        !item.isFinal && styles.transcriptPending
                      ]}>
                        {item.content}
                      </Text>
                    </View>
                  ))
                )}
              </ScrollView>
            </Card>
          )}
        </ScrollView>
      </View>
    </GradientBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    position: 'relative',
  },
  scrollContainer: {
    flex: 1,
    paddingHorizontal: theme.spacing.md,
  },
  // 右上角按钮组
  topRightButtons: {
    position: 'absolute',
    top: 30,
    right: theme.spacing.md,
    zIndex: 1000,
    flexDirection: 'row',
    gap: 8,
  },
  tipsIcon: {
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 20,
    padding: theme.spacing.sm,
  },
  debugIcon: {
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 20,
    padding: theme.spacing.sm,
    ...theme.shadows.md,
  },
  // 提示弹窗覆盖层
  tipsOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    zIndex: 999,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: theme.spacing.lg,
  },
  // 提示弹窗
  tipsPopup: {
    width: '100%',
    maxWidth: 300,
    backgroundColor: theme.colors.surface,
  },
  statusCard: {
    marginTop: theme.spacing.md,
    marginBottom: theme.spacing.md,
  },
  statusHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.md,
  },
  statusTitle: {
    fontSize: theme.fontSize.base,
    fontWeight: theme.fontWeight.semibold,
    color: theme.colors.textPrimary,
    marginLeft: theme.spacing.sm,
    flex: 1,
  },
  modeBadge: {
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.xs,
    borderRadius: theme.borderRadius.full,
  },
  modeBadgeText: {
    fontSize: theme.fontSize.xs,
    fontWeight: theme.fontWeight.medium,
    color: theme.colors.textInverse,
  },
  statusRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.sm,
  },
  statusLabel: {
    fontSize: theme.fontSize.sm,
    color: theme.colors.textSecondary,
  },
  statusValue: {
    fontSize: theme.fontSize.sm,
    color: theme.colors.textPrimary,
    fontWeight: theme.fontWeight.medium,
  },
  statusIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: theme.spacing.xs,
  },
  timerText: {
    fontSize: theme.fontSize.sm,
    color: theme.colors.success,
    fontWeight: theme.fontWeight.semibold,
  },
  warningText: {
    color: theme.colors.error,
  },
  loadingRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: theme.spacing.sm,
  },
  loadingText: {
    fontSize: theme.fontSize.sm,
    color: theme.colors.textSecondary,
    marginLeft: theme.spacing.sm,
  },
  controlCard: {
    marginBottom: theme.spacing.md,
  },
  controlHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.md,
  },
  controlTitle: {
    fontSize: theme.fontSize.base,
    fontWeight: theme.fontWeight.semibold,
    color: theme.colors.textPrimary,
    marginLeft: theme.spacing.sm,
  },
  controlGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    gap: theme.spacing.sm,
  },
  controlButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.sm,
    borderRadius: theme.borderRadius.md,
    minWidth: '30%',
    ...theme.shadows.sm,
  },
  controlButtonText: {
    fontSize: theme.fontSize.xs,
    color: theme.colors.textInverse,
    fontWeight: theme.fontWeight.medium,
    marginLeft: theme.spacing.xs,
  },
  startButton: {
    backgroundColor: theme.colors.success,
  },
  pauseButton: {
    backgroundColor: theme.colors.warning,
  },
  transcriptButton: {
    backgroundColor: theme.colors.secondary,
  },

  muteButton: {
    backgroundColor: theme.colors.primary,
  },
  endButton: {
    backgroundColor: theme.colors.error,
  },
  testButton: {
    backgroundColor: '#FF9800', // 橙色测试按钮
  },
  debugButton: {
    backgroundColor: '#9C27B0', // 紫色调试按钮
  },
  disabledButton: {
    opacity: 0.6,
  },
  // 增强的转录卡片
  transcriptCard: {
    marginBottom: theme.spacing.xl,
    flex: 1,
    minHeight: 300,
  },
  transcriptHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: theme.spacing.md,
  },
  transcriptTitle: {
    fontSize: theme.fontSize.base,
    fontWeight: theme.fontWeight.semibold,
    color: theme.colors.textPrimary,
    marginLeft: theme.spacing.sm,
    flex: 1,
  },
  transcriptCount: {
    fontSize: theme.fontSize.xs,
    color: theme.colors.textSecondary,
    fontWeight: theme.fontWeight.medium,
  },
  // 转录内容滚动视图
  transcriptScrollView: {
    flex: 1,
    maxHeight: 400, // 增加最大高度
    minHeight: 200,
  },
  transcriptScrollContent: {
    paddingBottom: theme.spacing.md,
    flexGrow: 1,
  },
  emptyTranscript: {
    fontSize: theme.fontSize.sm,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    fontStyle: 'italic',
    paddingVertical: theme.spacing.lg,
  },
  transcriptItem: {
    padding: theme.spacing.md,
    marginBottom: theme.spacing.sm,
    borderRadius: theme.borderRadius.md,
    borderLeftWidth: 4,
    ...theme.shadows.sm,
  },
  userTranscript: {
    backgroundColor: '#E3F2FD',
    borderLeftColor: theme.colors.secondary,
  },
  assistantTranscript: {
    backgroundColor: '#E8F5E8',
    borderLeftColor: theme.colors.success,
  },
  transcriptItemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.sm,
  },
  transcriptRole: {
    fontSize: theme.fontSize.sm,
    fontWeight: theme.fontWeight.bold,
    color: theme.colors.textSecondary,
  },
  transcriptText: {
    fontSize: theme.fontSize.sm,
    color: theme.colors.textPrimary,
    lineHeight: 20,
    letterSpacing: 0.3,
  },
  transcriptPending: {
    fontStyle: 'italic',
    opacity: 0.7,
  },
  transcriptTime: {
    fontSize: theme.fontSize.xs,
    color: theme.colors.textTertiary,
  },
  // 测试输入框样式
  testInputCard: {
    marginBottom: theme.spacing.md,
  },
  testInputHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.md,
  },
  testInputTitle: {
    fontSize: theme.fontSize.base,
    fontWeight: theme.fontWeight.semibold,
    color: theme.colors.textPrimary,
    marginLeft: theme.spacing.sm,
    flex: 1,
  },
  testInputSubtitle: {
    fontSize: theme.fontSize.xs,
    color: theme.colors.textSecondary,
  },
  testInputContainer: {
    gap: theme.spacing.md,
  },
  testTextInput: {
    borderWidth: 1,
    borderColor: theme.colors.textSecondary,
    borderRadius: theme.borderRadius.md,
    padding: theme.spacing.md,
    fontSize: theme.fontSize.sm,
    color: theme.colors.textPrimary,
    backgroundColor: 'rgba(255,255,255,0.05)',
    textAlignVertical: 'top',
    minHeight: 80,
  },
  testInputButtons: {
    flexDirection: 'row',
    gap: theme.spacing.sm,
  },
  testSendButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: theme.colors.success,
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    borderRadius: theme.borderRadius.md,
    flex: 1,
  },
  testClearButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: theme.colors.textSecondary,
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    borderRadius: theme.borderRadius.md,
    flex: 1,
  },
  testButtonText: {
    fontSize: theme.fontSize.sm,
    color: theme.colors.textInverse,
    fontWeight: theme.fontWeight.medium,
    marginLeft: theme.spacing.xs,
  },
  testHint: {
    fontSize: theme.fontSize.xs,
    color: theme.colors.textSecondary,
    fontStyle: 'italic',
    textAlign: 'center',
    paddingHorizontal: theme.spacing.md,
  },
  // 提示相关样式
  tipsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: theme.spacing.md,
  },
  tipsTitle: {
    fontSize: theme.fontSize.base,
    fontWeight: theme.fontWeight.semibold,
    color: theme.colors.textPrimary,
    marginLeft: theme.spacing.sm,
    flex: 1,
  },
  tipsList: {
    gap: theme.spacing.sm,
  },
  tipItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  tipText: {
    fontSize: theme.fontSize.sm,
    color: theme.colors.textSecondary,
    marginLeft: theme.spacing.sm,
  },
});

export default InterviewScreen;
