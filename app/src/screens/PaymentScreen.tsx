import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  TouchableOpacity, 
  StyleSheet, 
  Alert, 
  ScrollView, 
  ActivityIndicator,
  Modal,
  Linking,
  DeviceEventEmitter,
  Animated,
  Dimensions,
  Image
} from 'react-native';
import { useSelector, useDispatch } from 'react-redux';
import { RootState, AppDispatch } from '../store';
import { refreshUserInfo, setSelectedMode } from '../store/userSlice';
import PaymentService, { Product, PaymentMethod, PaymentStatus } from '../services/PaymentService';
import Icon from 'react-native-vector-icons/MaterialIcons';
import PaymentSuccessModal from '../components/PaymentSuccessModal';
import { theme } from '../styles/theme';

const PaymentScreen = ({ navigation }: any) => {
  const dispatch = useDispatch<AppDispatch>();
  const { token, userInfo, selectedMode } = useSelector((state: RootState) => state.user);
  const [products, setProducts] = useState<Product[]>([]);
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [paymentLoading, setPaymentLoading] = useState(false);
  const [modalAnimation] = useState(new Animated.Value(0));
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [successProduct, setSuccessProduct] = useState<Product | null>(null);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const [productsData, methodsData] = await Promise.all([
        PaymentService.getProducts(token),
        PaymentService.getPaymentMethods(token)
      ]);
      
      // 过滤出时长包商品
      const durationProducts = productsData.filter(p => p.type === 2 && p.status === 1);
      setProducts(durationProducts);
      setPaymentMethods(methodsData);
    } catch (error) {
      console.error('加载数据失败:', error);
      Alert.alert('错误', '加载数据失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    return `${minutes}分钟`;
  };

  const getPackageIcon = (productName: string) => {
    if (productName.includes('基础') || productName.includes('体验')) {
      return '🎯';
    } else if (productName.includes('标准') || productName.includes('专业')) {
      return '⭐';
    } else if (productName.includes('高级') || productName.includes('企业')) {
      return '👑';
    }
    return '💎';
  };

  const getPackageBadge = (productName: string) => {
    if (productName.includes('标准') || productName.includes('专业')) {
      return '推荐';
    } else if (productName.includes('高级') || productName.includes('企业')) {
      return '热门';
    }
    return null;
  };

  const renderPaymentIcon = (paymentType: string) => {
    switch (paymentType) {
      case 'wechat':
        return <Image source={require('../assets/images/wechat.png')} style={styles.paymentIcon} />;
      case 'alipay':
        return <Image source={require('../assets/images/alipay.png')} style={styles.paymentIcon} />;
      default:
        return <Icon name="payment" size={24} color="#666" />;
    }
  };

  const handleSelectProduct = (product: Product) => {
    setSelectedProduct(product);
    setShowPaymentModal(true);
    
    // 模态框动画
    Animated.spring(modalAnimation, {
      toValue: 1,
      useNativeDriver: true,
    }).start();
  };

  const handlePayment = async (paymentType: string) => {
    if (!selectedProduct) return;

    try {
      setPaymentLoading(true);
      setShowPaymentModal(false);

      const order = await PaymentService.createOrder(token, selectedProduct.id);

      let payResult;
      if (paymentType === 'wechat') {
        payResult = await PaymentService.payWithWeChat(token, order.order_no);
      } else if (paymentType === 'alipay') {
        payResult = await PaymentService.payWithAlipay(token, order.order_no);
      }

      if (payResult?.success) {
        setSuccessProduct(selectedProduct);
        setShowSuccessModal(true);
        
        setTimeout(() => {
          dispatch(refreshUserInfo(token));
        }, 1000);
      } else {
        // 用户取消或支付失败，静默处理，仅在控制台打印日志
        console.log('Payment was not successful or was cancelled.');
        cancelOrder(order.order_no);
      }
    } catch (error) {
      console.error('支付处理失败:', error);
      // 可以在这里添加一个非阻塞的提示，比如Toast
    } finally {
      setPaymentLoading(false);
    }
  };

  const cancelOrder = async (orderNo: string) => {
    try {
      await PaymentService.cancelOrder(token, orderNo);
      console.log(`Order ${orderNo} cancelled.`);
    } catch (error) {
      console.error('取消订单失败:', error);
    }
  };

  if (loading) {
    return (
      <View style={[styles.container, styles.centerContent]}>
        <ActivityIndicator size="large" color="#667eea" />
        <Text style={styles.loadingText}>加载中...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false} contentContainerStyle={styles.scrollContent}>
        {/* Header Section */}
        <View style={styles.headerSection}>
          <Text style={styles.title}>选择您的套餐</Text>
          <Text style={styles.subtitle}>解锁更多面试时长，提升求职成功率</Text>
        </View>

        {/* Current Balance - Simplified */}
        {(userInfo.total_remaining_duration || 0) > 0 && (
          <View style={styles.currentBalanceCard}>
            <View style={styles.balanceRow}>
              <Icon name="access-time" size={20} color={theme.colors.primary} />
              <Text style={styles.balanceText}>
                剩余时长: {formatDuration(userInfo.total_remaining_duration || 0)}
              </Text>
            </View>
          </View>
        )}
        
        {/* Package Cards */}
        <View style={styles.packagesGrid}>
          {products.map((product, index) => {
            const badge = getPackageBadge(product.name);
            const isRecommended = badge === '推荐';
            
            return (
              <TouchableOpacity 
                key={product.id} 
                style={[
                  styles.packageCard,
                  isRecommended && styles.recommendedCard
                ]}
                onPress={() => handleSelectProduct(product)}
                activeOpacity={0.8}
              >
                {badge && (
                  <View style={[styles.badge, isRecommended && styles.recommendedBadge]}>
                    <Text style={styles.badgeText}>{badge}</Text>
                  </View>
                )}
                
                <View style={styles.packageIcon}>
                  <Text style={styles.iconEmoji}>{getPackageIcon(product.name)}</Text>
                </View>
                
                <Text style={styles.packageName}>{product.name}</Text>
                <Text style={styles.packageDuration}>{formatDuration(product.duration)}</Text>
                
                <Text style={styles.priceText}>
                  ¥{product.price}
                </Text>
                
                <View style={[styles.purchaseButton, isRecommended && styles.recommendedButton]}>
                  <Text style={[styles.buttonText, isRecommended && styles.recommendedButtonText]}>
                    立即购买
                  </Text>
                </View>
              </TouchableOpacity>
            );
          })}
        </View>

        {/* Features Section */}
        <View style={styles.featuresSection}>
          <Text style={styles.featuresTitle}>套餐特权</Text>
          <View style={styles.featuresList}>
            <View style={styles.featureItem}>
              <Icon name="check-circle" size={18} color={theme.colors.success} />
              <Text style={styles.featureText}>时长永久有效</Text>
            </View>
            <View style={styles.featureItem}>
              <Icon name="check-circle" size={18} color={theme.colors.success} />
              <Text style={styles.featureText}>支持多种支付方式</Text>
            </View>
            <View style={styles.featureItem}>
              <Icon name="check-circle" size={18} color={theme.colors.success} />
              <Text style={styles.featureText}>24小时客服支持</Text>
            </View>
          </View>
        </View>
      </ScrollView>

      {/* 支付方式选择模态框 */}
      <Modal
        visible={showPaymentModal}
        transparent
        animationType="none"
        onRequestClose={() => setShowPaymentModal(false)}
      >
        <View style={styles.modalOverlay}>
          <Animated.View 
            style={[
              styles.modalContent,
              {
                transform: [
                  {
                    translateY: modalAnimation.interpolate({
                      inputRange: [0, 1],
                      outputRange: [300, 0],
                    }),
                  },
                ],
                opacity: modalAnimation,
              },
            ]}
          >
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>选择支付方式</Text>
              <TouchableOpacity 
                onPress={() => {
                  setShowPaymentModal(false);
                }}
                style={styles.closeButton}
              >
                <Icon name="close" size={24} color="#666" />
              </TouchableOpacity>
            </View>

            {selectedProduct && (
              <View style={styles.productInfo}>
                <View style={styles.productHeader}>
                  <Text style={styles.productName}>{selectedProduct.name}</Text>
                  <View style={styles.priceContainer}>
                    <Text style={styles.productPrice}>¥{selectedProduct.price}</Text>
                    <Text style={styles.originalPrice}>¥{(selectedProduct.price * 1.2).toFixed(2)}</Text>
                  </View>
                </View>
                <View style={styles.productDetails}>
                  <View style={styles.detailItem}>
                    <Icon name="schedule" size={16} color="#666" />
                    <Text style={styles.detailText}>
                      时长：{Math.floor(selectedProduct.duration / 60)}分钟
                    </Text>
                  </View>
                  <View style={styles.detailItem}>
                    <Icon name="local-offer" size={16} color="#FF6B35" />
                    <Text style={styles.detailText}>限时优惠</Text>
                  </View>
                </View>
              </View>
            )}

            <View style={styles.paymentMethods}>
              <Text style={styles.sectionTitle}>支付方式</Text>
              {paymentMethods.filter(method => method.enabled).map((method) => (
                <TouchableOpacity
                  key={method.type}
                  style={styles.paymentMethod}
                  onPress={() => handlePayment(method.type)}
                  disabled={paymentLoading}
                >
                  <View style={styles.paymentMethodIcon}>
                    {renderPaymentIcon(method.type)}
                  </View>
                  <View style={styles.paymentMethodInfo}>
                    <Text style={styles.paymentMethodName}>{method.name}</Text>
                    {method.description ? <Text style={styles.paymentMethodDesc}>{method.description}</Text> : null}
                  </View>
                  <View style={styles.paymentMethodAction}>
                    <Icon name="chevron-right" size={24} color="#ccc" />
                  </View>
                </TouchableOpacity>
              ))}
            </View>

            <View style={styles.securityInfo}>
              <Icon name="security" size={16} color="#4CAF50" />
              <Text style={styles.securityText}>支付安全由第三方平台保障</Text>
            </View>
          </Animated.View>
        </View>
      </Modal>

      <PaymentSuccessModal
        visible={showSuccessModal}
        product={successProduct}
        onContinueShopping={() => {
          setShowSuccessModal(false);
          setSuccessProduct(null);
          // 可以选择留在当前页面或返回
        }}
        onGoToProfile={() => {
          setShowSuccessModal(false);
          setSuccessProduct(null);
          navigation.navigate('Main', { screen: 'Profile' });
        }}
        onStartInterview={() => {
          setShowSuccessModal(false);
          setSuccessProduct(null);
          navigation.navigate('Main', { screen: 'Home' });
        }}
        onViewOrders={() => {
          setShowSuccessModal(false);
          setSuccessProduct(null);
          navigation.navigate('Order');
        }}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  scrollContent: {
    paddingBottom: theme.spacing.xl,
  },
  centerContent: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerSection: {
    paddingHorizontal: theme.spacing.lg,
    paddingTop: theme.spacing.xl,
    paddingBottom: theme.spacing.lg,
    alignItems: 'center',
  },
  title: {
    fontSize: theme.fontSize['3xl'],
    fontWeight: theme.fontWeight.bold,
    color: theme.colors.textPrimary,
    marginBottom: theme.spacing.sm,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: theme.fontSize.base,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    lineHeight: 24,
  },
  currentBalanceCard: {
    backgroundColor: theme.colors.surface,
    marginHorizontal: theme.spacing.lg,
    marginBottom: theme.spacing.lg,
    padding: theme.spacing.md,
    borderRadius: theme.borderRadius.md,
    ...theme.shadows.sm,
  },
  balanceRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  balanceText: {
    fontSize: theme.fontSize.base,
    color: theme.colors.textPrimary,
    fontWeight: theme.fontWeight.medium,
    marginLeft: theme.spacing.sm,
  },
  packagesGrid: {
    paddingHorizontal: theme.spacing.lg,
    gap: theme.spacing.md,
  },
  packageCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.xl,
    alignItems: 'center',
    position: 'relative',
    borderWidth: 2,
    borderColor: 'transparent',
    ...theme.shadows.md,
  },
  recommendedCard: {
    borderColor: theme.colors.primary,
    backgroundColor: '#fafbff',
    transform: [{ scale: 1.02 }],
  },
  badge: {
    position: 'absolute',
    top: -8,
    right: theme.spacing.lg,
    backgroundColor: theme.colors.warning,
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.xs,
    borderRadius: theme.borderRadius.full,
    zIndex: 1,
  },
  recommendedBadge: {
    backgroundColor: theme.colors.primary,
  },
  badgeText: {
    color: theme.colors.textInverse,
    fontSize: theme.fontSize.xs,
    fontWeight: theme.fontWeight.bold,
  },
  packageIcon: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: theme.colors.surfaceSecondary,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: theme.spacing.md,
  },
  iconEmoji: {
    fontSize: 28,
  },
  packageName: {
    fontSize: theme.fontSize.xl,
    fontWeight: theme.fontWeight.bold,
    color: theme.colors.textPrimary,
    marginBottom: theme.spacing.xs,
    textAlign: 'center',
  },
  packageDuration: {
    fontSize: theme.fontSize.base,
    color: theme.colors.primary,
    fontWeight: theme.fontWeight.semibold,
    marginBottom: theme.spacing.lg,
  },
  priceText: {
    fontSize: theme.fontSize['4xl'],
    fontWeight: theme.fontWeight.bold,
    color: theme.colors.error,
    textAlign: 'center',
    marginBottom: theme.spacing.lg,
    lineHeight: theme.fontSize['4xl'] * 1.1,
  },
  purchaseButton: {
    backgroundColor: theme.colors.primary,
    paddingHorizontal: theme.spacing.xl,
    paddingVertical: theme.spacing.md,
    borderRadius: theme.borderRadius.full,
    minWidth: 140,
    ...theme.shadows.sm,
  },
  recommendedButton: {
    backgroundColor: theme.colors.primary,
  },
  buttonText: {
    color: theme.colors.textInverse,
    fontSize: theme.fontSize.base,
    fontWeight: theme.fontWeight.semibold,
    textAlign: 'center',
  },
  recommendedButtonText: {
    color: theme.colors.textInverse,
  },
  featuresSection: {
    marginTop: theme.spacing.xl,
    paddingHorizontal: theme.spacing.lg,
  },
  featuresTitle: {
    fontSize: theme.fontSize.lg,
    fontWeight: theme.fontWeight.bold,
    color: theme.colors.textPrimary,
    marginBottom: theme.spacing.md,
    textAlign: 'center',
  },
  featuresList: {
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.md,
    padding: theme.spacing.lg,
    ...theme.shadows.sm,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.md,
  },
  featureText: {
    fontSize: theme.fontSize.base,
    color: theme.colors.textSecondary,
    marginLeft: theme.spacing.md,
    flex: 1,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  // 模态框样式
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: 'white',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 20,
    maxHeight: '80%',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
    color: '#333',
  },
  orderSummary: {
    backgroundColor: '#f8f9fa',
    padding: 15,
    borderRadius: 10,
    marginBottom: 20,
  },
  orderText: {
    fontSize: 14,
    color: '#666',
    marginBottom: 5,
  },
  orderPrice: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FF6B35',
  },
  paymentMethodsContainer: {
    gap: 10,
    marginBottom: 20,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  closeButton: {
    padding: 8,
  },
  productInfo: {
    backgroundColor: '#f8f9fa',
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
  },
  productName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  productPrice: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FF6B35',
  },
  paymentMethods: {
    marginBottom: 20,
  },
  paymentMethod: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  paymentMethodIcon: {
    marginRight: 15,
    width: 40,
    alignItems: 'center',
  },
  paymentMethodInfo: {
    flex: 1,
  },
  paymentMethodName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
  },
  paymentMethodDesc: {
    fontSize: 12,
    color: '#999',
    marginTop: 2,
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 15,
  },
  cancelButton: {
    backgroundColor: '#f0f0f0',
    padding: 15,
    borderRadius: 10,
    alignItems: 'center',
  },
  cancelButtonText: {
    fontSize: 16,
    color: '#666',
    fontWeight: '500',
  },
  // 支付图标样式
  paymentIcon: {
    width: 28,
    height: 28,
    resizeMode: 'contain',
  },
  
  // 新增样式
  productHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  priceContainer: {
    alignItems: 'flex-end',
  },
  originalPrice: {
    fontSize: 12,
    color: '#999',
    textDecorationLine: 'line-through',
    marginTop: 2,
  },
  productDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  detailText: {
    fontSize: 12,
    color: '#666',
    marginLeft: 4,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 16,
  },
  paymentMethodWrapper: {
    marginBottom: 12,
  },
  paymentMethodAction: {
    alignItems: 'flex-end',
  },
  paymentMethodPrice: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  securityInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 20,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#F0F0F0',
  },
  securityText: {
    fontSize: 12,
    color: '#4CAF50',
    marginLeft: 6,
  },
});

export default PaymentScreen;
