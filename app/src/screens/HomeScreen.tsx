import React, { useEffect, useState, useRef } from 'react';
import { View, Text, StyleSheet, Alert, ScrollView, Modal } from 'react-native';
import { useSelector, useDispatch } from 'react-redux';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { RootState, AppDispatch } from '../store';
import { fetchEphemeralToken, refreshUserInfo, setSelectedMode } from '../store/userSlice';
import GradientBackground from '../components/GradientBackground';
import Card from '../components/Card';
import Button from '../components/Button';
import ModeSelector from '../components/ModeSelector';
import { theme } from '../styles/theme';

const HomeScreen = ({ navigation }: any) => {
  const dispatch = useDispatch<AppDispatch>();
  const { isLoggedIn, token, userInfo, selectedMode } = useSelector((state: RootState) => state.user);
  const [isStartingInterview, setIsStartingInterview] = useState(false); // 防止重复点击
  const [showModeSelector, setShowModeSelector] = useState(false); // 控制职业选择模态框
  const lastRequestTime = useRef<number>(0); // 记录上次请求时间

  // App启动时刷新用户信息
  useEffect(() => {
    if (isLoggedIn && token) {
      console.log('[DEBUG] HomeScreen - App启动，刷新用户信息');
      dispatch(refreshUserInfo(token));
    }
  }, [isLoggedIn, token, dispatch]);

  // 监听navigation focus事件，从面试页面返回时刷新用户信息
  useEffect(() => {
    const unsubscribe = navigation.addListener('focus', () => {
      if (isLoggedIn && token) {
        console.log('[DEBUG] HomeScreen - 页面获得焦点，刷新用户信息');
        dispatch(refreshUserInfo(token));
      }
    });

    return unsubscribe;
  }, [navigation, isLoggedIn, token, dispatch]);

  // 获取面试职业的显示名称
  const getProfessionDisplayName = (mode: string) => {
    const professionMap: { [key: string]: string } = {
      'technical_interview': '技术岗',
      'hr_interview': '人力资源',
      'sales_interview': '销售岗',
      'marketing_interview': '市场营销',
      'finance_interview': '金融岗',
      'legal_interview': '法律岗',
      'medical_interview': '医疗岗',
      'education_interview': '教育岗',
      'architecture_interview': '建筑设计',
      'psychology_interview': '心理咨询',
      'leisure': '随便聊聊',
    };
    return professionMap[mode] || '技术岗';
  };

  // 处理面试职业选择
  const handleProfessionSelect = (mode: string) => {
    dispatch(setSelectedMode(mode));
    setShowModeSelector(false);
  };

  const handleStartInterview = async () => {
    const now = Date.now();
    
    // 防止重复点击 - 增强版
    if (isStartingInterview) {
      console.log('HomeScreen: 正在处理中，忽略重复点击');
      return;
    }
    
    // 防止短时间内重复请求（2秒内）
    if (now - lastRequestTime.current < 2000) {
      console.log('HomeScreen: 请求过于频繁，忽略重复请求');
      return;
    }
    
    lastRequestTime.current = now;

    if (!isLoggedIn) {
      navigation.navigate('Login');
      return;
    }

    // 检查是否有足够的时长（至少需要1分钟才能开始面试）
    const totalRemainingTime = userInfo.total_remaining_duration || 0;
    
    if (totalRemainingTime < 60) {
      Alert.alert('时长不足', '您的可用时长不足1分钟，无法开始面试，请购买套餐后继续使用', [
        { text: '取消' },
        { text: '去购买', onPress: () => navigation.navigate('Payment') }
      ]);
      return;
    }

    setIsStartingInterview(true); // 设置加载状态
    console.log('HomeScreen: 开始获取临时令牌');

    try {
      // 添加延迟防止重复请求
      await new Promise(resolve => setTimeout(resolve, 100));
      
      const result = await dispatch(fetchEphemeralToken({ token })).unwrap();
      console.log('临时令牌获取成功，剩余时长:', result.remaining_minutes, '分钟');
      
      // 确保导航前状态已更新
      await new Promise(resolve => setTimeout(resolve, 50));
      navigation.navigate('Interview', { domain: getProfessionDisplayName(selectedMode), mode: selectedMode });
    } catch (error: any) {
      console.error('获取临时令牌失败:', error);
      
      if (error.code === 'INSUFFICIENT_TIME') {
        Alert.alert('时长不足', '您的可用时长不足1分钟，无法开始面试，请购买套餐后继续使用', [
          { text: '取消' },
          { text: '去购买', onPress: () => navigation.navigate('Payment') }
        ]);
      } else {
        // 改善错误提示，包含网络问题的处理
        let errorMessage = error.message || '获取临时令牌失败，请重试';
        if (errorMessage.includes('timeout') || errorMessage.includes('network')) {
          errorMessage = '网络连接超时，请检查网络后重试';
        }
        Alert.alert('连接失败', errorMessage);
      }
    } finally {
      setIsStartingInterview(false); // 重置加载状态
    }
  };

  return (
    <GradientBackground colors={theme.colors.gradientPrimary}>
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        {/* Header Section */}
        <View style={styles.header}>
          <View style={styles.logoContainer}>
            <Icon name="school" size={48} color={theme.colors.textInverse} />
          </View>
          <Text style={styles.title}>面试助手</Text>
          <Text style={styles.subtitle}>AI实时面试辅助应用</Text>
        </View>

        {/* Main Action Card */}
        <Card style={styles.mainCard} shadow="lg">
          <View style={styles.cardHeader}>
            <View style={styles.cardHeaderLeft}>
              <Icon name="support-agent" size={32} color={theme.colors.primary} />
              <View style={styles.cardTitleContainer}>
                <Text style={styles.cardTitle}>实时面试助手</Text>
                <View style={styles.featureBadge}>
                  <Icon name="flash-on" size={12} color={theme.colors.warning} />
                  <Text style={styles.badgeText}>实时辅助</Text>
                </View>
              </View>
            </View>
          </View>
          <Text style={styles.cardDescription}>
            在真实面试过程中，AI助手实时为您提供回答建议和指导，帮助您更好地应对面试官的问题
          </Text>
          
          {/* 功能特点 */}
          <View style={styles.featureList}>
            <View style={styles.featureItem}>
              <Icon name="mic" size={16} color={theme.colors.success} />
              <Text style={styles.featureText}>实时语音识别</Text>
            </View>
            <View style={styles.featureItem}>
              <Icon name="lightbulb" size={16} color={theme.colors.warning} />
              <Text style={styles.featureText}>智能回答建议</Text>
            </View>
            <View style={styles.featureItem}>
              <Icon name="speed" size={16} color={theme.colors.primary} />
              <Text style={styles.featureText}>快速响应</Text>
            </View>
          </View>
          
          {/* 面试职业选择按钮 */}
          <Button
            title={`面试职业：${getProfessionDisplayName(selectedMode)}`}
            onPress={() => setShowModeSelector(true)}
            size="md"
            variant="secondary"
            style={[styles.startButton, { marginBottom: theme.spacing.md }]}
          />
          
          <Button
            title={isStartingInterview ? "准备中..." : "开始实时辅助"}
            onPress={handleStartInterview}
            size="lg"
            disabled={isStartingInterview}
            style={styles.startButton}
          />
        </Card>

        {/* User Info Card */}
        {isLoggedIn && (
          <Card style={styles.userInfoCard}>
            <View style={styles.infoHeader}>
              <Icon name="access-time" size={24} color={theme.colors.secondary} />
              <Text style={styles.infoTitle}>剩余时长</Text>
            </View>
            <View style={styles.timeInfo}>
              <View style={styles.timeItem}>
                <Text style={styles.timeValue}>
                  {Math.floor((userInfo.total_remaining_duration || 0) / 60)}
                </Text>
                <Text style={styles.timeLabel}>总计(分钟)</Text>
              </View>
              {(userInfo.trial_remaining_duration || 0) > 0 && (
                <View style={styles.timeItem}>
                  <Text style={styles.timeValue}>
                    {Math.floor((userInfo.trial_remaining_duration || 0) / 60)}
                  </Text>
                  <Text style={styles.timeLabel}>试用(分钟)</Text>
                </View>
              )}
            </View>
          </Card>
        )}

        {/* 功能对比说明 */}
        <Card style={styles.comparisonCard}>
          <View style={styles.comparisonHeader}>
            <Icon name="compare" size={24} color={theme.colors.primary} />
            <Text style={styles.comparisonTitle}>两种面试模式</Text>
          </View>
          
          <View style={styles.comparisonContent}>
            <View style={styles.comparisonItem}>
              <View style={styles.comparisonItemHeader}>
                <Icon name="support-agent" size={20} color={theme.colors.primary} />
                <Text style={styles.comparisonItemTitle}>实时助手（当前）</Text>
              </View>
              <Text style={styles.comparisonItemDesc}>真实面试时使用，AI实时提供回答建议</Text>
            </View>
            
            <View style={styles.divider} />
            
            <View style={styles.comparisonItem}>
              <View style={styles.comparisonItemHeader}>
                <Icon name="psychology" size={20} color={theme.colors.warning} />
                <Text style={styles.comparisonItemTitle}>模拟面试</Text>
              </View>
              <Text style={styles.comparisonItemDesc}>AI扮演面试官，完整练习+专业评分</Text>
              <Button
                title="去体验"
                onPress={() => navigation.navigate('MockInterview')}
                size="sm"
                variant="secondary"
                style={styles.tryButton}
              />
            </View>
          </View>
        </Card>

        {/* 面试职业选择模态框 */}
        <Modal
          visible={showModeSelector}
          animationType="slide"
          presentationStyle="pageSheet"
          onRequestClose={() => setShowModeSelector(false)}
        >
          <ModeSelector 
            onSelectMode={handleProfessionSelect} 
            onClose={() => setShowModeSelector(false)}
          />
        </Modal>

      </ScrollView>
    </GradientBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: theme.spacing.md,
  },
  header: {
    alignItems: 'center',
    paddingTop: theme.spacing.xxl,
    paddingBottom: theme.spacing.xl,
  },
  logoContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: theme.spacing.md,
  },
  title: {
    fontSize: theme.fontSize['3xl'],
    fontWeight: theme.fontWeight.bold as 'bold',
    textAlign: 'center',
    marginBottom: theme.spacing.sm,
    color: theme.colors.textInverse,
  },
  subtitle: {
    fontSize: theme.fontSize.lg,
    textAlign: 'center',
    color: 'rgba(255, 255, 255, 0.8)',
  },
  mainCard: {
    marginBottom: theme.spacing.lg,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: theme.spacing.md,
  },
  cardHeaderLeft: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    flex: 1,
  },
  cardTitleContainer: {
    marginLeft: theme.spacing.sm,
    flex: 1,
  },
  cardTitle: {
    fontSize: theme.fontSize.xl,
    fontWeight: theme.fontWeight.bold as 'bold',
    color: theme.colors.textPrimary,
    marginBottom: theme.spacing.xs,
  },
  featureBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(245, 166, 35, 0.1)',
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.xs,
    borderRadius: theme.borderRadius.full,
    alignSelf: 'flex-start',
  },
  badgeText: {
    fontSize: theme.fontSize.xs,
    fontWeight: theme.fontWeight.medium as '500',
    color: theme.colors.warning,
    marginLeft: theme.spacing.xs,
  },
  featureList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: theme.spacing.lg,
    gap: theme.spacing.md,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.surfaceVariant,
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.xs,
    borderRadius: theme.borderRadius.md,
  },
  featureText: {
    fontSize: theme.fontSize.sm,
    color: theme.colors.textSecondary,
    marginLeft: theme.spacing.xs,
    fontWeight: theme.fontWeight.medium as '500',
  },
  cardDescription: {
    fontSize: theme.fontSize.base,
    color: theme.colors.textSecondary,
    lineHeight: 24,
    marginBottom: theme.spacing.lg,
  },
  startButton: {
    marginTop: theme.spacing.sm,
  },
  userInfoCard: {
    marginBottom: theme.spacing.lg,
  },
  infoHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.md,
  },
  infoTitle: {
    fontSize: theme.fontSize.lg,
    fontWeight: theme.fontWeight.semibold as '600',
    color: theme.colors.textPrimary,
    marginLeft: theme.spacing.sm,
  },
  timeInfo: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  timeItem: {
    alignItems: 'center',
  },
  timeValue: {
    fontSize: theme.fontSize['2xl'],
    fontWeight: theme.fontWeight.bold as 'bold',
    color: theme.colors.primary,
  },
  timeLabel: {
    fontSize: theme.fontSize.sm,
    color: theme.colors.textSecondary,
    marginTop: theme.spacing.xs,
  },
  comparisonCard: {
    marginBottom: theme.spacing.lg,
    backgroundColor: theme.colors.surface,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  comparisonHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.lg,
  },
  comparisonTitle: {
    fontSize: theme.fontSize.lg,
    fontWeight: theme.fontWeight.semibold as '600',
    color: theme.colors.textPrimary,
    marginLeft: theme.spacing.sm,
  },
  comparisonContent: {
    gap: theme.spacing.md,
  },
  comparisonItem: {
    padding: theme.spacing.md,
    backgroundColor: theme.colors.surfaceVariant,
    borderRadius: theme.borderRadius.md,
  },
  comparisonItemHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.sm,
  },
  comparisonItemTitle: {
    fontSize: theme.fontSize.base,
    fontWeight: theme.fontWeight.semibold as '600',
    color: theme.colors.textPrimary,
    marginLeft: theme.spacing.sm,
  },
  comparisonItemDesc: {
    fontSize: theme.fontSize.sm,
    color: theme.colors.textSecondary,
    lineHeight: 20,
    marginBottom: theme.spacing.sm,
  },
  divider: {
    height: 1,
    backgroundColor: theme.colors.border,
    marginVertical: theme.spacing.xs,
  },
  tryButton: {
    backgroundColor: theme.colors.warning,
    alignSelf: 'flex-start',
    paddingHorizontal: theme.spacing.lg,
  },

});

export default HomeScreen;
