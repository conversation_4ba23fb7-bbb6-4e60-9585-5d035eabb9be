import React from 'react';
import { View, Text, StyleSheet, ScrollView, Dimensions } from 'react-native';
import { useRoute } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import GradientBackground from '../components/GradientBackground';
import Card from '../components/Card';
import Button from '../components/Button';
import { theme } from '../styles/theme';

const { width } = Dimensions.get('window');

interface ScoreData {
  overall_score: number;
  dimensions: {
    communication: number;
    professional_knowledge: number;
    problem_solving: number;
    attitude_confidence: number;
  };
  feedback: string;
  suggestions: string[];
}

const InterviewScoreScreen = ({ navigation }: any) => {
  const route = useRoute();
  const { scoreData } = route.params as { scoreData: ScoreData };

  // 获取评分等级和颜色
  const getScoreLevel = (score: number) => {
    if (score >= 90) return { level: '优秀', color: '#10b981' };
    if (score >= 80) return { level: '良好', color: '#3b82f6' };
    if (score >= 70) return { level: '中等', color: '#f59e0b' };
    if (score >= 60) return { level: '及格', color: '#ef4444' };
    return { level: '需改进', color: '#dc2626' };
  };

  const scoreLevel = getScoreLevel(scoreData.overall_score);

  // 维度名称映射
  const dimensionNames = {
    communication: '沟通表达',
    professional_knowledge: '专业知识',
    problem_solving: '问题解决',
    attitude_confidence: '态度自信'
  };

  // 渲染分数圆环（使用简化的圆形进度条）
  const renderScoreCircle = (score: number, size: number = 120) => {
    return (
      <View style={[styles.scoreCircleContainer, { width: size, height: size }]}>
        {/* 背景圆环 */}
        <View style={[
          styles.scoreCircleBackground, 
          { 
            width: size, 
            height: size, 
            borderRadius: size / 2,
            borderWidth: 8,
            borderColor: '#e5e7eb'
          }
        ]} />
        
        {/* 进度圆环 - 使用简化的视觉效果 */}
        <View style={[
          styles.scoreCircleProgress, 
          { 
            width: size - 16, 
            height: size - 16, 
            borderRadius: (size - 16) / 2,
            borderWidth: 8,
            borderColor: scoreLevel.color,
            borderTopColor: score >= 75 ? scoreLevel.color : '#e5e7eb',
            borderRightColor: score >= 50 ? scoreLevel.color : '#e5e7eb',
            borderBottomColor: score >= 25 ? scoreLevel.color : '#e5e7eb',
            borderLeftColor: '#e5e7eb',
          }
        ]} />
        
        <View style={styles.scoreTextContainer}>
          <Text style={[styles.scoreText, { color: scoreLevel.color }]}>{score}</Text>
          <Text style={styles.scoreLabel}>分</Text>
        </View>
      </View>
    );
  };

  // 渲染维度条形图
  const renderDimensionBar = (dimension: keyof typeof dimensionNames, score: number) => {
    const percentage = (score / 25) * 100;
    
    return (
      <View key={dimension} style={styles.dimensionItem}>
        <View style={styles.dimensionHeader}>
          <Text style={styles.dimensionName}>{dimensionNames[dimension]}</Text>
          <Text style={styles.dimensionScore}>{score}/25</Text>
        </View>
        <View style={styles.progressBarContainer}>
          <View style={styles.progressBarBackground}>
            <View 
              style={[
                styles.progressBarFill, 
                { 
                  width: `${percentage}%`,
                  backgroundColor: score >= 20 ? '#10b981' : score >= 15 ? '#f59e0b' : '#ef4444'
                }
              ]} 
            />
          </View>
        </View>
      </View>
    );
  };

  return (
    <GradientBackground>
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        {/* 页面标题 */}
        <View style={styles.header}>
          <Icon name="assessment" size={32} color={theme.colors.primary} />
          <Text style={styles.headerTitle}>面试评分报告</Text>
          <Text style={styles.headerSubtitle}>AI智能评估您的面试表现</Text>
        </View>

        {/* 总分卡片 */}
        <Card style={styles.card}>
          <View style={styles.overallScoreContainer}>
            <Text style={styles.cardTitle}>总体评分</Text>
            <View style={styles.scoreDisplayContainer}>
              {renderScoreCircle(scoreData.overall_score, 140)}
              <View style={styles.scoreLevelContainer}>
                <Text style={[styles.scoreLevel, { color: scoreLevel.color }]}>
                  {scoreLevel.level}
                </Text>
                <Text style={styles.scoreLevelDesc}>
                  {scoreData.overall_score >= 80 ? '表现优异，继续保持！' : 
                   scoreData.overall_score >= 70 ? '表现良好，还有提升空间' : 
                   '需要加强练习和准备'}
                </Text>
              </View>
            </View>
          </View>
        </Card>

        {/* 维度评分卡片 */}
        <Card style={styles.card}>
          <View style={styles.sectionHeader}>
            <Icon name="bar-chart" size={20} color={theme.colors.primary} />
            <Text style={styles.sectionTitle}>各维度评分</Text>
          </View>
          <View style={styles.dimensionsContainer}>
            {Object.entries(scoreData.dimensions).map(([dimension, score]) =>
              renderDimensionBar(dimension as keyof typeof dimensionNames, score)
            )}
          </View>
        </Card>

        {/* 详细评价卡片 */}
        <Card style={styles.card}>
          <View style={styles.sectionHeader}>
            <Icon name="feedback" size={20} color={theme.colors.primary} />
            <Text style={styles.sectionTitle}>详细评价</Text>
          </View>
          <Text style={styles.feedbackText}>{scoreData.feedback}</Text>
        </Card>

        {/* 改进建议卡片 */}
        <Card style={styles.card}>
          <View style={styles.sectionHeader}>
            <Icon name="lightbulb" size={20} color={theme.colors.warning} />
            <Text style={styles.sectionTitle}>改进建议</Text>
          </View>
          <View style={styles.suggestionsContainer}>
            {scoreData.suggestions.map((suggestion, index) => (
              <View key={index} style={styles.suggestionItem}>
                <View style={styles.suggestionBullet}>
                  <Text style={styles.suggestionNumber}>{index + 1}</Text>
                </View>
                <Text style={styles.suggestionText}>{suggestion}</Text>
              </View>
            ))}
          </View>
        </Card>

        {/* 操作按钮 */}
        <View style={styles.actionButtons}>
          <Button
            title="重新面试"
            onPress={() => {
              navigation.goBack();
              navigation.goBack(); // 返回到模拟面试选择页面
            }}
            variant="secondary"
            style={styles.actionButton}
          />
          <Button
            title="返回首页"
            onPress={() => navigation.navigate('Home')}
            style={styles.actionButton}
          />
        </View>
      </ScrollView>
    </GradientBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: theme.spacing.md,
  },
  header: {
    alignItems: 'center',
    paddingVertical: theme.spacing.xl,
  },
  headerTitle: {
    fontSize: theme.fontSize['2xl'],
    fontWeight: theme.fontWeight.bold,
    color: theme.colors.textPrimary,
    marginTop: theme.spacing.sm,
  },
  headerSubtitle: {
    fontSize: theme.fontSize.sm,
    color: theme.colors.textSecondary,
    marginTop: theme.spacing.xs,
    textAlign: 'center',
  },
  card: {
    marginBottom: theme.spacing.md,
  },
  cardTitle: {
    fontSize: theme.fontSize.lg,
    fontWeight: theme.fontWeight.semibold,
    color: theme.colors.textPrimary,
    textAlign: 'center',
    marginBottom: theme.spacing.lg,
  },
  overallScoreContainer: {
    alignItems: 'center',
  },
  scoreDisplayContainer: {
    alignItems: 'center',
  },
  scoreCircleContainer: {
    position: 'relative',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: theme.spacing.md,
  },
  scoreCircleBackground: {
    position: 'absolute',
    backgroundColor: 'transparent',
  },
  scoreCircleProgress: {
    position: 'absolute',
    backgroundColor: 'transparent',
    top: 8,
    left: 8,
  },
  scoreTextContainer: {
    alignItems: 'center',
  },
  scoreText: {
    fontSize: 36,
    fontWeight: theme.fontWeight.bold,
  },
  scoreLabel: {
    fontSize: theme.fontSize.sm,
    color: theme.colors.textSecondary,
  },
  scoreLevelContainer: {
    alignItems: 'center',
  },
  scoreLevel: {
    fontSize: theme.fontSize.xl,
    fontWeight: theme.fontWeight.semibold,
    marginBottom: theme.spacing.xs,
  },
  scoreLevelDesc: {
    fontSize: theme.fontSize.sm,
    color: theme.colors.textSecondary,
    textAlign: 'center',
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.md,
  },
  sectionTitle: {
    fontSize: theme.fontSize.lg,
    fontWeight: theme.fontWeight.semibold,
    color: theme.colors.textPrimary,
    marginLeft: theme.spacing.sm,
  },
  dimensionsContainer: {
    gap: theme.spacing.md,
  },
  dimensionItem: {
    marginBottom: theme.spacing.sm,
  },
  dimensionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.xs,
  },
  dimensionName: {
    fontSize: theme.fontSize.base,
    fontWeight: theme.fontWeight.medium,
    color: theme.colors.textPrimary,
  },
  dimensionScore: {
    fontSize: theme.fontSize.sm,
    fontWeight: theme.fontWeight.semibold,
    color: theme.colors.textSecondary,
  },
  progressBarContainer: {
    width: '100%',
  },
  progressBarBackground: {
    height: 8,
    backgroundColor: theme.colors.borderLight,
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressBarFill: {
    height: '100%',
    borderRadius: 4,
  },
  feedbackText: {
    fontSize: theme.fontSize.base,
    color: theme.colors.textPrimary,
    lineHeight: 24,
  },
  suggestionsContainer: {
    gap: theme.spacing.sm,
  },
  suggestionItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  suggestionBullet: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: theme.colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: theme.spacing.sm,
    marginTop: 2,
  },
  suggestionNumber: {
    fontSize: theme.fontSize.xs,
    fontWeight: theme.fontWeight.bold,
    color: theme.colors.textInverse,
  },
  suggestionText: {
    flex: 1,
    fontSize: theme.fontSize.base,
    color: theme.colors.textPrimary,
    lineHeight: 22,
  },
  actionButtons: {
    flexDirection: 'row',
    gap: theme.spacing.md,
    paddingVertical: theme.spacing.xl,
    paddingBottom: theme.spacing.xxl,
  },
  actionButton: {
    flex: 1,
  },
});

export default InterviewScoreScreen;