import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Dimensions,
  Platform,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { theme } from '../styles/theme';

const { width: screenWidth } = Dimensions.get('window');
const isSmallScreen = screenWidth < 375;
const isTablet = screenWidth > 768;

const AboutScreen = () => {
  const appInfo = {
    name: '面试助手',
    version: '1.0.0',
    description: '智能AI面试练习平台，帮助您提升面试技能',
    features: [
      {
        icon: 'psychology',
        title: 'AI智能面试',
        description: '基于先进的AI技术，提供真实的面试体验',
        color: '#007AFF',
      },
      {
        icon: 'record-voice-over',
        title: '语音交互',
        description: '支持语音对话，模拟真实面试场景',
        color: '#34C759',
      },
      {
        icon: 'analytics',
        title: '智能分析',
        description: '深度分析面试表现，提供专业建议',
        color: '#FF9500',
      },
      {
        icon: 'history',
        title: '历史记录',
        description: '完整记录面试历史，追踪进步轨迹',
        color: '#5856D6',
      },
    ],
    team: [
      {
        role: '产品设计',
        description: '专注用户体验设计',
      },
      {
        role: '技术开发',
        description: 'AI技术与移动端开发',
      },
      {
        role: '内容策划',
        description: '面试题库与场景设计',
      },
    ],
    contact: {
      email: '<EMAIL>',
      website: 'www.interview-assistant.com',
    },
  };

  return (
    <View style={styles.container}>
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* App Info Card */}
        <View style={styles.card}>
          <View style={styles.appHeader}>
            <View style={styles.appIconContainer}>
              <Icon name="psychology" size={48} color="#007AFF" />
            </View>
            <View style={styles.appInfo}>
              <Text style={styles.appName}>{appInfo.name}</Text>
              <Text style={styles.appVersion}>版本 {appInfo.version}</Text>
            </View>
          </View>
          <Text style={styles.appDescription}>{appInfo.description}</Text>
        </View>

        {/* Features Card */}
        <View style={styles.card}>
          <Text style={styles.sectionTitle}>核心功能</Text>
          {appInfo.features.map((feature, index) => (
            <View
              key={index}
              style={[
                styles.featureItem,
                index < appInfo.features.length - 1 && styles.featureItemBorder
              ]}
            >
              <View style={[styles.featureIcon, { backgroundColor: `${feature.color}15` }]}>
                <Icon name={feature.icon} size={24} color={feature.color} />
              </View>
              <View style={styles.featureContent}>
                <Text style={styles.featureTitle}>{feature.title}</Text>
                <Text style={styles.featureDescription}>{feature.description}</Text>
              </View>
            </View>
          ))}
        </View>

        {/* Team Card */}
        <View style={styles.card}>
          <Text style={styles.sectionTitle}>开发团队</Text>
          {appInfo.team.map((member, index) => (
            <View
              key={index}
              style={[
                styles.teamItem,
                index < appInfo.team.length - 1 && styles.teamItemBorder
              ]}
            >
              <Text style={styles.teamRole}>{member.role}</Text>
              <Text style={styles.teamDescription}>{member.description}</Text>
            </View>
          ))}
        </View>

        {/* Contact Card */}
        <View style={styles.card}>
          <Text style={styles.sectionTitle}>联系我们</Text>
          <View style={styles.contactItem}>
            <Icon name="email" size={20} color="#666" />
            <Text style={styles.contactText}>{appInfo.contact.email}</Text>
          </View>
          <View style={styles.contactItem}>
            <Icon name="language" size={20} color="#666" />
            <Text style={styles.contactText}>{appInfo.contact.website}</Text>
          </View>
        </View>

        {/* Copyright */}
        <View style={styles.footer}>
          <Text style={styles.footerText}>
            © 2024 面试助手. 保留所有权利.
          </Text>
          <Text style={styles.footerSubtext}>
            致力于为求职者提供最优质的面试练习体验
          </Text>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    flex: 1,
    paddingHorizontal: isSmallScreen ? theme.spacing.md : theme.spacing.lg,
    paddingBottom: Platform.OS === 'ios' ? theme.spacing.xl : theme.spacing.lg,
  },
  card: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginTop: theme.spacing.lg,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    marginHorizontal: isTablet ? theme.spacing.xl : 0,
  },
  appHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.md,
  },
  appIconContainer: {
    width: 64,
    height: 64,
    borderRadius: 16,
    backgroundColor: '#007AFF15',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: theme.spacing.md,
  },
  appInfo: {
    flex: 1,
  },
  appName: {
    fontSize: isSmallScreen ? 20 : 24,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  appVersion: {
    fontSize: isSmallScreen ? 14 : 16,
    color: '#666',
  },
  appDescription: {
    fontSize: isSmallScreen ? 14 : 16,
    color: '#666',
    lineHeight: isSmallScreen ? 20 : 22,
  },
  sectionTitle: {
    fontSize: isSmallScreen ? 16 : 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: theme.spacing.md,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: theme.spacing.md,
  },
  featureItemBorder: {
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  featureIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: theme.spacing.md,
  },
  featureContent: {
    flex: 1,
  },
  featureTitle: {
    fontSize: isSmallScreen ? 14 : 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  featureDescription: {
    fontSize: isSmallScreen ? 12 : 14,
    color: '#666',
    lineHeight: isSmallScreen ? 16 : 18,
  },
  teamItem: {
    paddingVertical: theme.spacing.sm,
  },
  teamItemBorder: {
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  teamRole: {
    fontSize: isSmallScreen ? 14 : 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  teamDescription: {
    fontSize: isSmallScreen ? 12 : 14,
    color: '#666',
  },
  contactItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: theme.spacing.sm,
  },
  contactText: {
    fontSize: isSmallScreen ? 14 : 16,
    color: '#666',
    marginLeft: theme.spacing.sm,
  },
  footer: {
    paddingVertical: theme.spacing.xl,
    alignItems: 'center',
  },
  footerText: {
    fontSize: isSmallScreen ? 12 : 14,
    color: '#999',
    textAlign: 'center',
    marginBottom: 4,
  },
  footerSubtext: {
    fontSize: isSmallScreen ? 10 : 12,
    color: '#999',
    textAlign: 'center',
  },
});

export default AboutScreen;