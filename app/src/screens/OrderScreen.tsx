import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  RefreshControl,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useSelector } from 'react-redux';
import { RootState } from '../store';
import GradientBackground from '../components/GradientBackground';
import Card from '../components/Card';
import { theme } from '../styles/theme';
import PaymentService, { Order } from '../services/PaymentService';

const OrderScreen = ({ navigation }: any) => {
  const { token, isLoggedIn } = useSelector((state: RootState) => state.user);
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [total, setTotal] = useState(0);

  const loadOrders = useCallback(async (pageNum: number = 1, isRefresh: boolean = false) => {
    try {
      if (!isLoggedIn || !token) {
        Alert.alert('提示', '请先登录');
        navigation.navigate('Login');
        return;
      }

      if (pageNum === 1) {
        setLoading(true);
      }

      const result = await PaymentService.getUserOrders(token, pageNum, 20);
      
      if (isRefresh || pageNum === 1) {
        setOrders(result.orders);
      } else {
        setOrders(prev => [...prev, ...result.orders]);
      }
      
      setTotal(result.total);
      setHasMore(result.orders.length === 20);
      setPage(pageNum);
    } catch (error) {
      console.error('获取订单列表失败:', error);
      Alert.alert('错误', error instanceof Error ? error.message : '获取订单列表失败');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, [isLoggedIn, token, navigation]);

  useEffect(() => {
    // 添加一个小的延迟来等待Redux状态完全恢复
    const timer = setTimeout(() => {
      if (isLoggedIn && token) {
        loadOrders();
      }
    }, 100);

    return () => clearTimeout(timer);
  }, [loadOrders, isLoggedIn, token]);

  const onRefresh = useCallback(() => {
    setRefreshing(true);
    loadOrders(1, true);
  }, [loadOrders]);

  const loadMore = useCallback(() => {
    if (!loading && hasMore) {
      loadOrders(page + 1);
    }
  }, [loading, hasMore, page, loadOrders]);

  const getStatusText = (status: number) => {
    switch (status) {
      case 0:
        return '待支付';
      case 1:
        return '已支付';
      case 2:
        return '已关闭';
      default:
        return '未知';
    }
  };

  const getStatusColor = (status: number) => {
    switch (status) {
      case 0:
        return theme.colors.warning;
      case 1:
        return theme.colors.success;
      case 2:
        return theme.colors.textTertiary;
      default:
        return theme.colors.textSecondary;
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    if (hours > 0) {
      return `${hours}小时${minutes}分钟`;
    }
    return `${minutes}分钟`;
  };

  const renderOrderItem = (order: Order) => (
    <Card key={order.id} style={styles.orderCard} shadow="md">
      <View style={styles.orderHeader}>
        <Text style={styles.orderNo}>订单号: {order.order_no}</Text>
        <View style={[styles.statusBadge, { backgroundColor: getStatusColor(order.status) + '20' }]}>
          <Text style={[styles.statusText, { color: getStatusColor(order.status) }]}>
            {getStatusText(order.status)}
          </Text>
        </View>
      </View>
      
      <View style={styles.orderContent}>
        <View style={styles.productInfo}>
          <Icon name="access-time" size={20} color={theme.colors.primary} />
          <View style={styles.productDetails}>
            <Text style={styles.productName}>
              {order.product?.name || '时长套餐'}
            </Text>
            <Text style={styles.productDesc}>
              {order.product?.duration ? formatDuration(order.product.duration) : ''}
            </Text>
          </View>
        </View>
        
        <View style={styles.orderMeta}>
          <View style={styles.priceContainer}>
            <Text style={styles.priceLabel}>支付金额</Text>
            <Text style={styles.price}>¥{order.amount.toFixed(2)}</Text>
          </View>
          
          <View style={styles.timeContainer}>
            <Text style={styles.timeLabel}>创建时间</Text>
            <Text style={styles.time}>{formatDate(order.created_at)}</Text>
            {order.paid_at && (
              <>
                <Text style={styles.timeLabel}>支付时间</Text>
                <Text style={styles.time}>{formatDate(order.paid_at)}</Text>
              </>
            )}
          </View>
        </View>
        
        {order.pay_type && (
          <View style={styles.payTypeContainer}>
            <Icon 
              name={order.pay_type === 'wechat' ? 'chat' : 'payment'} 
              size={16} 
              color={theme.colors.textSecondary} 
            />
            <Text style={styles.payType}>
              {order.pay_type === 'wechat' ? '微信支付' : '支付宝'}
            </Text>
          </View>
        )}
      </View>
    </Card>
  );

  // 如果用户未登录，显示登录提示
  if (!isLoggedIn || !token) {
    return (
      <GradientBackground colors={theme.colors.gradientSecondary}>
        <View style={styles.notLoggedInContainer}>
          <View style={styles.iconContainer}>
            <Icon name="person" size={48} color={theme.colors.textInverse} />
          </View>
          <Text style={styles.title}>未登录</Text>
          <Text style={styles.subtitle}>请先登录以查看订单记录</Text>
          <TouchableOpacity 
            style={styles.loginButton}
            onPress={() => navigation.navigate('Login')}
          >
            <Text style={styles.loginButtonText}>登录/注册</Text>
          </TouchableOpacity>
        </View>
      </GradientBackground>
    );
  }

  if (loading && orders.length === 0) {
    return (
      <GradientBackground colors={theme.colors.gradientSecondary}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.textInverse} />
          <Text style={styles.loadingText}>加载中...</Text>
        </View>
      </GradientBackground>
    );
  }

  return (
    <GradientBackground colors={theme.colors.gradientSecondary}>
      <ScrollView 
        style={styles.container} 
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        onMomentumScrollEnd={({ nativeEvent }) => {
          const { layoutMeasurement, contentOffset, contentSize } = nativeEvent;
          const isCloseToBottom = layoutMeasurement.height + contentOffset.y >= contentSize.height - 20;
          if (isCloseToBottom) {
            loadMore();
          }
        }}
      >
        {/* Header Section */}
        <View style={styles.header}>
          <View style={styles.iconContainer}>
            <Icon name="receipt" size={48} color={theme.colors.textInverse} />
          </View>
          <Text style={styles.title}>订单记录</Text>
          <Text style={styles.subtitle}>查看您的购买记录</Text>
          {total > 0 && (
            <Text style={styles.totalText}>共 {total} 条订单</Text>
          )}
        </View>

        {/* Orders List */}
        {orders.length > 0 ? (
          <View style={styles.ordersContainer}>
            {orders.map(renderOrderItem)}
            
            {loading && orders.length > 0 && (
              <View style={styles.loadMoreContainer}>
                <ActivityIndicator size="small" color={theme.colors.primary} />
                <Text style={styles.loadMoreText}>加载更多...</Text>
              </View>
            )}
            
            {!hasMore && orders.length > 0 && (
              <Text style={styles.noMoreText}>没有更多订单了</Text>
            )}
          </View>
        ) : (
          /* Empty State */
          <Card style={styles.emptyCard} shadow="lg">
            <View style={styles.emptyContent}>
              <Icon name="receipt-long" size={64} color={theme.colors.textTertiary} />
              <Text style={styles.emptyTitle}>暂无订单记录</Text>
              <Text style={styles.emptyDescription}>
                购买套餐后，订单记录将会显示在这里
              </Text>
              <TouchableOpacity 
                style={styles.goShopButton}
                onPress={() => navigation.navigate('Payment')}
              >
                <Icon name="shopping-cart" size={20} color={theme.colors.primary} />
                <Text style={styles.goShopText}>去购买套餐</Text>
              </TouchableOpacity>
            </View>
          </Card>
        )}
      </ScrollView>
    </GradientBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: theme.spacing.md,
  },
  notLoggedInContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: theme.spacing.lg,
  },
  loginButton: {
    backgroundColor: theme.colors.primary,
    paddingHorizontal: theme.spacing.xl,
    paddingVertical: theme.spacing.md,
    borderRadius: 25,
    marginTop: theme.spacing.lg,
  },
  loginButtonText: {
    color: theme.colors.textInverse,
    fontSize: theme.fontSize.base,
    fontWeight: theme.fontWeight.medium,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: theme.spacing.md,
    fontSize: theme.fontSize.base,
    color: theme.colors.textInverse,
  },
  header: {
    alignItems: 'center',
    paddingTop: theme.spacing.xxl,
    paddingBottom: theme.spacing.xl,
  },
  iconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: theme.spacing.md,
  },
  title: {
    fontSize: theme.fontSize['3xl'],
    fontWeight: theme.fontWeight.bold,
    textAlign: 'center',
    marginBottom: theme.spacing.sm,
    color: theme.colors.textInverse,
  },
  subtitle: {
    fontSize: theme.fontSize.lg,
    textAlign: 'center',
    color: 'rgba(255, 255, 255, 0.8)',
    marginBottom: theme.spacing.sm,
  },
  totalText: {
    fontSize: theme.fontSize.sm,
    color: 'rgba(255, 255, 255, 0.7)',
  },
  ordersContainer: {
    paddingBottom: theme.spacing.xl,
  },
  orderCard: {
    marginBottom: theme.spacing.md,
    padding: theme.spacing.lg,
  },
  orderHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.md,
    paddingBottom: theme.spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  orderNo: {
    fontSize: theme.fontSize.sm,
    color: theme.colors.textSecondary,
    flex: 1,
  },
  statusBadge: {
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.xs,
    borderRadius: 12,
  },
  statusText: {
    fontSize: theme.fontSize.sm,
    fontWeight: theme.fontWeight.medium,
  },
  orderContent: {
    gap: theme.spacing.md,
  },
  productInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: theme.spacing.sm,
  },
  productDetails: {
    flex: 1,
  },
  productName: {
    fontSize: theme.fontSize.base,
    fontWeight: theme.fontWeight.medium,
    color: theme.colors.textPrimary,
    marginBottom: theme.spacing.xs,
  },
  productDesc: {
    fontSize: theme.fontSize.sm,
    color: theme.colors.textSecondary,
  },
  orderMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  priceContainer: {
    alignItems: 'flex-start',
  },
  priceLabel: {
    fontSize: theme.fontSize.sm,
    color: theme.colors.textSecondary,
    marginBottom: theme.spacing.xs,
  },
  price: {
    fontSize: theme.fontSize.lg,
    fontWeight: theme.fontWeight.bold,
    color: theme.colors.primary,
  },
  timeContainer: {
    alignItems: 'flex-end',
  },
  timeLabel: {
    fontSize: theme.fontSize.sm,
    color: theme.colors.textSecondary,
    marginBottom: theme.spacing.xs,
  },
  time: {
    fontSize: theme.fontSize.sm,
    color: theme.colors.textPrimary,
    marginBottom: theme.spacing.xs,
  },
  payTypeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: theme.spacing.xs,
  },
  payType: {
    fontSize: theme.fontSize.sm,
    color: theme.colors.textSecondary,
  },
  loadMoreContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: theme.spacing.md,
    gap: theme.spacing.sm,
  },
  loadMoreText: {
    fontSize: theme.fontSize.sm,
    color: theme.colors.textSecondary,
  },
  noMoreText: {
    textAlign: 'center',
    fontSize: theme.fontSize.sm,
    color: theme.colors.textTertiary,
    paddingVertical: theme.spacing.md,
  },
  emptyCard: {
    marginBottom: theme.spacing.lg,
  },
  emptyContent: {
    alignItems: 'center',
    paddingVertical: theme.spacing.xl,
  },
  emptyTitle: {
    fontSize: theme.fontSize.xl,
    fontWeight: theme.fontWeight.bold,
    color: theme.colors.textPrimary,
    marginTop: theme.spacing.md,
    marginBottom: theme.spacing.sm,
  },
  emptyDescription: {
    fontSize: theme.fontSize.base,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: theme.spacing.lg,
  },
  goShopButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: theme.spacing.sm,
    paddingHorizontal: theme.spacing.lg,
    paddingVertical: theme.spacing.sm,
    backgroundColor: theme.colors.primary + '20',
    borderRadius: 20,
  },
  goShopText: {
    fontSize: theme.fontSize.base,
    color: theme.colors.primary,
    fontWeight: theme.fontWeight.medium,
  },
});

export default OrderScreen;