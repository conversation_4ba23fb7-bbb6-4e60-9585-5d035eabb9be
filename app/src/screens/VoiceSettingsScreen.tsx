import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
  ActivityIndicator
} from 'react-native';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '../store';
import { setUserInfo } from '../store/userSlice';
import { getPreferredVoice } from '../utils/voiceUtils';

interface VoiceOption {
  name: string;
  displayName: string;
  gender: 'male' | 'female';
  description: string;
  recommended?: boolean;
}

// Google Gemini Live API 支持的语音列表
// 基于官方文档和API测试确认的可用语音
const VOICE_OPTIONS: VoiceOption[] = [
  // 女性声音 - 经过验证的可用语音
  { name: 'Aoede', displayName: 'Aoede', gender: 'female', description: 'Natural and clear - 自然清晰', recommended: true },
  { name: '<PERSON><PERSON>', displayName: '<PERSON>ron', gender: 'female', description: 'Warm and friendly - 温暖友好', recommended: true },
  { name: 'Fenrir', displayName: 'Fenrir', gender: 'female', description: 'Professional and confident - 专业自信' },
  
  // 男性声音 - 经过验证的可用语音
  { name: 'Puck', displayName: 'Puck', gender: 'male', description: 'Clear and confident - 清晰自信', recommended: true },
  { name: 'Kore', displayName: 'Kore', gender: 'male', description: 'Professional and steady - 专业稳重' },
];

const VoiceSettingsScreen: React.FC = () => {
  const dispatch = useDispatch();
  const { geminiConfig } = useSelector((state: RootState) => state.user);
  const [selectedVoice, setSelectedVoice] = useState('Aoede');

  // 初始化时获取当前选择的语音
  useEffect(() => {
    const loadCurrentVoice = async () => {
      const currentVoice = await getPreferredVoice();
      setSelectedVoice(currentVoice);
    };
    loadCurrentVoice();
  }, []);
  const [isTestingVoice, setIsTestingVoice] = useState<string | null>(null);

  const handleVoiceSelect = async (voiceName: string) => {
    setSelectedVoice(voiceName);
    
    // 保存到本地存储，下次获取配置时会使用
    try {
      const AsyncStorage = require('@react-native-async-storage/async-storage').default;
      await AsyncStorage.setItem('preferred_voice', voiceName);
      
      Alert.alert(
        '语音已更改',
        `已切换到 ${VOICE_OPTIONS.find(v => v.name === voiceName)?.displayName}，将在下次面试时生效。`,
        [{ text: '确定' }]
      );
    } catch (error) {
      console.error('保存语音设置失败:', error);
      Alert.alert('保存失败', '语音设置保存失败，请重试');
    }
  };

  const testVoice = async (voiceName: string) => {
    setIsTestingVoice(voiceName);
    // 这里可以添加语音试听功能
    setTimeout(() => {
      setIsTestingVoice(null);
      Alert.alert('试听完成', '如果您喜欢这个声音，请点击选择按钮。');
    }, 2000);
  };

  const renderVoiceOption = (voice: VoiceOption) => {
    const isSelected = selectedVoice === voice.name;
    const isTesting = isTestingVoice === voice.name;

    return (
      <View key={voice.name} style={[styles.voiceCard, isSelected && styles.selectedCard]}>
        <View style={styles.voiceHeader}>
          <View style={styles.voiceInfo}>
            <Text style={[styles.voiceName, isSelected && styles.selectedText]}>
              {voice.displayName}
              {voice.recommended && <Text style={styles.recommendedTag}> 推荐</Text>}
            </Text>
            <Text style={[styles.genderTag, voice.gender === 'female' ? styles.femaleTag : styles.maleTag]}>
              {voice.gender === 'female' ? '女性' : '男性'}
            </Text>
          </View>
        </View>
        
        <Text style={styles.voiceDescription}>{voice.description}</Text>
        
        <View style={styles.buttonRow}>
          <TouchableOpacity
            style={[styles.testButton, isTesting && styles.testingButton]}
            onPress={() => testVoice(voice.name)}
            disabled={isTesting}
          >
            {isTesting ? (
              <ActivityIndicator size="small" color="#fff" />
            ) : (
              <Text style={styles.testButtonText}>试听</Text>
            )}
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.selectButton, isSelected && styles.selectedButton]}
            onPress={() => handleVoiceSelect(voice.name)}
            disabled={isSelected}
          >
            <Text style={[styles.selectButtonText, isSelected && styles.selectedButtonText]}>
              {isSelected ? '已选择' : '选择'}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>推荐女性声音</Text>
        {VOICE_OPTIONS.filter(v => v.gender === 'female').map(renderVoiceOption)}
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>男性声音选项</Text>
        {VOICE_OPTIONS.filter(v => v.gender === 'male').map(renderVoiceOption)}
      </View>

      <View style={styles.footer}>
        <Text style={styles.footerText}>
          💡 提示：建议选择女性声音，通常更温和亲切，有助于缓解面试紧张情绪
        </Text>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    padding: 20,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  section: {
    marginTop: 20,
    paddingHorizontal: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 15,
  },
  voiceCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 2,
    borderColor: 'transparent',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  selectedCard: {
    borderColor: '#007AFF',
    backgroundColor: '#f0f8ff',
  },
  voiceHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  voiceInfo: {
    flex: 1,
  },
  voiceName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  selectedText: {
    color: '#007AFF',
  },
  recommendedTag: {
    fontSize: 12,
    color: '#FF6B35',
    fontWeight: 'bold',
  },
  genderTag: {
    fontSize: 12,
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 10,
    overflow: 'hidden',
  },
  femaleTag: {
    backgroundColor: '#FFE4E1',
    color: '#FF69B4',
  },
  maleTag: {
    backgroundColor: '#E6F3FF',
    color: '#4169E1',
  },
  voiceDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 12,
    lineHeight: 20,
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  testButton: {
    backgroundColor: '#34C759',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
    minWidth: 60,
    alignItems: 'center',
  },
  testingButton: {
    backgroundColor: '#999',
  },
  testButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
  },
  selectButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
    minWidth: 60,
    alignItems: 'center',
  },
  selectedButton: {
    backgroundColor: '#34C759',
  },
  selectButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
  },
  selectedButtonText: {
    color: '#fff',
  },
  footer: {
    padding: 20,
    marginTop: 20,
  },
  footerText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    lineHeight: 20,
  },
});

export default VoiceSettingsScreen;
