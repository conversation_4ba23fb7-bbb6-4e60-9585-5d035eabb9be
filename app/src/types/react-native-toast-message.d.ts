// Toast message type definitions
declare module 'react-native-toast-message' {
  export interface ToastShowParams {
    type: 'success' | 'error' | 'info' | 'warning';
    text1: string;
    text2?: string;
    position?: 'top' | 'bottom';
    topOffset?: number;
    bottomOffset?: number;
    visibilityTime?: number;
    autoHide?: boolean;
    swipeable?: boolean;
  }

  export interface ToastConfig {
    success?: (props: any) => React.ReactElement;
    error?: (props: any) => React.ReactElement;
    info?: (props: any) => React.ReactElement;
    warning?: (props: any) => React.ReactElement;
  }

  export default class Toast {
    static show(params: ToastShowParams): void;
    static hide(): void;
  }
}