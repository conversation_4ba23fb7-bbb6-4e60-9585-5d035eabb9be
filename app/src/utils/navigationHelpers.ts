import { NavigationProp } from '@react-navigation/native';

export interface NavigationHelpers {
  goToHome: () => void;
  goToProfile: () => void;
  goToInterview: () => void;
  goToHistory: () => void;
  goBack: () => void;
}

export const createNavigationHelpers = (navigation: NavigationProp<any>): NavigationHelpers => ({
  goToHome: () => navigation.navigate('Home'),
  goToProfile: () => navigation.navigate('Profile'),
  goToInterview: () => navigation.navigate('Interview'),
  goToHistory: () => navigation.navigate('History'),
  goBack: () => navigation.goBack(),
});

// 支付成功后的导航选项
export const getPaymentSuccessNavigationOptions = (navigation: NavigationProp<any>) => ({
  // 立即开始面试
  startInterview: () => {
    navigation.reset({
      index: 0,
      routes: [
        { name: 'Home' },
        { name: 'Interview' }
      ],
    });
  },
  
  // 查看个人资料和余额
  viewProfile: () => {
    navigation.navigate('Profile');
  },
  
  // 继续购买其他套餐
  continueShopping: () => {
    // 留在当前页面，只是关闭模态框
  },
  
  // 返回首页
  goHome: () => {
    navigation.reset({
      index: 0,
      routes: [{ name: 'Home' }],
    });
  },
  
  // 查看购买历史
  viewHistory: () => {
    navigation.navigate('History');
  },
});

// 支付成功后的推荐操作
export const getRecommendedActions = (userBalance: number, isFirstPurchase: boolean) => {
  const actions = [];
  
  if (isFirstPurchase) {
    actions.push({
      id: 'start_interview',
      title: '开始首次面试',
      description: '体验AI面试助手的强大功能',
      icon: 'play-circle',
      priority: 1,
      color: '#4CAF50',
    });
  } else {
    actions.push({
      id: 'start_interview',
      title: '继续练习面试',
      description: '提升您的面试技能',
      icon: 'play-circle',
      priority: 1,
      color: '#4CAF50',
    });
  }
  
  actions.push({
    id: 'view_profile',
    title: '查看账户余额',
    description: `当前余额: ${Math.floor(userBalance / 60)}分钟`,
    icon: 'account-circle',
    priority: 2,
    color: '#2196F3',
  });
  
  if (userBalance < 1800) { // 少于30分钟
    actions.push({
      id: 'buy_more',
      title: '购买更多时长',
      description: '获得更多练习机会',
      icon: 'add-shopping-cart',
      priority: 3,
      color: '#FF9800',
    });
  }
  
  return actions.sort((a, b) => a.priority - b.priority);
};