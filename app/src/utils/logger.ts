/**
 * 可控调试级别的日志工具
 * 支持不同级别的日志输出控制
 */

export enum LogLevel {
  ERROR = 0,
  WARN = 1,
  INFO = 2,
  DEBUG = 3,
}

export interface LogConfig {
  level: LogLevel;
  enableTimestamp: boolean;
  enablePrefix: boolean;
}

class Logger {
  private config: LogConfig = {
    level: LogLevel.ERROR, // 默认只显示ERROR级别
    enableTimestamp: true,
    enablePrefix: true,
  };

  /**
   * 设置日志级别
   * @param level 日志级别
   */
  setLevel(level: LogLevel): void {
    this.config.level = level;
    this.info('Logger', `日志级别已设置为: ${LogLevel[level]}`);
  }

  /**
   * 获取当前日志级别
   */
  getLevel(): LogLevel {
    return this.config.level;
  }

  /**
   * 设置日志配置
   * @param config 日志配置
   */
  setConfig(config: Partial<LogConfig>): void {
    this.config = { ...this.config, ...config };
  }

  /**
   * 格式化日志消息
   * @param level 日志级别
   * @param tag 标签
   * @param message 消息
   * @param optionalParams 可选参数
   */
  private formatMessage(level: LogLevel, tag: string, message: string, ...optionalParams: any[]): [string, any[]] {
    let formattedMessage = message;
    
    if (this.config.enablePrefix) {
      const levelName = LogLevel[level];
      formattedMessage = `[${levelName}] ${tag}: ${message}`;
    }
    
    if (this.config.enableTimestamp) {
      const timestamp = new Date().toLocaleTimeString();
      formattedMessage = `[${timestamp}] ${formattedMessage}`;
    }
    
    return [formattedMessage, optionalParams];
  }

  /**
   * 检查是否应该输出日志
   * @param level 日志级别
   */
  private shouldLog(level: LogLevel): boolean {
    return level <= this.config.level;
  }

  /**
   * ERROR级别日志
   * @param tag 标签
   * @param message 消息
   * @param optionalParams 可选参数
   */
  error(tag: string, message: string, ...optionalParams: any[]): void {
    if (this.shouldLog(LogLevel.ERROR)) {
      const [formattedMessage, params] = this.formatMessage(LogLevel.ERROR, tag, message, ...optionalParams);
      console.error(formattedMessage, ...params);
    }
  }

  /**
   * WARN级别日志
   * @param tag 标签
   * @param message 消息
   * @param optionalParams 可选参数
   */
  warn(tag: string, message: string, ...optionalParams: any[]): void {
    if (this.shouldLog(LogLevel.WARN)) {
      const [formattedMessage, params] = this.formatMessage(LogLevel.WARN, tag, message, ...optionalParams);
      console.warn(formattedMessage, ...params);
    }
  }

  /**
   * INFO级别日志
   * @param tag 标签
   * @param message 消息
   * @param optionalParams 可选参数
   */
  info(tag: string, message: string, ...optionalParams: any[]): void {
    if (this.shouldLog(LogLevel.INFO)) {
      const [formattedMessage, params] = this.formatMessage(LogLevel.INFO, tag, message, ...optionalParams);
      console.log(formattedMessage, ...params);
    }
  }

  /**
   * DEBUG级别日志
   * @param tag 标签
   * @param message 消息
   * @param optionalParams 可选参数
   */
  debug(tag: string, message: string, ...optionalParams: any[]): void {
    if (this.shouldLog(LogLevel.DEBUG)) {
      const [formattedMessage, params] = this.formatMessage(LogLevel.DEBUG, tag, message, ...optionalParams);
      console.log(formattedMessage, ...params);
    }
  }

  /**
   * 音频相关的特殊日志方法
   */
  audioSuccess(tag: string, message: string, ...optionalParams: any[]): void {
    if (this.shouldLog(LogLevel.DEBUG)) {
      const [formattedMessage, params] = this.formatMessage(LogLevel.DEBUG, tag, `✅ ${message}`, ...optionalParams);
      console.log(formattedMessage, ...params);
    }
  }

  audioInfo(tag: string, message: string, ...optionalParams: any[]): void {
    if (this.shouldLog(LogLevel.DEBUG)) {
      const [formattedMessage, params] = this.formatMessage(LogLevel.DEBUG, tag, `🎵 ${message}`, ...optionalParams);
      console.log(formattedMessage, ...params);
    }
  }

  audioError(tag: string, message: string, ...optionalParams: any[]): void {
    if (this.shouldLog(LogLevel.ERROR)) {
      const [formattedMessage, params] = this.formatMessage(LogLevel.ERROR, tag, `❌ ${message}`, ...optionalParams);
      console.error(formattedMessage, ...params);
    }
  }

  /**
   * 获取日志级别的字符串表示
   */
  getLevelString(): string {
    return LogLevel[this.config.level];
  }

  /**
   * 设置为调试模式（显示所有日志）
   */
  enableDebugMode(): void {
    this.setLevel(LogLevel.DEBUG);
  }

  /**
   * 设置为生产模式（只显示错误）
   */
  enableProductionMode(): void {
    this.setLevel(LogLevel.ERROR);
  }
}

// 创建全局日志实例
const logger = new Logger();

// 导出日志实例和枚举
export { logger, Logger };
export default logger;