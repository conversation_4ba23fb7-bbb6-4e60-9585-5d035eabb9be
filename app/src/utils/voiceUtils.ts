import AsyncStorage from '@react-native-async-storage/async-storage';

export interface VoiceInfo {
  name: string;
  displayName: string;
  gender: 'male' | 'female';
  description: string;
  recommended?: boolean;
}

// Google Gemini Live API 支持的语音列表
// 基于官方文档和API测试确认的可用语音
export const AVAILABLE_VOICES: VoiceInfo[] = [
  // 女性声音 - 经过验证的可用语音
  {
    name: 'Aoede',
    displayName: 'Aoede',
    gender: 'female',
    description: 'Natural and clear - 自然清晰的女性声音',
    recommended: true
  },
  {
    name: '<PERSON>ron',
    displayName: 'Charon',
    gender: 'female',
    description: 'Warm and friendly - 温暖友好的女性声音',
    recommended: true
  },
  {
    name: '<PERSON><PERSON><PERSON>',
    displayName: 'Fenrir',
    gender: 'female',
    description: 'Professional and confident - 专业自信的女性声音'
  },
  
  // 男性声音 - 经过验证的可用语音
  {
    name: 'Puck',
    displayName: 'Puck',
    gender: 'male',
    description: 'Clear and confident - 清晰自信的男性声音',
    recommended: true
  },
  {
    name: '<PERSON><PERSON>',
    displayName: 'Kore',
    gender: 'male',
    description: 'Professional and steady - 专业稳重的男性声音'
  }
];

/**
 * 获取用户偏好的语音设置
 */
export const getPreferredVoice = async (): Promise<string> => {
  try {
    const preferredVoice = await AsyncStorage.getItem('preferred_voice');
    // 验证语音是否在支持列表中
    const isSupported = AVAILABLE_VOICES.some(voice => voice.name === preferredVoice);
    if (preferredVoice && isSupported) {
      return preferredVoice;
    }
    // 如果不支持，返回默认的推荐语音
    return 'Aoede';
  } catch (error) {
    console.error('获取偏好语音失败:', error);
    return 'Aoede';
  }
};

/**
 * 保存用户偏好的语音设置
 */
export const setPreferredVoice = async (voiceName: string): Promise<boolean> => {
  try {
    await AsyncStorage.setItem('preferred_voice', voiceName);
    console.log('语音偏好已保存:', voiceName);
    return true;
  } catch (error) {
    console.error('保存语音偏好失败:', error);
    return false;
  }
};

/**
 * 获取语音信息
 */
export const getVoiceInfo = (voiceName: string): VoiceInfo | null => {
  return AVAILABLE_VOICES.find(voice => voice.name === voiceName) || null;
};

/**
 * 获取推荐的女性声音列表
 */
export const getRecommendedFemaleVoices = (): VoiceInfo[] => {
  return AVAILABLE_VOICES.filter(voice => voice.gender === 'female');
};

/**
 * 获取所有男性声音列表
 */
export const getMaleVoices = (): VoiceInfo[] => {
  return AVAILABLE_VOICES.filter(voice => voice.gender === 'male');
};

/**
 * 快速切换到推荐的女性声音
 */
export const switchToRecommendedFemaleVoice = async (): Promise<string> => {
  const recommendedVoice = 'Aoede';
  await setPreferredVoice(recommendedVoice);
  return recommendedVoice;
};

/**
 * 随机选择一个女性声音
 */
export const getRandomFemaleVoice = async (): Promise<string> => {
  const femaleVoices = getRecommendedFemaleVoices();
  const randomIndex = Math.floor(Math.random() * femaleVoices.length);
  const selectedVoice = femaleVoices[randomIndex].name;
  await setPreferredVoice(selectedVoice);
  return selectedVoice;
};