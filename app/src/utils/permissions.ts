import { Platform, PermissionsAndroid, Alert } from 'react-native';

export const requestAudioPermission = async (): Promise<boolean> => {
  if (Platform.OS === 'android') {
    try {
      const granted = await PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.RECORD_AUDIO,
        {
          title: '音频录制权限',
          message: '应用需要访问麦克风来录制音频',
          buttonNeutral: '稍后询问',
          buttonNegative: '拒绝',
          buttonPositive: '允许',
        }
      );
      return granted === PermissionsAndroid.RESULTS.GRANTED;
    } catch (err) {
      console.warn('权限请求失败:', err);
      return false;
    }
  }
  return true; // iOS权限在Info.plist中配置
};

export const checkPermissions = async (): Promise<boolean> => {
  const hasAudioPermission = await requestAudioPermission();
  
  if (!hasAudioPermission) {
    Alert.alert(
      '权限不足',
      '应用需要麦克风权限才能正常工作，请在设置中开启权限。',
      [{ text: '确定' }]
    );
    return false;
  }
  return true;
};
