/**
 * 调试配置工具
 * 提供快速切换调试模式的方法
 */

import logger, { LogLevel } from './logger';

export class DebugConfig {
  private static instance: DebugConfig;

  private constructor() {}

  static getInstance(): DebugConfig {
    if (!DebugConfig.instance) {
      DebugConfig.instance = new DebugConfig();
    }
    return DebugConfig.instance;
  }

  /**
   * 启用音频调试模式
   * 显示所有音频相关的详细日志
   */
  enableAudioDebug(): void {
    logger.setLevel(LogLevel.DEBUG);
    logger.info('DebugConfig', '🔧 音频调试模式已启用');
    logger.debug('DebugConfig', '现在将显示所有音频处理的详细日志信息');
  }

  /**
   * 启用生产模式
   * 只显示错误信息
   */
  enableProductionMode(): void {
    logger.setLevel(LogLevel.ERROR);
    logger.error('DebugConfig', '🔒 生产模式已启用，只显示错误信息');
  }

  /**
   * 启用信息模式
   * 显示基本信息和错误
   */
  enableInfoMode(): void {
    logger.setLevel(LogLevel.INFO);
    logger.info('DebugConfig', '📋 信息模式已启用');
  }

  /**
   * 获取当前调试级别
   */
  getCurrentLevel(): LogLevel {
    return logger.getLevel();
  }

  /**
   * 获取当前调试级别的描述
   */
  getCurrentLevelDescription(): string {
    const level = this.getCurrentLevel();
    switch (level) {
      case LogLevel.ERROR:
        return '只显示错误信息（生产模式）';
      case LogLevel.WARN:
        return '显示警告和错误信息';
      case LogLevel.INFO:
        return '显示基本信息、警告和错误';
      case LogLevel.DEBUG:
        return '显示所有调试信息（开发模式）';
      default:
        return '未知级别';
    }
  }

  /**
   * 测试日志输出
   */
  testLogOutput(): void {
    logger.error('DebugTest', '这是一个错误日志测试');
    logger.warn('DebugTest', '这是一个警告日志测试');
    logger.info('DebugTest', '这是一个信息日志测试');
    logger.debug('DebugTest', '这是一个调试日志测试');
    logger.audioSuccess('DebugTest', '音频成功日志测试');
    logger.audioInfo('DebugTest', '音频信息日志测试');
    logger.audioError('DebugTest', '音频错误日志测试');
  }
}

export default DebugConfig.getInstance();