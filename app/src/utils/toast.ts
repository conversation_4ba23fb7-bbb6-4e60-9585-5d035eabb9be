import Toast from 'react-native-toast-message';
import { defaultToastProps } from '../config/toastConfig';

// Toast工具函数，简化使用
export const showToast = {
  success: (message: string, subtitle?: string) => {
    Toast.show({
      type: 'success',
      text1: message,
      text2: subtitle,
      ...defaultToastProps,
    });
  },
  
  error: (message: string, subtitle?: string) => {
    Toast.show({
      type: 'error',
      text1: message,
      text2: subtitle,
      ...defaultToastProps,
    });
  },
  
  info: (message: string, subtitle?: string) => {
    Toast.show({
      type: 'info',
      text1: message,
      text2: subtitle,
      ...defaultToastProps,
    });
  },
  
  warning: (message: string, subtitle?: string) => {
    Toast.show({
      type: 'warning',
      text1: message,
      text2: subtitle,
      ...defaultToastProps,
    });
  },
};