import { Platform } from 'react-native';

// Network configuration for different environments
// Android emulator: Use ******** to access host machine's localhost
// iOS simulator: Can use localhost directly
// Physical device: Needs actual IP address of the development machine
const getBaseUrl = () => {
  //return 'http://***************:8090';
  return 'https://interview.szboweixing.com:8443';
};

export const BASE_URL = getBaseUrl();

export const AUDIO_CONFIG = {
  format: 'PCM_16',
  channels: 1,
  sendSampleRate: 16000,
  receiveSampleRate: 24000,
  chunkSize: 1024
};
