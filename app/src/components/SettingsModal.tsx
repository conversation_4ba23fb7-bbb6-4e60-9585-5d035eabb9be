import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  ScrollView,
  Alert,
  Dimensions,
  Platform,
  SafeAreaView,
} from 'react-native';
import { useDispatch } from 'react-redux';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { logout } from '../store/userSlice';
import { theme } from '../styles/theme';
import Button from './Button';
import GradientBackground from './GradientBackground';
import Card from './Card';

interface SettingsModalProps {
  visible: boolean;
  onClose: () => void;
  navigation: any;
}

const SettingsModal: React.FC<SettingsModalProps> = ({
  visible,
  onClose,
  navigation,
}) => {
  const dispatch = useDispatch();

  const handleLogout = () => {
    Alert.alert(
      '退出登录',
      '确定要退出登录吗？',
      [
        { text: '取消' },
        { 
          text: '确定', 
          onPress: () => {
            dispatch(logout());
            onClose();
          }
        }
      ]
    );
  };

  const handleVoiceSettings = () => {
    onClose();
    navigation.navigate('VoiceSettings');
  };

  const settingsOptions = [
    {
      icon: 'record-voice-over',
      title: '语音设置',
      subtitle: '选择您喜欢的语音类型',
      onPress: handleVoiceSettings,
      color: theme.colors.primary,
    },
    {
      icon: 'notifications',
      title: '通知设置',
      subtitle: '管理推送通知',
      onPress: () => {
        // TODO: 实现通知设置
        Alert.alert('提示', '通知设置功能即将上线');
      },
      color: theme.colors.warning,
    },
    {
      icon: 'privacy-tip',
      title: '隐私设置',
      subtitle: '管理您的隐私偏好',
      onPress: () => {
        onClose();
        navigation.navigate('PrivacySettings');
      },
      color: theme.colors.info,
    },
    {
      icon: 'help',
      title: '帮助与反馈',
      subtitle: '获取帮助或提供反馈',
      onPress: () => {
        onClose();
        navigation.navigate('HelpFeedback');
      },
      color: theme.colors.success,
    },
    {
      icon: 'info',
      title: '关于我们',
      subtitle: '查看应用信息',
      onPress: () => {
        onClose();
        navigation.navigate('About');
      },
      color: theme.colors.secondary,
    },
  ];

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <GradientBackground colors={theme.colors.gradientPrimary}>
        <SafeAreaView style={styles.container}>
          {/* Header */}
          <View style={styles.header}>
            <TouchableOpacity onPress={onClose} style={styles.backButton}>
              <Icon name="arrow-back" size={24} color={theme.colors.textInverse} />
            </TouchableOpacity>
            <Text style={styles.headerTitle}>设置</Text>
            <View style={styles.placeholder} />
          </View>

          {/* Content */}
          <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
            <Card style={styles.settingsCard}>
              {settingsOptions.map((option, index) => (
                <TouchableOpacity
                  key={index}
                  style={[
                    styles.settingItem,
                    index < settingsOptions.length - 1 && styles.settingItemBorder
                  ]}
                  onPress={option.onPress}
                  activeOpacity={0.7}
                >
                  <View style={[styles.iconContainer, { backgroundColor: `${option.color}15` }]}>
                    <Icon name={option.icon} size={24} color={option.color} />
                  </View>
                  <View style={styles.textContainer}>
                    <Text style={styles.settingTitle}>{option.title}</Text>
                    <Text style={styles.settingSubtitle}>{option.subtitle}</Text>
                  </View>
                  <Icon name="chevron-right" size={20} color={theme.colors.textSecondary} />
                </TouchableOpacity>
              ))}
            </Card>

            {/* Logout Section */}
            <View style={styles.logoutSection}>
              <Button
                title="退出登录"
                onPress={handleLogout}
                variant="error"
                size="lg"
                style={styles.logoutButton}
              />
            </View>
          </ScrollView>
        </SafeAreaView>
      </GradientBackground>
    </Modal>
  );
};

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');
const isSmallScreen = screenWidth < 375;
const isTablet = screenWidth > 768;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: isSmallScreen ? theme.spacing.md : theme.spacing.lg,
    paddingTop: Platform.OS === 'ios' ? theme.spacing.xl + 20 : theme.spacing.xl,
    paddingBottom: theme.spacing.md,
    backgroundColor: 'transparent',
    minHeight: Platform.OS === 'ios' ? 100 : 80,
  },
  backButton: {
    width: isSmallScreen ? 36 : 40,
    height: isSmallScreen ? 36 : 40,
    borderRadius: isSmallScreen ? 18 : 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: isSmallScreen ? theme.fontSize.lg : theme.fontSize.xl,
    fontWeight: theme.fontWeight.bold as 'bold',
    color: theme.colors.textInverse,
    textAlign: 'center',
    flex: 1,
    marginHorizontal: theme.spacing.sm,
  },
  placeholder: {
    width: isSmallScreen ? 36 : 40,
  },
  content: {
    flex: 1,
    paddingHorizontal: isSmallScreen ? theme.spacing.md : theme.spacing.lg,
    paddingBottom: Platform.OS === 'ios' ? theme.spacing.xl : theme.spacing.lg,
  },
  settingsCard: {
    marginBottom: theme.spacing.lg,
    marginHorizontal: isTablet ? theme.spacing.xl : 0,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: isSmallScreen ? theme.spacing.md : theme.spacing.lg,
    paddingHorizontal: isSmallScreen ? theme.spacing.sm : theme.spacing.md,
    minHeight: isSmallScreen ? 60 : 72,
  },
  settingItemBorder: {
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.borderLight,
  },
  iconContainer: {
    width: isSmallScreen ? 40 : 48,
    height: isSmallScreen ? 40 : 48,
    borderRadius: isSmallScreen ? 20 : 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: isSmallScreen ? theme.spacing.sm : theme.spacing.md,
  },
  textContainer: {
    flex: 1,
    paddingRight: theme.spacing.sm,
  },
  settingTitle: {
    fontSize: isSmallScreen ? theme.fontSize.sm : theme.fontSize.base,
    fontWeight: theme.fontWeight.semibold as '600',
    color: theme.colors.textPrimary,
    marginBottom: theme.spacing.xs,
    lineHeight: isSmallScreen ? 18 : 20,
  },
  settingSubtitle: {
    fontSize: isSmallScreen ? theme.fontSize.xs : theme.fontSize.sm,
    color: theme.colors.textSecondary,
    lineHeight: isSmallScreen ? 16 : 18,
    flexWrap: 'wrap',
  },
  logoutSection: {
    paddingVertical: isSmallScreen ? theme.spacing.lg : theme.spacing.xxl,
    paddingHorizontal: isSmallScreen ? theme.spacing.sm : theme.spacing.md,
    marginHorizontal: isTablet ? theme.spacing.xl : 0,
  },
  logoutButton: {
    width: '100%',
    minHeight: isSmallScreen ? 44 : 48,
  },
});

export default SettingsModal;