import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Animated,
  Dimensions,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';

interface PaymentSuccessAnimationProps {
  visible: boolean;
  onAnimationComplete?: () => void;
}

const { width: screenWidth } = Dimensions.get('window');

const PaymentSuccessAnimation: React.FC<PaymentSuccessAnimationProps> = ({
  visible,
  onAnimationComplete,
}) => {
  const [checkAnimation] = useState(new Animated.Value(0));
  const [circleAnimation] = useState(new Animated.Value(0));
  const [textAnimation] = useState(new Animated.Value(0));
  const [particleAnimations] = useState(
    Array.from({ length: 8 }, () => new Animated.Value(0))
  );

  useEffect(() => {
    if (visible) {
      // 动画序列
      Animated.sequence([
        // 圆圈扩展
        Animated.timing(circleAnimation, {
          toValue: 1,
          duration: 600,
          useNativeDriver: true,
        }),
        // 对勾出现
        Animated.timing(checkAnimation, {
          toValue: 1,
          duration: 400,
          useNativeDriver: true,
        }),
        // 粒子效果
        Animated.parallel(
          particleAnimations.map((anim, index) =>
            Animated.timing(anim, {
              toValue: 1,
              duration: 800,
              delay: index * 100,
              useNativeDriver: true,
            })
          )
        ),
        // 文字出现
        Animated.timing(textAnimation, {
          toValue: 1,
          duration: 500,
          useNativeDriver: true,
        }),
      ]).start(() => {
        if (onAnimationComplete) {
          setTimeout(onAnimationComplete, 1000);
        }
      });
    } else {
      // 重置动画
      checkAnimation.setValue(0);
      circleAnimation.setValue(0);
      textAnimation.setValue(0);
      particleAnimations.forEach(anim => anim.setValue(0));
    }
  }, [visible, checkAnimation, circleAnimation, onAnimationComplete, particleAnimations, textAnimation]);

  if (!visible) return null;

  return (
    <View style={styles.container}>
      {/* 背景圆圈 */}
      <Animated.View
        style={[
          styles.circle,
          {
            transform: [
              {
                scale: circleAnimation.interpolate({
                  inputRange: [0, 1],
                  outputRange: [0, 1],
                }),
              },
            ],
            opacity: circleAnimation,
          },
        ]}
      />

      {/* 对勾图标 */}
      <Animated.View
        style={[
          styles.checkContainer,
          {
            transform: [
              {
                scale: checkAnimation.interpolate({
                  inputRange: [0, 0.5, 1],
                  outputRange: [0, 1.2, 1],
                }),
              },
            ],
            opacity: checkAnimation,
          },
        ]}
      >
        <Icon name="check" size={60} color="white" />
      </Animated.View>

      {/* 粒子效果 */}
      {particleAnimations.map((anim, index) => {
        const angle = (index * 360) / particleAnimations.length;
        const radius = 80;
        const x = Math.cos((angle * Math.PI) / 180) * radius;
        const y = Math.sin((angle * Math.PI) / 180) * radius;

        return (
          <Animated.View
            key={index}
            style={[
              styles.particle,
              {
                transform: [
                  {
                    translateX: anim.interpolate({
                      inputRange: [0, 1],
                      outputRange: [0, x],
                    }),
                  },
                  {
                    translateY: anim.interpolate({
                      inputRange: [0, 1],
                      outputRange: [0, y],
                    }),
                  },
                  {
                    scale: anim.interpolate({
                      inputRange: [0, 0.5, 1],
                      outputRange: [0, 1, 0],
                    }),
                  },
                ],
                opacity: anim.interpolate({
                  inputRange: [0, 0.5, 1],
                  outputRange: [0, 1, 0],
                }),
              },
            ]}
          />
        );
      })}

      {/* 成功文字 */}
      <Animated.View
        style={[
          styles.textContainer,
          {
            transform: [
              {
                translateY: textAnimation.interpolate({
                  inputRange: [0, 1],
                  outputRange: [20, 0],
                }),
              },
            ],
            opacity: textAnimation,
          },
        ]}
      >
        <Text style={styles.successText}>支付成功！</Text>
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  circle: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: '#4CAF50',
    position: 'absolute',
  },
  checkContainer: {
    position: 'absolute',
    justifyContent: 'center',
    alignItems: 'center',
  },
  particle: {
    position: 'absolute',
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#FFD700',
  },
  textContainer: {
    position: 'absolute',
    top: '60%',
  },
  successText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
    textAlign: 'center',
  },
});

export default PaymentSuccessAnimation;