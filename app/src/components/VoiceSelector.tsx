import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  ScrollView,
  Alert
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface VoiceOption {
  name: string;
  displayName: string;
  gender: 'male' | 'female';
  description: string;
  recommended?: boolean;
}

// Google Gemini Live API 支持的语音列表
// 基于官方文档和API测试确认的可用语音
const VOICE_OPTIONS: VoiceOption[] = [
  // 女性声音 - 经过验证的可用语音
  { name: 'Aoede', displayName: 'Aoede', gender: 'female', description: 'Natural and clear - 自然清晰', recommended: true },
  { name: '<PERSON><PERSON>', displayName: 'Charon', gender: 'female', description: 'Warm and friendly - 温暖友好', recommended: true },
  { name: '<PERSON>rir', displayName: 'Fenrir', gender: 'female', description: 'Professional and confident - 专业自信' },
  
  // 男性声音 - 经过验证的可用语音
  { name: 'Puck', displayName: 'Puck', gender: 'male', description: 'Clear and confident - 清晰自信', recommended: true },
  { name: '<PERSON><PERSON>', displayName: '<PERSON>re', gender: 'male', description: 'Professional and steady - 专业稳重' },
  { name: 'Orus', displayName: 'Orus', gender: 'male', description: 'Firm - 坚定' },
  { name: 'Enceladus', displayName: 'Enceladus', gender: 'male', description: 'Breathy - 气声' },
  { name: 'Iapetus', displayName: 'Iapetus', gender: 'male', description: 'Clear - 清晰' },
  { name: 'Algieba', displayName: 'Algieba', gender: 'male', description: 'Smooth - 流畅' },
  { name: 'Rasalgethi', displayName: 'Rasalgethi', gender: 'male', description: 'Informative - 信息丰富' },
  { name: 'Alnilam', displayName: 'Alnilam', gender: 'male', description: 'Firm - 坚定' },
];

interface VoiceSelectorProps {
  currentVoice?: string;
  onVoiceChange?: (voiceName: string) => void;
}

const VoiceSelector: React.FC<VoiceSelectorProps> = ({ 
  currentVoice = 'Aoede', 
  onVoiceChange 
}) => {
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedVoice, setSelectedVoice] = useState(currentVoice);

  const handleVoiceSelect = async (voiceName: string) => {
    try {
      await AsyncStorage.setItem('preferred_voice', voiceName);
      setSelectedVoice(voiceName);
      setModalVisible(false);
      onVoiceChange?.(voiceName);
      
      const voice = VOICE_OPTIONS.find(v => v.name === voiceName);
      Alert.alert(
        '语音已更改',
        `已切换到 ${voice?.displayName}，将在下次对话时生效。`,
        [{ text: '确定' }]
      );
    } catch (error) {
      console.error('保存语音设置失败:', error);
      Alert.alert('保存失败', '语音设置保存失败，请重试');
    }
  };

  const getCurrentVoiceInfo = () => {
    return VOICE_OPTIONS.find(v => v.name === selectedVoice) || VOICE_OPTIONS[0];
  };

  const renderVoiceOption = (voice: VoiceOption) => {
    const isSelected = selectedVoice === voice.name;
    
    return (
      <TouchableOpacity
        key={voice.name}
        style={[styles.voiceOption, isSelected && styles.selectedOption]}
        onPress={() => handleVoiceSelect(voice.name)}
      >
        <View style={styles.voiceInfo}>
          <Text style={[styles.voiceName, isSelected && styles.selectedText]}>
            {voice.displayName}
            {voice.recommended && <Text style={styles.recommendedTag}> 推荐</Text>}
          </Text>
          <Text style={[styles.genderTag, voice.gender === 'female' ? styles.femaleTag : styles.maleTag]}>
            {voice.gender === 'female' ? '女性' : '男性'}
          </Text>
        </View>
        <Text style={styles.voiceDescription}>{voice.description}</Text>
        {isSelected && <Text style={styles.checkMark}>✓</Text>}
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={styles.selectorButton}
        onPress={() => setModalVisible(true)}
      >
        <Text style={styles.selectorLabel}>语音</Text>
        <Text style={styles.selectorValue}>
          {getCurrentVoiceInfo().displayName}
          <Text style={styles.genderIndicator}>
            {getCurrentVoiceInfo().gender === 'female' ? ' 👩' : ' 👨'}
          </Text>
        </Text>
      </TouchableOpacity>

      <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>选择语音</Text>
              <TouchableOpacity
                style={styles.closeButton}
                onPress={() => setModalVisible(false)}
              >
                <Text style={styles.closeButtonText}>×</Text>
              </TouchableOpacity>
            </View>
            
            <ScrollView style={styles.voiceList}>
              <Text style={styles.sectionTitle}>女性声音 (推荐)</Text>
              {VOICE_OPTIONS.filter(v => v.gender === 'female').map(renderVoiceOption)}
              
              <Text style={styles.sectionTitle}>男性声音</Text>
              {VOICE_OPTIONS.filter(v => v.gender === 'male').map(renderVoiceOption)}
            </ScrollView>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 5,
  },
  selectorButton: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e9ecef',
  },
  selectorLabel: {
    fontSize: 14,
    color: '#666',
    fontWeight: '500',
  },
  selectorValue: {
    fontSize: 14,
    color: '#333',
    fontWeight: '600',
  },
  genderIndicator: {
    fontSize: 12,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: '#fff',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#e9ecef',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  closeButton: {
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: '#f8f9fa',
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeButtonText: {
    fontSize: 20,
    color: '#666',
    fontWeight: 'bold',
  },
  voiceList: {
    padding: 20,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 12,
    marginTop: 10,
  },
  voiceOption: {
    backgroundColor: '#f8f9fa',
    borderRadius: 12,
    padding: 16,
    marginBottom: 8,
    borderWidth: 2,
    borderColor: 'transparent',
    position: 'relative',
  },
  selectedOption: {
    borderColor: '#007AFF',
    backgroundColor: '#f0f8ff',
  },
  voiceInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  voiceName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    flex: 1,
  },
  selectedText: {
    color: '#007AFF',
  },
  recommendedTag: {
    fontSize: 12,
    color: '#FF6B35',
    fontWeight: 'bold',
  },
  genderTag: {
    fontSize: 12,
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
    overflow: 'hidden',
  },
  femaleTag: {
    backgroundColor: '#FFE4E1',
    color: '#FF69B4',
  },
  maleTag: {
    backgroundColor: '#E6F3FF',
    color: '#4169E1',
  },
  voiceDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  checkMark: {
    position: 'absolute',
    top: 12,
    right: 12,
    fontSize: 18,
    color: '#007AFF',
    fontWeight: 'bold',
  },
});

export default VoiceSelector;
