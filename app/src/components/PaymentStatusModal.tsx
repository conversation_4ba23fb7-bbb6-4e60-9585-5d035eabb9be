import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  Modal,
  StyleSheet,
  Animated,
  Dimensions,
  TouchableOpacity,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { PaymentStatus } from '../services/PaymentService';

interface PaymentStatusModalProps {
  visible: boolean;
  status: PaymentStatus | null;
  onClose: () => void;
  onRetry?: () => void;
}

const { width } = Dimensions.get('window');

const PaymentStatusModal: React.FC<PaymentStatusModalProps> = ({
  visible,
  status,
  onClose,
  onRetry,
}) => {
  const [animation] = useState(new Animated.Value(0));
  const [iconAnimation] = useState(new Animated.Value(0));

  useEffect(() => {
    if (visible) {
      Animated.parallel([
        Animated.spring(animation, {
          toValue: 1,
          useNativeDriver: true,
        }),
        Animated.sequence([
          Animated.delay(300),
          Animated.spring(iconAnimation, {
            toValue: 1,
            useNativeDriver: true,
          }),
        ]),
      ]).start();
    } else {
      animation.setValue(0);
      iconAnimation.setValue(0);
    }
  }, [visible, animation, iconAnimation]);

  const getStatusConfig = () => {
    switch (status?.status) {
      case 'processing':
        return {
          icon: 'payment',
          color: '#2196F3',
          title: '正在支付',
          description: status.message || '请在第三方应用中完成支付',
          showRetry: false,
        };
      case 'success':
        return {
          icon: 'check-circle',
          color: '#4CAF50',
          title: '支付成功',
          description: '支付已完成，正在更新账户信息...',
          showRetry: false,
        };
      case 'failed':
        return {
          icon: 'error',
          color: '#F44336',
          title: '支付失败',
          description: status.message || '支付过程中出现错误',
          showRetry: true,
        };
      case 'cancelled':
        return {
          icon: 'cancel',
          color: '#FF9800',
          title: '支付取消',
          description: '您已取消本次支付',
          showRetry: true,
        };
      default:
        return {
          icon: 'payment',
          color: '#2196F3',
          title: '准备支付',
          description: '正在准备支付信息...',
          showRetry: false,
        };
    }
  };

  const config = getStatusConfig();

  return (
    <Modal
      visible={visible}
      transparent
      animationType="none"
      onRequestClose={onClose}
    >
      <View style={styles.overlay}>
        <Animated.View
          style={[
            styles.container,
            {
              transform: [
                {
                  scale: animation.interpolate({
                    inputRange: [0, 1],
                    outputRange: [0.8, 1],
                  }),
                },
              ],
              opacity: animation,
            },
          ]}
        >
          <Animated.View
            style={[
              styles.iconContainer,
              {
                backgroundColor: config.color + '20',
                transform: [
                  {
                    scale: iconAnimation.interpolate({
                      inputRange: [0, 1],
                      outputRange: [0, 1],
                    }),
                  },
                ],
              },
            ]}
          >
            <Icon name={config.icon} size={48} color={config.color} />
          </Animated.View>

          <Text style={styles.title}>{config.title}</Text>
          <Text style={styles.description}>{config.description}</Text>

          {status?.status === 'processing' && (
            <View style={styles.loadingContainer}>
              <Animated.View
                style={[
                  styles.loadingBar,
                  {
                    transform: [
                      {
                        scaleX: animation,
                      },
                    ],
                  },
                ]}
              />
            </View>
          )}

          <View style={styles.buttonContainer}>
            {config.showRetry && onRetry && (
              <TouchableOpacity style={styles.retryButton} onPress={onRetry}>
                <Text style={styles.retryButtonText}>重试支付</Text>
              </TouchableOpacity>
            )}
            
            {(status?.status === 'failed' || status?.status === 'cancelled') && (
              <TouchableOpacity style={styles.closeButton} onPress={onClose}>
                <Text style={styles.closeButtonText}>关闭</Text>
              </TouchableOpacity>
            )}
          </View>
        </Animated.View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  container: {
    width: width * 0.8,
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 24,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.25,
    shadowRadius: 8,
    elevation: 8,
  },
  iconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
    textAlign: 'center',
  },
  description: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 24,
  },
  loadingContainer: {
    width: '100%',
    height: 4,
    backgroundColor: '#E0E0E0',
    borderRadius: 2,
    marginBottom: 24,
    overflow: 'hidden',
  },
  loadingBar: {
    height: '100%',
    backgroundColor: '#2196F3',
    borderRadius: 2,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    width: '100%',
  },
  retryButton: {
    backgroundColor: '#2196F3',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
    marginRight: 12,
  },
  retryButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  closeButton: {
    backgroundColor: '#F5F5F5',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  closeButtonText: {
    color: '#666',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default PaymentStatusModal;