import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import Ionicons from 'react-native-vector-icons/Ionicons';

interface HeaderProps {
  title: string;
  subtitle?: string;
  onBackPress?: () => void;
}

const Header: React.FC<HeaderProps> = ({ title, subtitle, onBackPress }) => {
  return (
    <View style={styles.headerContainer}>
      {onBackPress && (
        <TouchableOpacity onPress={onBackPress} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color="#fff" />
        </TouchableOpacity>
      )}
      <View style={styles.titleContainer}>
        <Text style={styles.title}>{title}</Text>
        {subtitle && <Text style={styles.subtitle}>{subtitle}</Text>}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 15,
    // Background will be handled by <PERSON>rad<PERSON><PERSON><PERSON><PERSON> in the parent screen
  },
  backButton: {
    marginRight: 15,
    padding: 5, // Add some padding for easier touch
  },
  titleContainer: {
    flex: 1,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff', // Assuming white text on a gradient background
  },
  subtitle: {
    fontSize: 16,
    color: '#eee', // Slightly lighter color for subtitle
    marginTop: 2,
  },
});

export default Header;