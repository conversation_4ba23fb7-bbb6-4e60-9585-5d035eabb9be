import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  Modal,
  StyleSheet,
  Animated,
  Dimensions,
  TouchableOpacity,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { Product } from '../services/PaymentService';

interface PaymentSuccessModalProps {
  visible: boolean;
  product: Product | null;
  onContinueShopping: () => void;
  onGoToProfile: () => void;
  onStartInterview: () => void;
  onViewOrders: () => void;
}

const { width } = Dimensions.get('window');

const PaymentSuccessModal: React.FC<PaymentSuccessModalProps> = ({
  visible,
  product,
  onContinueShopping,
  onGoToProfile,
  onStartInterview,
  onViewOrders,
}) => {
  const [animation] = useState(new Animated.Value(0));
  const [confettiAnimation] = useState(new Animated.Value(0));
  const [buttonAnimation] = useState(new Animated.Value(0));

  useEffect(() => {
    if (visible) {
      // 成功动画序列
      Animated.sequence([
        // 模态框弹出
        Animated.spring(animation, {
          toValue: 1,
          useNativeDriver: true,
          tension: 100,
          friction: 8,
        }),
        // 延迟后显示彩带动画
        Animated.delay(300),
        Animated.spring(confettiAnimation, {
          toValue: 1,
          useNativeDriver: true,
        }),
        // 按钮动画
        Animated.delay(200),
        Animated.spring(buttonAnimation, {
          toValue: 1,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      animation.setValue(0);
      confettiAnimation.setValue(0);
      buttonAnimation.setValue(0);
    }
  }, [visible]);

  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    return `${minutes}分钟`;
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="none"
      onRequestClose={onContinueShopping}
    >
      <View style={styles.overlay}>
        <Animated.View
          style={[
            styles.container,
            {
              transform: [
                {
                  scale: animation.interpolate({
                    inputRange: [0, 1],
                    outputRange: [0.3, 1],
                  }),
                },
              ],
              opacity: animation,
            },
          ]}
        >
          {/* 成功图标和彩带效果 */}
          <View style={styles.iconContainer}>
            <Animated.View
              style={[
                styles.successIcon,
                {
                  transform: [
                    {
                      rotate: confettiAnimation.interpolate({
                        inputRange: [0, 1],
                        outputRange: ['0deg', '360deg'],
                      }),
                    },
                  ],
                },
              ]}
            >
              <Icon name="check-circle" size={80} color="#4CAF50" />
            </Animated.View>
            
            {/* 彩带效果 */}
            <Animated.View
              style={[
                styles.confetti,
                styles.confetti1,
                {
                  opacity: confettiAnimation,
                  transform: [
                    {
                      translateY: confettiAnimation.interpolate({
                        inputRange: [0, 1],
                        outputRange: [-50, 50],
                      }),
                    },
                  ],
                },
              ]}
            />
            <Animated.View
              style={[
                styles.confetti,
                styles.confetti2,
                {
                  opacity: confettiAnimation,
                  transform: [
                    {
                      translateY: confettiAnimation.interpolate({
                        inputRange: [0, 1],
                        outputRange: [-30, 70],
                      }),
                    },
                  ],
                },
              ]}
            />
          </View>

          <Text style={styles.title}>支付成功！</Text>
          <Text style={styles.subtitle}>恭喜您购买成功</Text>

          {product && (
            <View style={styles.purchaseInfo}>
              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>购买商品</Text>
                <Text style={styles.infoValue}>{product.name}</Text>
              </View>
              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>获得时长</Text>
                <Text style={styles.infoValue}>{formatDuration(product.duration)}</Text>
              </View>
              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>支付金额</Text>
                <Text style={styles.infoValuePrice}>¥{product.price}</Text>
              </View>
            </View>
          )}

          <View style={styles.benefitsContainer}>
            <Text style={styles.benefitsTitle}>您现在可以：</Text>
            <View style={styles.benefitItem}>
              <Icon name="chat" size={20} color="#2196F3" />
              <Text style={styles.benefitText}>开始AI面试练习</Text>
            </View>
            <View style={styles.benefitItem}>
              <Icon name="history" size={20} color="#2196F3" />
              <Text style={styles.benefitText}>查看历史记录</Text>
            </View>
            <View style={styles.benefitItem}>
              <Icon name="trending-up" size={20} color="#2196F3" />
              <Text style={styles.benefitText}>提升面试技能</Text>
            </View>
          </View>

          <Animated.View
            style={[
              styles.buttonContainer,
              {
                opacity: buttonAnimation,
                transform: [
                  {
                    translateY: buttonAnimation.interpolate({
                      inputRange: [0, 1],
                      outputRange: [30, 0],
                    }),
                  },
                ],
              },
            ]}
          >
            <TouchableOpacity style={styles.primaryButton} onPress={onStartInterview}>
              <Icon name="play-arrow" size={20} color="white" />
              <Text style={styles.primaryButtonText}>立即开始</Text>
            </TouchableOpacity>

            <View style={styles.secondaryButtons}>
              <TouchableOpacity style={styles.secondaryButton} onPress={onViewOrders}>
                <Icon name="receipt" size={18} color="#666" />
                <Text style={styles.secondaryButtonText}>查看订单</Text>
              </TouchableOpacity>
              
              <TouchableOpacity style={styles.secondaryButton} onPress={onGoToProfile}>
                <Icon name="person" size={18} color="#666" />
                <Text style={styles.secondaryButtonText}>查看账户</Text>
              </TouchableOpacity>
              
              <TouchableOpacity style={styles.secondaryButton} onPress={onContinueShopping}>
                <Icon name="shopping-cart" size={18} color="#666" />
                <Text style={styles.secondaryButtonText}>继续购买</Text>
              </TouchableOpacity>
            </View>
          </Animated.View>
        </Animated.View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  container: {
    width: width * 0.9,
    maxWidth: 400,
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 24,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 10,
    },
    shadowOpacity: 0.3,
    shadowRadius: 20,
    elevation: 15,
  },
  iconContainer: {
    position: 'relative',
    marginBottom: 20,
  },
  successIcon: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  confetti: {
    position: 'absolute',
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  confetti1: {
    backgroundColor: '#FFD700',
    top: -20,
    left: -30,
  },
  confetti2: {
    backgroundColor: '#FF6B6B',
    top: -10,
    right: -25,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    marginBottom: 24,
    textAlign: 'center',
  },
  purchaseInfo: {
    width: '100%',
    backgroundColor: '#F8F9FA',
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  infoLabel: {
    fontSize: 14,
    color: '#666',
  },
  infoValue: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
  },
  infoValuePrice: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#4CAF50',
  },
  benefitsContainer: {
    width: '100%',
    marginBottom: 24,
  },
  benefitsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 12,
    textAlign: 'center',
  },
  benefitItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    paddingHorizontal: 16,
  },
  benefitText: {
    fontSize: 14,
    color: '#666',
    marginLeft: 8,
  },
  buttonContainer: {
    width: '100%',
  },
  primaryButton: {
    backgroundColor: '#4CAF50',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    borderRadius: 12,
    marginBottom: 12,
    shadowColor: '#4CAF50',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  primaryButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  secondaryButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    flexWrap: 'wrap',
    gap: 8,
  },
  secondaryButton: {
    flex: 1,
    minWidth: '30%',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 8,
    backgroundColor: '#F5F5F5',
    borderRadius: 8,
  },
  secondaryButtonText: {
    color: '#666',
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 4,
  },
});

export default PaymentSuccessModal;
