import React from 'react';
import { View, StyleSheet } from 'react-native';
import { theme } from '../styles/theme';

interface GradientBackgroundProps {
  children: React.ReactNode;
  colors?: string[];
  style?: any;
}

const GradientBackground: React.FC<GradientBackgroundProps> = ({ 
  children, 
  colors = theme.colors.gradientBackground,
  style 
}) => {
  return (
    <View style={[styles.container, { backgroundColor: colors[0] }, style]}>
      <View style={[styles.gradient, { backgroundColor: colors[1] }]} />
      {children}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  gradient: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: '50%',
    opacity: 0.3,
  },
});

export default GradientBackground;