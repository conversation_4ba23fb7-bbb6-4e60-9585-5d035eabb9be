import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  FlatList,
  Dimensions,
  SafeAreaView,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';

interface Mode {
  id: string;
  title: string;
  icon: string;
  color: string;
}

// 定义所有面试模式，方便管理
const interviewModes: Mode[] = [
  { id: 'technical_interview', title: '技术岗', icon: 'computer', color: '#4A90E2' },
  { id: 'hr_interview', title: '人力资源', icon: 'people', color: '#50E3C2' },
  { id: 'sales_interview', title: '销售岗', icon: 'trending-up', color: '#F5A623' },
  { id: 'marketing_interview', title: '市场营销', icon: 'campaign', color: '#BD10E0' },
  { id: 'finance_interview', title: '金融岗', icon: 'account-balance', color: '#7ED321' },
  { id: 'legal_interview', title: '法律岗', icon: 'gavel', color: '#9B9B9B' },
  { id: 'medical_interview', title: '医疗岗', icon: 'local-hospital', color: '#D0021B' },
  { id: 'education_interview', title: '教育岗', icon: 'school', color: '#417505' },
  { id: 'architecture_interview', title: '建筑设计', icon: 'architecture', color: '#B8E986' },
  { id: 'psychology_interview', title: '心理咨询', icon: 'psychology', color: '#F8E71C' },
  { id: 'leisure', title: '随便聊聊', icon: 'chat', color: '#9013FE' },
];

interface ModeSelectorProps {
  onSelectMode: (mode: string) => void;
  onClose?: () => void;
}

const numColumns = 3;
const screenWidth = Dimensions.get('window').width;

const ModeSelector: React.FC<ModeSelectorProps> = ({ onSelectMode, onClose }) => {
  const renderItem = ({ item }: { item: Mode }) => (
    <TouchableOpacity
      style={styles.itemContainer}
      onPress={() => onSelectMode(item.id)}>
      <View style={[styles.iconWrapper, { backgroundColor: item.color }]}>
        <Icon name={item.icon} size={35} color="#fff" />
      </View>
      <Text style={styles.itemText}>{item.title}</Text>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.safeArea}>
      <View style={styles.container}>
        {/* 头部导航 */}
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={onClose}
          >
            <Icon name="arrow-back" size={24} color="#333" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>选择面试职业</Text>
          <View style={styles.placeholder} />
        </View>

        <FlatList
          data={interviewModes}
          renderItem={renderItem}
          keyExtractor={item => item.id}
          numColumns={numColumns}
          contentContainerStyle={styles.list}
          showsVerticalScrollIndicator={false}
        />
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#f5f5f7',
  },
  container: {
    flex: 1,
    paddingHorizontal: 20,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 16,
    marginBottom: 20,
  },
  backButton: {
    padding: 8,
    borderRadius: 20,
    backgroundColor: '#f0f0f0',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    flex: 1,
    textAlign: 'center',
  },
  placeholder: {
    width: 40, // 与backButton宽度相同，保持居中
  },
  list: {
    justifyContent: 'center',
  },
  itemContainer: {
    width: (screenWidth - 40) / numColumns,
    alignItems: 'center',
    marginBottom: 30,
  },
  iconWrapper: {
    width: 70,
    height: 70,
    borderRadius: 35,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  itemText: {
    fontSize: 14,
    color: '#333',
    fontWeight: '500',
  },
});

export default ModeSelector;