import React from 'react';
import { View, StyleSheet, ViewStyle } from 'react-native';
import { theme } from '../styles/theme';

interface CardProps {
  children: React.ReactNode;
  style?: ViewStyle;
  padding?: keyof typeof theme.spacing;
  shadow?: keyof typeof theme.shadows;
}

const Card: React.FC<CardProps> = ({ 
  children, 
  style, 
  padding = 'md',
  shadow = 'sm'
}) => {
  return (
    <View style={[
      styles.card,
      { padding: theme.spacing[padding] },
      theme.shadows[shadow],
      style
    ]}>
      {children}
    </View>
  );
};

const styles = StyleSheet.create({
  card: {
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.lg,
    marginVertical: theme.spacing.sm,
  },
});

export default Card;