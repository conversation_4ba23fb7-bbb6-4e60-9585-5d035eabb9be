import React, { useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Modal } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import logger, { LogLevel } from '../utils/logger';
import { theme } from '../styles/theme';

interface DebugLogControllerProps {
  visible: boolean;
  onClose: () => void;
}

const DebugLogController: React.FC<DebugLogControllerProps> = ({ visible, onClose }) => {
  const [currentLevel, setCurrentLevel] = useState(logger.getLevel());

  const logLevels = [
    { level: LogLevel.ERROR, name: 'ERROR', description: '只显示错误信息', color: '#ff4444' },
    { level: LogLevel.WARN, name: 'WARN', description: '显示警告和错误', color: '#ff8800' },
    { level: LogLevel.INFO, name: 'INFO', description: '显示基本信息', color: '#0088ff' },
    { level: LogLevel.DEBUG, name: 'DEBUG', description: '显示所有调试信息', color: '#00aa00' },
  ];

  const handleLevelChange = (level: LogLevel) => {
    setCurrentLevel(level);
    logger.setLevel(level);
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="slide"
      onRequestClose={onClose}
    >
      <View style={styles.overlay}>
        <View style={styles.container}>
          <View style={styles.header}>
            <Text style={styles.title}>🔧 调试日志控制</Text>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <Icon name="close" size={24} color={theme.colors.text} />
            </TouchableOpacity>
          </View>

          <Text style={styles.description}>
            选择日志级别来控制音频处理相关的调试信息显示：
          </Text>

          <View style={styles.levelContainer}>
            {logLevels.map((item) => (
              <TouchableOpacity
                key={item.level}
                style={[
                  styles.levelButton,
                  currentLevel === item.level && styles.selectedLevel,
                  { borderColor: item.color }
                ]}
                onPress={() => handleLevelChange(item.level)}
              >
                <View style={styles.levelInfo}>
                  <View style={styles.levelHeader}>
                    <View style={[styles.levelIndicator, { backgroundColor: item.color }]} />
                    <Text style={[
                      styles.levelName,
                      currentLevel === item.level && styles.selectedLevelText
                    ]}>
                      {item.name}
                    </Text>
                    {currentLevel === item.level && (
                      <Icon name="check" size={20} color={item.color} />
                    )}
                  </View>
                  <Text style={styles.levelDescription}>{item.description}</Text>
                </View>
              </TouchableOpacity>
            ))}
          </View>

          <View style={styles.currentStatus}>
            <Text style={styles.statusText}>
              当前级别: <Text style={styles.currentLevelText}>
                {LogLevel[currentLevel]}
              </Text>
            </Text>
          </View>

          <View style={styles.tips}>
            <Text style={styles.tipsTitle}>💡 提示：</Text>
            <Text style={styles.tipsText}>
              • ERROR: 生产环境推荐，只显示错误信息{'\n'}
              • DEBUG: 开发调试时使用，显示详细的音频处理日志{'\n'}
              • 包括音频块发送、接收、播放等详细信息
            </Text>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  container: {
    backgroundColor: theme.colors.surface,
    borderRadius: 16,
    padding: 20,
    width: '100%',
    maxWidth: 400,
    maxHeight: '80%',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
  },
  closeButton: {
    padding: 4,
  },
  description: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginBottom: 20,
    lineHeight: 20,
  },
  levelContainer: {
    marginBottom: 20,
  },
  levelButton: {
    borderWidth: 2,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    backgroundColor: theme.colors.background,
  },
  selectedLevel: {
    backgroundColor: theme.colors.primary + '10',
  },
  levelInfo: {
    flex: 1,
  },
  levelHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  levelIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  levelName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: theme.colors.text,
    flex: 1,
  },
  selectedLevelText: {
    color: theme.colors.primary,
  },
  levelDescription: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginLeft: 20,
  },
  currentStatus: {
    backgroundColor: theme.colors.background,
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  statusText: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    textAlign: 'center',
  },
  currentLevelText: {
    fontWeight: 'bold',
    color: theme.colors.primary,
  },
  tips: {
    backgroundColor: theme.colors.background,
    padding: 12,
    borderRadius: 8,
    borderLeftWidth: 4,
    borderLeftColor: theme.colors.primary,
  },
  tipsTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: 4,
  },
  tipsText: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    lineHeight: 16,
  },
});

export default DebugLogController;