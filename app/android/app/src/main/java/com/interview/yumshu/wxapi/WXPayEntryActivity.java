package com.app.wxapi;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.util.Log;

import com.tencent.mm.opensdk.constants.ConstantsAPI;
import com.tencent.mm.opensdk.modelbase.BaseReq;
import com.tencent.mm.opensdk.modelbase.BaseResp;
import com.tencent.mm.opensdk.openapi.IWXAPI;
import com.tencent.mm.opensdk.openapi.IWXAPIEventHandler;
import com.tencent.mm.opensdk.openapi.WXAPIFactory;

public class WXPayEntryActivity extends Activity implements IWXAPIEventHandler {
    
    private static final String TAG = "WXPayEntryActivity";
    private IWXAPI api;
    
    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        // 注意：这里需要替换为实际的微信App ID
        api = WXAPIFactory.createWXAPI(this, "your_wechat_app_id");
        api.handleIntent(getIntent(), this);
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        setIntent(intent);
        api.handleIntent(intent, this);
    }

    @Override
    public void onReq(BaseReq req) {
        Log.d(TAG, "onReq: " + req.getType());
    }

    @Override
    public void onResp(BaseResp resp) {
        Log.d(TAG, "onResp: " + resp.errCode);
        
        if (resp.getType() == ConstantsAPI.COMMAND_PAY_BY_WX) {
            // 支付结果处理
            switch (resp.errCode) {
                case BaseResp.ErrCode.ERR_OK:
                    Log.d(TAG, "支付成功");
                    break;
                case BaseResp.ErrCode.ERR_USER_CANCEL:
                    Log.d(TAG, "用户取消支付");
                    break;
                case BaseResp.ErrCode.ERR_COMM:
                    Log.d(TAG, "支付失败");
                    break;
                default:
                    Log.d(TAG, "支付结果未知: " + resp.errCode);
                    break;
            }
            
            // 发送广播通知React Native层
            Intent intent = new Intent("WECHAT_PAY_RESULT");
            intent.putExtra("errCode", resp.errCode);
            sendBroadcast(intent);
        }
        finish();
    }
}