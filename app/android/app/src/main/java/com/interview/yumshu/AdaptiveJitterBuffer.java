package com.interview.yumshu;

import android.util.Log;
import java.util.PriorityQueue;

public class AdaptiveJitterBuffer {

    public static class RTPPacket implements Comparable<RTPPacket> {
        public final byte[] payload;
        public final int timestamp;
        public final int sequenceNumber;

        public RTPPacket(byte[] payload, int timestamp, int sequenceNumber) {
            this.payload = payload;
            this.timestamp = timestamp;
            this.sequenceNumber = sequenceNumber;
        }

        @Override
        public int compareTo(RTPPacket other) {
            return Integer.compare(this.sequenceNumber, other.sequenceNumber);
        }
    }

    private final PriorityQueue<RTPPacket> packetQueue = new PriorityQueue<>();
    private final int sampleRate;
    private final int channels;

    // 简单的时间戳缓冲控制
    private int firstTimestamp = -1;
    private final int minBufferMs = 50; // 最小50ms缓冲，确保不丢包

    public AdaptiveJitterBuffer(int sampleRate, int channels) {
        this.sampleRate = sampleRate;
        this.channels = channels;
    }

    public synchronized void addPacket(RTPPacket packet) {
        // 绝不丢包策略：直接添加包到优先队列
        // 优先队列会自动按序列号排序
        packetQueue.add(packet);

        // 记录第一个包的时间戳作为基准
        if (firstTimestamp == -1) {
            firstTimestamp = packet.timestamp;
        }
    }

    public synchronized RTPPacket getPacket() {
        if (packetQueue.isEmpty()) {
            return null;
        }

        // 简单的时间戳缓冲：确保有最小缓冲时间，宁可延迟也不丢包
        if (firstTimestamp != -1) {
            RTPPacket nextPacket = packetQueue.peek();
            if (nextPacket != null) {
                // 计算当前缓冲的时间长度（基于时间戳）
                int timestampDiff = nextPacket.timestamp - firstTimestamp;
                double bufferTimeMs = (timestampDiff * 1000.0) / sampleRate;

                // 如果缓冲时间不足最小要求，等待更多包
                if (bufferTimeMs < minBufferMs && packetQueue.size() < 3) {
                    return null; // 等待更多包，确保不丢包
                }
            }
        }

        // 返回下一个包
        return packetQueue.poll();
    }
    
    public synchronized int getBufferSize() {
        return packetQueue.size();
    }


}
