package com.interview.yumshu;

import android.media.AudioAttributes;
import android.media.AudioFormat;
import android.media.AudioManager;
import android.media.AudioTrack;
import android.util.Base64;
import android.util.Log;

import com.facebook.react.bridge.Promise;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.bridge.ReadableMap;

import javax.annotation.Nonnull;

public class EnhancedAudioStreamPlayerModule extends ReactContextBaseJavaModule {

    private static final String TAG = "EnhancedAudioPlayer";
    private AudioTrack audioTrack;
    private AdaptiveJitterBuffer jitterBuffer;
    private Thread streamingThread;
    private volatile boolean isStreaming = false;
    private float volumeGain = 1.5f;


    public EnhancedAudioStreamPlayerModule(ReactApplicationContext reactContext) {
        super(reactContext);
    }

    @Nonnull
    @Override
    public String getName() {
        return "EnhancedAudioStreamPlayer";
    }

    @ReactMethod
    public void init(ReadableMap options, Promise promise) {
        try {
            // 确保使用传入的采样率，默认24000Hz而不是16000Hz
            this.sampleRate = options.hasKey("sampleRate") ? options.getInt("sampleRate") : 24000;
            int channelConfig = options.hasKey("channels") && options.getInt("channels") == 2 ?
                AudioFormat.CHANNEL_OUT_STEREO : AudioFormat.CHANNEL_OUT_MONO;
            int audioFormat = AudioFormat.ENCODING_PCM_16BIT;
            this.channels = (channelConfig == AudioFormat.CHANNEL_OUT_MONO) ? 1 : 2;

            Log.i(TAG, "初始化音频播放器 - 采样率: " + this.sampleRate + "Hz, 声道: " + this.channels);

            jitterBuffer = new AdaptiveJitterBuffer(this.sampleRate, this.channels);

            if (options.hasKey("volumeGain")) {
                volumeGain = (float) options.getDouble("volumeGain");
            }

            int bufferSize = AudioTrack.getMinBufferSize(this.sampleRate, channelConfig, audioFormat);
            // 优化AudioTrack缓冲区大小，与AJB缓冲区协调
            // 计算合适的缓冲区大小：约100ms的音频数据
            int optimalBufferSize = (this.sampleRate * this.channels * 2 * 100) / 1000; // 100ms缓冲
            int enhancedBufferSize = Math.max(bufferSize, Math.min(optimalBufferSize, 16384)); // 限制最大16KB

            AudioAttributes attributes = new AudioAttributes.Builder()
                    .setUsage(AudioAttributes.USAGE_VOICE_COMMUNICATION)
                    .setContentType(AudioAttributes.CONTENT_TYPE_SPEECH)
                    .build();

            AudioFormat format = new AudioFormat.Builder()
                    .setSampleRate(this.sampleRate)
                    .setChannelMask(channelConfig)
                    .setEncoding(audioFormat)
                    .build();

            audioTrack = new AudioTrack(attributes, format, enhancedBufferSize,
                AudioTrack.MODE_STREAM, AudioManager.AUDIO_SESSION_ID_GENERATE);

            // 使用AudioTrack原生音量控制，而不是应用层处理
            float nativeVolume = Math.min(volumeGain, AudioTrack.getMaxVolume());
            audioTrack.setVolume(nativeVolume);
            audioTrack.play();
            startStreaming();

            Log.d(TAG, "AudioTrack initialized with enhanced buffer size: " + enhancedBufferSize +
                ", native volume: " + nativeVolume);

            promise.resolve(null);
        } catch (Exception e) {
            Log.e(TAG, "Failed to initialize AudioTrack", e);
            promise.reject("INIT_ERROR", e);
        }
    }

    private int sequenceCounter = 0;
    private long timestampCounter = 0;
    private int sampleRate = 24000; // 默认采样率改为24kHz
    private int channels = 1; // 默认单声道

    @ReactMethod
    public void write(String base64Data, Promise promise) {
        long startTime = System.currentTimeMillis();

        if (jitterBuffer == null) {
            promise.reject("NOT_INITIALIZED", "JitterBuffer not initialized.");
            return;
        }
        try {
            byte[] data = Base64.decode(base64Data, Base64.NO_WRAP);
            long decodeTime = System.currentTimeMillis();

            // 生成递增的序列号和时间戳，确保AJB能正确处理
            synchronized (this) {
                sequenceCounter++;
                // 正确计算时间戳：基于实际音频时长（样本数）
                // data.length / 2 = 16位PCM的样本数，除以声道数得到每声道样本数
                int samplesPerChannel = (data.length / 2) / channels;
                timestampCounter += samplesPerChannel;
            }

            AdaptiveJitterBuffer.RTPPacket packet = new AdaptiveJitterBuffer.RTPPacket(
                data, (int)timestampCounter, sequenceCounter);

            jitterBuffer.addPacket(packet);
            long totalTime = System.currentTimeMillis() - startTime;
            long decodeOnlyTime = decodeTime - startTime;

            Log.d(TAG, String.format("🚀 直接写入优化: 总耗时=%dms, 解码=%dms, 数据大小=%d字节, 队列大小=%d",
                totalTime, decodeOnlyTime, data.length, jitterBuffer.getBufferSize()));

            promise.resolve(null);
        } catch (Exception e) {
            Log.e(TAG, "Failed to write audio data", e);
            promise.reject("WRITE_ERROR", e);
        }
    }

    @ReactMethod
    public void setVolumeGain(double gain, Promise promise) {
        try {
            volumeGain = (float) gain;
            // 直接设置AudioTrack的原生音量，而不是应用层处理
            if (audioTrack != null) {
                float nativeVolume = Math.min(volumeGain, AudioTrack.getMaxVolume());
                audioTrack.setVolume(nativeVolume);
                Log.d(TAG, "Native volume set to: " + nativeVolume);
            }
            promise.resolve(null);
        } catch (Exception e) {
            promise.reject("SET_VOLUME_ERROR", e);
        }
    }

    @ReactMethod
    public void stop(Promise promise) {
        stopStreaming();
        if (audioTrack != null) {
            try {
                audioTrack.stop();
                audioTrack.release();
                audioTrack = null;
                Log.d(TAG, "AudioTrack stopped and released");
                promise.resolve(null);
            } catch (Exception e) {
                Log.e(TAG, "Failed to stop AudioTrack", e);
                promise.reject("STOP_ERROR", e);
            }
        } else {
            promise.resolve(null);
        }
    }

    @ReactMethod
    public void getPendingBufferCount(Promise promise) {
        if (jitterBuffer == null) {
            promise.resolve(0);
            return;
        }
        promise.resolve(jitterBuffer.getBufferSize());
    }

    @ReactMethod
    public void getStats(Promise promise) {
        if (jitterBuffer == null) {
            promise.reject("NOT_INITIALIZED", "JitterBuffer not initialized.");
            return;
        }
        // In a real implementation, we would return a map with more stats
        promise.resolve(jitterBuffer.getBufferSize());
    }

    private void startStreaming() {
        isStreaming = true;
        streamingThread = new Thread(() -> {
            Log.d(TAG, "Audio streaming thread started - pure native control");

            while (isStreaming && audioTrack != null) {
                try {
                    AdaptiveJitterBuffer.RTPPacket packet = jitterBuffer.getPacket();
                    if (packet != null && packet.payload.length > 0) {
                        // 直接写入原始音频数据，让AudioTrack处理一切
                        // 音量控制、时序控制都交给AudioTrack原生处理
                        audioTrack.write(packet.payload, 0, packet.payload.length, AudioTrack.WRITE_BLOCKING);

                        // 完全交给原生组件控制，应用层不做任何处理

                    } else {
                        // 只在没有数据时才等待，且时间极短
                        Thread.sleep(1); // 最小等待时间
                    }
                } catch (InterruptedException e) {
                    Log.d(TAG, "Audio streaming thread interrupted");
                    Thread.currentThread().interrupt();
                    break;
                } catch (Exception e) {
                    Log.e(TAG, "Error in streaming thread", e);
                }
            }
            Log.d(TAG, "Audio streaming thread stopped");
        });
        streamingThread.start();
    }

    private void stopStreaming() {
        isStreaming = false;
        if (streamingThread != null) {
            streamingThread.interrupt();
            try {
                streamingThread.join();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
            streamingThread = null;
        }
    }


}
