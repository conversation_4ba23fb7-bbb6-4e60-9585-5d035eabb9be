# 面试助手

## 项目概述

这是一款基于AI的实时面试辅助移动应用，帮助技术岗位面试者通过手机听筒实时获取针对面试官提问的专业回答建议。

## 技术栈

- **前端**: React Native + TypeScript
- **后端**: Go + Gin + MySQL + Redis  
- **AI模型**: Google Gemini Live API
- **通信**: WebSocket + RESTful API
- **第三方服务**: 阿里云短信、微信支付、支付宝

## 项目结构

```
interviewMaster/
├── app/                    # React Native前端项目
│   ├── src/               # 源代码
│   ├── android/           # Android平台代码
│   ├── ios/               # iOS平台代码
│   └── package.json       # 前端依赖配置
├── backend/               # Go后端项目
│   ├── internal/          # 内部模块
│   ├── pkg/               # 公共包
│   ├── config.json        # 配置文件
│   └── main.go            # 主程序入口
├── docs/                  # 文档目录
├── scripts/               # 脚本文件
└── README.md
```

## 快速开始

### 环境要求

- Node.js >= 18
- Go >= 1.19
- MySQL >= 8.0
- Redis >= 6.0
- Android Studio / Xcode (移动端开发)

### 后端启动

```bash
# 1. 进入后端目录
cd backend

# 2. 安装依赖
go mod tidy

# 3. 配置数据库和服务
# 复制配置文件并修改
cp config.example.json config.json
# 编辑config.json，配置数据库、Redis、Gemini API等

# 4. 启动服务
go run main.go
```

### 前端启动

```bash
# 1. 进入前端目录
cd app

# 2. 安装依赖
npm install

# 3. 启动Metro服务器
npm start

# 4. 运行Android应用（新终端）
npm run android

# 5. 运行iOS应用
npm run ios
```

## 核心功能

### ✅ 已完成功能
- 手机号注册/登录系统
- 实时WebSocket通信
- Gemini AI对话集成
- 音频录制与播放
- 面试历史记录
- 用户余额管理
- 支付系统 (微信/支付宝)
- 管理员后台接口

### 🔧 技术特性
- JWT认证中间件
- 数据库迁移工具
- 音频格式转换
- WebSocket连接管理
- 优雅停机机制
- 统一错误处理
- 分页查询支持

## 配置说明

### 后端配置 (backend/config.json)

```json
{
  "server": {
    "host": "0.0.0.0",
    "port": "8090",
    "mode": "debug",
    "read_timeout": 60,
    "write_timeout": 60
  },
  "database": {
    "mysql": {
      "host": "localhost",
      "port": "3306",
      "username": "root",
      "password": "your_password",
      "database": "interview_master",
      "charset": "utf8mb4",
      "parse_time": true,
      "loc": "Local"
    },
    "redis": {
      "host": "localhost",
      "port": "6379",
      "password": "",
      "db": 0
    }
  },
  "jwt": {
    "secret": "your-jwt-secret",
    "expire_hours": 24
  },
  "gemini": {
    "api_key": "your_gemini_api_key",
    "model": "gemini-2.5-flash-preview-native-audio-dialog",
    "base_url": "https://generativelanguage.googleapis.com",
    "connection_mode": "auto",
    "timeout": 30
  },
  "aliyun_sms": {
    "access_key_id": "your-aliyun-access-key-id",
    "access_key_secret": "your-aliyun-access-key-secret",
    "sign_name": "your-sms-sign-name",
    "template_code": "your-sms-template-code"
  },
  "payment": {
    "wechat": {
      "app_id": "your_wechat_app_id",
      "mch_id": "your_merchant_id",
      "api_key": "your_api_key",
      "notify_url": "https://your-domain.com/api/v1/notify/wechat"
    },
    "alipay": {
      "app_id": "your_alipay_app_id",
      "private_key": "your_private_key",
      "public_key": "alipay_public_key",
      "notify_url": "https://your-domain.com/api/v1/notify/alipay"
    }
  }
}
```

### 前端配置 (app/src/utils/config.ts)

前端配置通过代码文件管理，支持开发和生产环境的自动切换：

```typescript
const getBaseUrl = () => {
  if (__DEV__) {
    if (Platform.OS === 'android') {
      return 'http://**************:8090'; // Android模拟器/真机
    } else {
      return 'http://localhost:8090'; // iOS模拟器
    }
  }
  return 'http://**************:8090'; // 生产环境
};
```

**重要说明**: 
- 前后端都使用端口 `8090`，配置已保持一致
- 需要将 `**************` 替换为你的实际开发机器IP地址
- Docker部署时使用端口 `8080`，需要注意环境差异

## 部署指南

### Docker部署

```bash
# 构建并启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

### 生产环境部署

1. **后端部署**
   ```bash
   # 编译
   go build -o interview-master main.go
   
   # 运行
   ./interview-master
   ```

2. **前端打包**
   ```bash
   # Android
   cd app/android && ./gradlew assembleRelease
   
   # iOS
   cd app/ios && xcodebuild -workspace app.xcworkspace -scheme app archive
   ```

## 故障排除

### 常见问题

1. **Android应用无法连接后端**
   ```bash
   # 配置Android网络端口转发
   ./app/setup-android-network.sh
   ```
   
   **注意**: 脚本会设置端口转发 8090→8090 和 8081→8081

2. **Gemini API连接失败**
   - 检查API密钥是否正确
   - 确认网络连接（中国大陆需要代理）
   - 验证模型名称是否正确

3. **支付功能异常**
   - 检查微信/支付宝配置参数
   - 确认回调URL可访问
   - 验证签名算法

4. **数据库连接失败**
   - 检查MySQL服务状态
   - 确认数据库用户权限
   - 验证连接参数

### 调试命令

```bash
# 查看后端日志（如果logs目录存在）
tail -f backend/logs/app.log
# 或者直接查看控制台输出
go run main.go

# 重置React Native缓存
npx react-native start --reset-cache

# 清理Android构建
cd app/android && ./gradlew clean

# 检查设备连接
adb devices
```

## API文档

详细的API文档请参考 [API.md](API.md)

## 开发规范

- 使用TypeScript进行类型检查
- 遵循ESLint代码规范 (`npm run lint`)
- 提交前运行测试用例 (`npm test`)
- 使用语义化版本号 (当前版本: 0.0.1)

### 代码检查命令
```bash
# 前端代码检查
cd app
npm run lint

# 前端测试
npm test

# 后端代码格式化
cd backend
go fmt ./...
```

## 许可证

MIT License

## 技术支持

如遇到问题，请提供：
- 错误日志信息
- 环境配置详情
- 具体复现步骤

---

**让AI助力你的面试成功！** 🚀