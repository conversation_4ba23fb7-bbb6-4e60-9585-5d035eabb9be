# API 接口文档

## 基础信息

- **Base URL**: `http://localhost:8090/api/v1`
- **认证方式**: JWT <PERSON>er <PERSON>
- **数据格式**: JSON
- **字符编码**: UTF-8

## 通用响应格式

### 成功响应
```json
{
  "code": 200,
  "message": "success",
  "data": {}
}
```

### 错误响应
```json
{
  "code": 400,
  "message": "error message",
  "data": null
}
```

## 认证接口

### 发送短信验证码
```http
POST /auth/sms
Content-Type: application/json

{
  "phone": "13800138000"
}
```

### 用户登录
```http
POST /auth/login
Content-Type: application/json

{
  "phone": "13800138000",
  "code": "123456"
}
```

**响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "id": 1,
      "phone": "13800138000",
      "nickname": "用户昵称",
      "balance": 100
    }
  }
}
```

### 刷新Token
```http
POST /auth/refresh
Content-Type: application/json

{
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

### 用户登出
```http
POST /auth/logout
Authorization: Bearer {token}
```

### 验证Token
```http
GET /auth/verify
Authorization: Bearer {token}
```

## 用户接口

### 获取用户信息
```http
GET /user/info
Authorization: Bearer {token}
```

### 更新用户信息
```http
PUT /user/info
Authorization: Bearer {token}
Content-Type: application/json

{
  "nickname": "新昵称",
  "avatar": "头像URL"
}
```

### 获取用户余额
```http
GET /user/balance
Authorization: Bearer {token}
```

### 消费用户余额
```http
POST /user/consume
Authorization: Bearer {token}
Content-Type: application/json

{
  "amount": 10,
  "reason": "面试服务"
}
```

## Gemini AI接口

### 创建临时令牌
```http
POST /gemini/ephemeral-token
Authorization: Bearer {token}
```

**响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "ephemeral_token": "auth_tokens/xxx...",
    "expires_at": "2024-01-01T12:00:00Z",
    "config": {
      "model": "gemini-live-2.5-flash-preview",
      "base_url": "wss://generativelanguage.googleapis.com"
    }
  }
}
```

### WebSocket代理连接
```http
GET /gemini/ws-proxy?token={jwt_token}
Upgrade: websocket
Connection: Upgrade
```

## 商品接口

### 获取商品列表
```http
GET /product/list
```

**响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": 1,
      "name": "基础套餐",
      "description": "包含100次面试服务",
      "price": 9900,
      "credits": 100,
      "status": "active"
    }
  ]
}
```

## 订单接口

### 创建订单
```http
POST /order/create
Authorization: Bearer {token}
Content-Type: application/json

{
  "product_id": 1,
  "quantity": 1
}
```

### 获取用户订单列表
```http
GET /order/list?page=1&limit=10
Authorization: Bearer {token}
```

### 获取订单详情
```http
GET /order/{order_no}
Authorization: Bearer {token}
```

### 获取订单状态
```http
GET /order/{order_no}/status
Authorization: Bearer {token}
```

### 取消订单
```http
POST /order/{order_no}/cancel
Authorization: Bearer {token}
```

## 支付接口

### 获取支付方式
```http
GET /payment/methods
Authorization: Bearer {token}
```

### 微信支付
```http
POST /payment/wechat/pay
Authorization: Bearer {token}
Content-Type: application/json

{
  "order_no": "ORDER20240101001"
}
```

**响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "appid": "wx1234567890abcdef",
    "partnerid": "1234567890",
    "prepayid": "wx123456789012345678901234567890",
    "package": "Sign=WXPay",
    "noncestr": "random_string",
    "timestamp": "1640995200",
    "sign": "signature_string"
  }
}
```

### 支付宝支付
```http
POST /payment/alipay/pay
Authorization: Bearer {token}
Content-Type: application/json

{
  "order_no": "ORDER20240101001"
}
```

**响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "order_string": "alipay_sdk=alipay-sdk-java-4.22.110.ALL&charset=utf-8..."
  }
}
```

## 面试接口

### 开始面试
```http
POST /interview/start
Authorization: Bearer {token}
Content-Type: application/json

{
  "position": "前端工程师",
  "company": "某科技公司",
  "difficulty": "中级"
}
```

### 结束面试
```http
POST /interview/end
Authorization: Bearer {token}
Content-Type: application/json

{
  "session_id": "session_123456",
  "duration": 1800,
  "feedback": "面试表现良好"
}
```

### 获取面试历史
```http
GET /interview/history?page=1&limit=10
Authorization: Bearer {token}
```

### 获取面试统计
```http
GET /interview/stats
Authorization: Bearer {token}
```

**响应**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total_interviews": 50,
    "total_duration": 90000,
    "average_score": 85,
    "recent_interviews": []
  }
}
```

### 更新面试反馈
```http
PUT /interview/feedback/{id}
Authorization: Bearer {token}
Content-Type: application/json

{
  "rating": 5,
  "feedback": "AI回答很有帮助"
}
```

## 管理员接口

### 获取用户列表
```http
GET /admin/user/list?page=1&limit=10
Authorization: Bearer {admin_token}
```

### 更新用户余额
```http
PUT /admin/user/{id}/balance
Authorization: Bearer {admin_token}
Content-Type: application/json

{
  "balance": 200,
  "reason": "管理员调整"
}
```

### 删除用户
```http
DELETE /admin/user/{id}
Authorization: Bearer {admin_token}
```

### 获取订单列表
```http
GET /admin/order/list?page=1&limit=10&status=paid
Authorization: Bearer {admin_token}
```

### 更新订单状态
```http
PUT /admin/order/{order_no}/status
Authorization: Bearer {admin_token}
Content-Type: application/json

{
  "status": "completed",
  "reason": "管理员操作"
}
```

## 配置接口

### 更新Gemini配置
```http
PUT /config/gemini
Authorization: Bearer {admin_token}
Content-Type: application/json

{
  "api_key": "new_api_key",
  "model": "gemini-live-2.5-flash-preview",
  "base_url": "https://generativelanguage.googleapis.com"
}
```

## 支付回调接口

### 微信支付回调
```http
POST /notify/wechat
Content-Type: application/xml

<xml>
  <appid><![CDATA[wx1234567890abcdef]]></appid>
  <mch_id><![CDATA[1234567890]]></mch_id>
  <out_trade_no><![CDATA[ORDER20240101001]]></out_trade_no>
  <result_code><![CDATA[SUCCESS]]></result_code>
  <!-- 其他微信回调参数 -->
</xml>
```

### 支付宝支调
```http
POST /notify/alipay
Content-Type: application/x-www-form-urlencoded

app_id=2021001234567890&out_trade_no=ORDER20240101001&trade_status=TRADE_SUCCESS&...
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未授权/Token无效 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 409 | 资源冲突 |
| 429 | 请求频率限制 |
| 500 | 服务器内部错误 |

## WebSocket接口

### Gemini Live连接
```javascript
// 连接WebSocket
const ws = new WebSocket('ws://localhost:8090/api/v1/gemini/ws-proxy?token=your_jwt_token');

// 发送音频数据
ws.send(JSON.stringify({
  type: 'audio',
  data: base64AudioData
}));

// 接收AI响应
ws.onmessage = function(event) {
  const response = JSON.parse(event.data);
  console.log('AI响应:', response);
};
```

## 限流说明

- 短信验证码: 1分钟内最多1次，1小时内最多5次
- 登录接口: 1分钟内最多5次
- AI对话: 每用户并发连接数限制为3个
- 支付接口: 1分钟内最多10次

## 安全说明

- 所有接口支持HTTPS
- JWT Token有效期为24小时
- Refresh Token有效期为7天
- 支付回调使用签名验证
- 敏感操作需要二次验证

---

**注意**: 生产环境请使用HTTPS协议，并妥善保管API密钥和证书文件。