package middleware

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"

	"interviewmaster/pkg/jwt"
	"interviewmaster/internal/utils"
)

// WebSocketAuthMiddleware WebSocket认证中间件
// 支持从URL参数或Authorization头获取token
func WebSocketAuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		var token string

		// 首先尝试从Authorization头获取token（兼容HTTP请求）
		authHeader := c.GetHeader("Authorization")
		if authHeader != "" {
			parts := strings.SplitN(authHeader, " ", 2)
			if len(parts) == 2 && parts[0] == "Bearer" {
				token = parts[1]
			}
		}

		// 如果Authorization头中没有token，尝试从URL参数获取（WebSocket连接）
		if token == "" {
			// 尝试从access_token参数获取（临时令牌）
			token = c.Query("access_token")
			if token == "" {
				// 尝试从key参数获取（API密钥）
				token = c.Query("key")
			}
		}

		// 如果仍然没有token，返回认证错误
		if token == "" {
			utils.ErrorResponse(c, http.StatusUnauthorized, "缺少认证令牌")
			c.Abort()
			return
		}

		// 对于临时令牌（auth_tokens/开头），直接通过，因为它们已经在Google API层面验证
		if strings.HasPrefix(token, "auth_tokens/") {
			// 临时令牌无法解析为JWT，但我们需要用户ID
			// 这里我们需要从其他地方获取用户信息，或者修改临时令牌的获取逻辑
			// 暂时跳过JWT验证，但这需要在ProxyWebSocket中处理用户认证
			c.Set("token", token)
			c.Set("token_type", "ephemeral")
			c.Next()
			return
		}

		// 对于普通JWT token，进行解析验证
		claims, err := jwt.ParseToken(token)
		if err != nil {
			utils.ErrorResponse(c, http.StatusUnauthorized, "无效的认证令牌")
			c.Abort()
			return
		}

		// 将用户信息存储到上下文
		c.Set("user_id", claims.UserID)
		c.Set("phone", claims.Phone)
		c.Set("token", token)
		c.Set("token_type", "jwt")

		c.Next()
	}
}