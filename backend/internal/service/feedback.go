package service

import (
	"errors"

	"gorm.io/gorm"
	"interviewmaster/internal/model"
)

type FeedbackService struct {
	db *gorm.DB
}

func NewFeedbackService(db *gorm.DB) *FeedbackService {
	return &FeedbackService{db: db}
}

// CreateFeedback 创建用户反馈
func (s *FeedbackService) CreateFeedback(userID uint64, feedbackType, content, contactEmail string) (*model.Feedback, error) {
	// 验证反馈类型
	if feedbackType != model.FeedbackTypeSuggestion && 
	   feedbackType != model.FeedbackTypeBug && 
	   feedbackType != model.FeedbackTypeOther {
		return nil, errors.New("无效的反馈类型")
	}

	// 验证内容长度
	if len(content) < 10 {
		return nil, errors.New("反馈内容至少需要10个字符")
	}
	if len(content) > 2000 {
		return nil, errors.New("反馈内容不能超过2000个字符")
	}

	feedback := &model.Feedback{
		UserID:       userID,
		Type:         feedbackType,
		Content:      content,
		ContactEmail: contactEmail,
		Status:       model.FeedbackStatusPending,
	}

	if err := s.db.Create(feedback).Error; err != nil {
		return nil, err
	}

	return feedback, nil
}

// GetUserFeedbacks 获取用户的反馈列表
func (s *FeedbackService) GetUserFeedbacks(userID uint64, page, pageSize int) ([]model.Feedback, int64, error) {
	var feedbacks []model.Feedback
	var total int64

	// 计算总数
	if err := s.db.Model(&model.Feedback{}).Where("user_id = ?", userID).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	if err := s.db.Where("user_id = ?", userID).
		Order("created_at DESC").
		Limit(pageSize).
		Offset(offset).
		Find(&feedbacks).Error; err != nil {
		return nil, 0, err
	}

	return feedbacks, total, nil
}

// GetFeedbackByID 根据ID获取反馈详情
func (s *FeedbackService) GetFeedbackByID(id, userID uint64) (*model.Feedback, error) {
	var feedback model.Feedback
	if err := s.db.Where("id = ? AND user_id = ?", id, userID).First(&feedback).Error; err != nil {
		return nil, err
	}
	return &feedback, nil
}

// GetFAQs 获取常见问题列表
func (s *FeedbackService) GetFAQs(category string) ([]model.FAQ, error) {
	var faqs []model.FAQ
	query := s.db.Where("status = ?", model.FAQStatusPublished)
	
	if category != "" {
		query = query.Where("category = ?", category)
	}
	
	if err := query.Order("sort_order ASC, created_at DESC").Find(&faqs).Error; err != nil {
		return nil, err
	}

	return faqs, nil
}

// GetFAQCategories 获取FAQ分类列表
func (s *FeedbackService) GetFAQCategories() ([]string, error) {
	var categories []string
	if err := s.db.Model(&model.FAQ{}).
		Where("status = ?", model.FAQStatusPublished).
		Distinct("category").
		Pluck("category", &categories).Error; err != nil {
		return nil, err
	}
	return categories, nil
}

// IncrementFAQViewCount 增加FAQ查看次数
func (s *FeedbackService) IncrementFAQViewCount(id uint64) error {
	return s.db.Model(&model.FAQ{}).Where("id = ?", id).
		UpdateColumn("view_count", gorm.Expr("view_count + 1")).Error
}

// GetHelpArticles 获取帮助文章列表
func (s *FeedbackService) GetHelpArticles(category string) ([]model.HelpArticle, error) {
	var articles []model.HelpArticle
	query := s.db.Where("status = ?", model.HelpArticleStatusPublished)
	
	if category != "" {
		query = query.Where("category = ?", category)
	}
	
	if err := query.Order("sort_order ASC, created_at DESC").Find(&articles).Error; err != nil {
		return nil, err
	}

	return articles, nil
}

// GetHelpArticleByID 根据ID获取帮助文章详情
func (s *FeedbackService) GetHelpArticleByID(id uint64) (*model.HelpArticle, error) {
	var article model.HelpArticle
	if err := s.db.Where("id = ? AND status = ?", id, model.HelpArticleStatusPublished).
		First(&article).Error; err != nil {
		return nil, err
	}

	// 增加查看次数
	s.db.Model(&article).UpdateColumn("view_count", gorm.Expr("view_count + 1"))

	return &article, nil
}

// GetHelpCategories 获取帮助文章分类列表
func (s *FeedbackService) GetHelpCategories() ([]string, error) {
	var categories []string
	if err := s.db.Model(&model.HelpArticle{}).
		Where("status = ?", model.HelpArticleStatusPublished).
		Distinct("category").
		Pluck("category", &categories).Error; err != nil {
		return nil, err
	}
	return categories, nil
}