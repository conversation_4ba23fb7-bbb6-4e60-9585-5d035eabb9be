package service

import (
	"errors"
	"time"

	"gorm.io/gorm"

	"interviewmaster/internal/model"
	"interviewmaster/internal/utils"
	"interviewmaster/pkg/database"
)

// OrderService 订单服务
type OrderService struct {
	db *gorm.DB
}

// NewOrderService 创建订单服务实例
func NewOrderService() *OrderService {
	return &OrderService{
		db: database.GetDB(),
	}
}

// CreateOrder 创建订单
func (s *OrderService) CreateOrder(userID uint64, productID string) (*model.Order, error) {
	// 检查用户是否存在
	var user model.User
	if err := s.db.Where("id = ?", userID).First(&user).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("用户不存在")
		}
		return nil, err
	}

	// 检查商品是否存在
	var product model.Product
	if err := s.db.Where("id = ? AND status = 1", productID).First(&product).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("商品不存在或已下架")
		}
		return nil, err
	}

	// 生成订单号
	orderNo := utils.GenerateOrderNo()

	// 创建订单
	order := &model.Order{
		OrderNo:   orderNo,
		UserID:    userID,
		ProductID: productID,
		Amount:    product.Price,
		Status:    model.OrderStatusPending,
	}

	if err := s.db.Create(order).Error; err != nil {
		return nil, err
	}

	return order, nil
}

// GetOrderByOrderNo 根据订单号获取订单
func (s *OrderService) GetOrderByOrderNo(orderNo string) (*model.Order, error) {
	var order model.Order
	err := s.db.Preload("User").Preload("Product").Where("order_no = ?", orderNo).First(&order).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &order, nil
}

// GetUserOrders 获取用户订单列表
func (s *OrderService) GetUserOrders(userID uint64, page, pageSize int) ([]model.Order, int64, error) {
	var orders []model.Order
	var total int64

	offset := utils.GetOffset(page, pageSize)

	// 获取总数
	if err := s.db.Model(&model.Order{}).Where("user_id = ?", userID).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取订单列表
	err := s.db.Preload("Product").Where("user_id = ?", userID).
		Order("created_at DESC").
		Offset(offset).Limit(pageSize).
		Find(&orders).Error

	return orders, total, err
}

// PayOrder 支付订单
func (s *OrderService) PayOrder(orderNo, payType string) error {
	return s.db.Transaction(func(tx *gorm.DB) error {
		// 获取订单
		var order model.Order
		if err := tx.Where("order_no = ?", orderNo).First(&order).Error; err != nil {
			return err
		}

		// 检查订单状态
		if order.Status != model.OrderStatusPending {
			return errors.New("订单状态不正确")
		}

		// 获取商品信息
		var product model.Product
		if err := tx.Where("id = ?", order.ProductID).First(&product).Error; err != nil {
			return err
		}

		// 更新订单状态
		now := time.Now()
		order.Status = model.OrderStatusPaid
		order.PayType = payType
		order.PaidAt = &now

		if err := tx.Save(&order).Error; err != nil {
			return err
		}

		// 更新用户余额
		var user model.User
		if err := tx.Where("id = ?", order.UserID).First(&user).Error; err != nil {
			return err
		}

		// 根据商品类型增加用户余额
		switch product.Type {
		case model.ProductTypeCount:
			user.BalanceCount += product.Count
		case model.ProductTypeDuration:
			user.PaidTotalDuration += product.Duration
		case model.ProductTypeMonthly:
			// 包月类型，增加时长
			user.PaidTotalDuration += product.Duration
		}

		return tx.Save(&user).Error
	})
}

// CloseOrder 关闭订单
func (s *OrderService) CloseOrder(orderNo string) error {
	return s.db.Model(&model.Order{}).
		Where("order_no = ? AND status = ?", orderNo, model.OrderStatusPending).
		Update("status", model.OrderStatusClosed).Error
}

// ProductService 商品服务
type ProductService struct {
	db *gorm.DB
}

// NewProductService 创建商品服务实例
func NewProductService() *ProductService {
	return &ProductService{
		db: database.GetDB(),
	}
}

// GetProducts 获取商品列表
func (s *ProductService) GetProducts() ([]model.Product, error) {
	var products []model.Product
	err := s.db.Where("status = 1").Order("sort_order ASC, created_at ASC").Find(&products).Error
	return products, err
}

// GetProductByID 根据ID获取商品
func (s *ProductService) GetProductByID(id string) (*model.Product, error) {
	var product model.Product
	err := s.db.Where("id = ?", id).First(&product).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &product, nil
}

// CreateProduct 创建商品
func (s *ProductService) CreateProduct(product *model.Product) error {
	return s.db.Create(product).Error
}

// UpdateProduct 更新商品
func (s *ProductService) UpdateProduct(product *model.Product) error {
	return s.db.Save(product).Error
}

// DeleteProduct 删除商品（软删除，设置状态为下架）
func (s *ProductService) DeleteProduct(id string) error {
	return s.db.Model(&model.Product{}).Where("id = ?", id).Update("status", 0).Error
}
