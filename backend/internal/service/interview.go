package service

import (
	"errors"
	"time"

	"gorm.io/gorm"

	"interviewmaster/internal/model"
	"interviewmaster/internal/utils"
	"interviewmaster/pkg/database"
)

// InterviewService 面试服务
type InterviewService struct {
	db          *gorm.DB
	userService *UserService
}

// NewInterviewService 创建面试服务实例
func NewInterviewService() *InterviewService {
	return &InterviewService{
		db:          database.GetDB(),
		userService: NewUserService(),
	}
}

// CreateInterviewLog 创建面试日志
func (s *InterviewService) CreateInterviewLog(log *model.InterviewLog) error {
	return s.db.Create(log).Error
}

// GetInterviewLogByID 根据ID获取面试日志
func (s *InterviewService) GetInterviewLogByID(id uint64) (*model.InterviewLog, error) {
	var log model.InterviewLog
	err := s.db.Where("id = ?", id).First(&log).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &log, nil
}

// UpdateInterviewLogFeedback 更新面试日志反馈
func (s *InterviewService) UpdateInterviewLogFeedback(id uint64, userID uint64, feedback int8) error {
	// 验证反馈值
	if feedback < -1 || feedback > 1 {
		return errors.New("无效的反馈值")
	}

	return s.db.Model(&model.InterviewLog{}).
		Where("id = ? AND user_id = ?", id, userID).
		Update("user_feedback", feedback).Error
}

// GetInterviewLogsBySession 根据会话ID获取面试日志
func (s *InterviewService) GetInterviewLogsBySession(sessionID string) ([]model.InterviewLog, error) {
	var logs []model.InterviewLog
	err := s.db.Where("session_id = ?", sessionID).
		Order("created_at ASC").
		Find(&logs).Error
	return logs, err
}

// GetLatestLogBySessionID 根据会话ID获取最新的面试日志
func (s *InterviewService) GetLatestLogBySessionID(sessionID string) (*model.InterviewLog, error) {
	var log model.InterviewLog
	err := s.db.Where("session_id = ?", sessionID).Order("started_at DESC").First(&log).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &log, nil
}

// GetInterviewStats 获取面试统计数据
func (s *InterviewService) GetInterviewStats(userID uint64) (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// 总面试次数
	var totalCount int64
	if err := s.db.Model(&model.InterviewLog{}).Where("user_id = ?", userID).Count(&totalCount).Error; err != nil {
		return nil, err
	}
	stats["total_count"] = totalCount

	// 本月面试次数
	now := time.Now()
	monthStart := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
	var monthCount int64
	if err := s.db.Model(&model.InterviewLog{}).
		Where("user_id = ? AND created_at >= ?", userID, monthStart).
		Count(&monthCount).Error; err != nil {
		return nil, err
	}
	stats["month_count"] = monthCount

	// 今日面试次数
	dayStart := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	var todayCount int64
	if err := s.db.Model(&model.InterviewLog{}).
		Where("user_id = ? AND created_at >= ?", userID, dayStart).
		Count(&todayCount).Error; err != nil {
		return nil, err
	}
	stats["today_count"] = todayCount

	// 平均响应时间
	var avgResponseTime float64
	if err := s.db.Model(&model.InterviewLog{}).
		Where("user_id = ? AND response_time_ms > 0", userID).
		Select("AVG(response_time_ms)").
		Scan(&avgResponseTime).Error; err != nil {
		return nil, err
	}
	stats["avg_response_time"] = avgResponseTime

	// 用户反馈统计
	var positiveCount, negativeCount int64
	s.db.Model(&model.InterviewLog{}).
		Where("user_id = ? AND user_feedback = 1", userID).
		Count(&positiveCount)
	s.db.Model(&model.InterviewLog{}).
		Where("user_id = ? AND user_feedback = -1", userID).
		Count(&negativeCount)

	stats["positive_feedback"] = positiveCount
	stats["negative_feedback"] = negativeCount

	return stats, nil
}

// DeleteInterviewLog 删除面试日志
func (s *InterviewService) DeleteInterviewLog(id uint64, userID uint64) error {
	return s.db.Where("id = ? AND user_id = ?", id, userID).Delete(&model.InterviewLog{}).Error
}

// CleanOldInterviewLogs 清理旧的面试日志（保留最近N天的数据）
func (s *InterviewService) CleanOldInterviewLogs(days int) error {
	cutoffTime := time.Now().AddDate(0, 0, -days)
	return s.db.Where("created_at < ?", cutoffTime).Delete(&model.InterviewLog{}).Error
}

// GetInterviewLogsByDateRange 根据日期范围获取面试日志
func (s *InterviewService) GetInterviewLogsByDateRange(userID uint64, startDate, endDate time.Time, page, pageSize int) ([]model.InterviewLog, int64, error) {
	var logs []model.InterviewLog
	var total int64

	offset := utils.GetOffset(page, pageSize)

	query := s.db.Model(&model.InterviewLog{}).Where("user_id = ? AND created_at >= ? AND created_at <= ?", userID, startDate, endDate)

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取日志列表
	err := query.Order("created_at DESC").
		Offset(offset).Limit(pageSize).
		Find(&logs).Error

	return logs, total, err
}

// EndInterviewSession 结束面试会话并计算时长
func (s *InterviewService) EndInterviewSession(userID uint64, sessionID string) error {
	return s.db.Transaction(func(tx *gorm.DB) error {
		// 获取面试日志
		var log model.InterviewLog
		if err := tx.Where("user_id = ? AND session_id = ? AND ended_at IS NULL", userID, sessionID).First(&log).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return errors.New("面试会话不存在或已结束")
			}
			return err
		}

		// 计算面试时长
		now := time.Now()
		if log.StartedAt == nil {
			return errors.New("面试开始时间异常")
		}

		durationSeconds := uint32(now.Sub(*log.StartedAt).Seconds())

		// 获取用户信息以检查可用时长
		var user model.User
		if err := tx.Where("id = ?", userID).First(&user).Error; err != nil {
			return err
		}

		// 计算用户总剩余时长
		trialRemaining := user.FreeTrialTotalDuration - user.FreeTrialUsedDuration
		totalRemaining := trialRemaining + user.BalanceDuration

		// 如果实际使用时长超过了用户的总剩余时长，限制为总剩余时长
		if durationSeconds > totalRemaining {
			durationSeconds = totalRemaining
		}

		// 更新面试日志
		log.EndedAt = &now
		log.DurationSeconds = durationSeconds

		if err := tx.Save(&log).Error; err != nil {
			return err
		}

		// 扣除用户时长
		return s.deductUserDuration(tx, userID, durationSeconds)
	})
}

// CheckAndEndExpiredSessions 检查并结束超时的面试会话
func (s *InterviewService) CheckAndEndExpiredSessions() error {
	// 定义一个合理的面试最大时长，例如1小时，防止会话无限期挂起
	const maxInterviewDuration = 1 * time.Hour

	// 查找所有未结束且开始时间早于1小时前的会话
	var logs []model.InterviewLog
	cutoffTime := time.Now().Add(-maxInterviewDuration)
	if err := s.db.Where("ended_at IS NULL AND started_at < ?", cutoffTime).Find(&logs).Error; err != nil {
		return err
	}

	// 查找所有未结束的会话，以检查是否已用尽时长
	var allPendingLogs []model.InterviewLog
	if err := s.db.Where("ended_at IS NULL").Find(&allPendingLogs).Error; err != nil {
		return err
	}

	// 合并两个列表并去重
	logMap := make(map[uint64]model.InterviewLog)
	for _, log := range logs {
		logMap[log.ID] = log
	}
	for _, log := range allPendingLogs {
		logMap[log.ID] = log
	}

	now := time.Now()
	for _, log := range logMap {
		if log.StartedAt == nil {
			continue
		}

		// 获取用户信息
		var user model.User
		if err := s.db.Where("id = ?", log.UserID).First(&user).Error; err != nil {
			// 如果找不到用户，也应该结束这个会话，避免孤儿会话
			s.forceEndSession(log.ID)
			continue
		}

		// 计算用户总剩余时长
		trialRemaining := user.FreeTrialTotalDuration - user.FreeTrialUsedDuration
		totalRemaining := trialRemaining + user.BalanceDuration

		// 计算已经使用的时长
		usedDuration := uint32(now.Sub(*log.StartedAt).Seconds())
		sessionDuration := now.Sub(*log.StartedAt)

		// 检查两个条件：1. 使用时长是否耗尽 2. 是否超过了最大允许时长
		if usedDuration >= totalRemaining || sessionDuration > maxInterviewDuration {
			// 自动结束面试会话
			if err := s.EndInterviewSession(log.UserID, log.SessionID); err != nil {
				// 记录错误但继续处理其他会话
				continue
			}
		}
	}

	return nil
}

// forceEndSession 强制结束一个没有用户信息的会话
func (s *InterviewService) forceEndSession(logID uint64) {
	now := time.Now()
	s.db.Model(&model.InterviewLog{}).Where("id = ?", logID).Updates(map[string]interface{}{
		"ended_at":         &now,
		"duration_seconds": 0, // 因为无法计算用户时长，所以记为0
	})
}

// deductUserDuration 扣除用户时长
func (s *InterviewService) deductUserDuration(tx *gorm.DB, userID uint64, durationSeconds uint32) error {
	// 获取用户信息
	var user model.User
	if err := tx.Where("id = ?", userID).First(&user).Error; err != nil {
		return err
	}

	// 优先扣除试用时长
	trialRemaining := user.FreeTrialTotalDuration - user.FreeTrialUsedDuration
	if trialRemaining > 0 {
		trialDeduct := durationSeconds
		if trialDeduct > trialRemaining {
			trialDeduct = trialRemaining
		}

		user.FreeTrialUsedDuration += trialDeduct
		durationSeconds -= trialDeduct
	}

	// 如果还有剩余时长需要扣除，从付费时长中扣除
	if durationSeconds > 0 {
		if user.BalanceDuration < durationSeconds {
			return errors.New("剩余时长不足")
		}
		user.BalanceDuration -= durationSeconds
	}

	return tx.Save(&user).Error
}
