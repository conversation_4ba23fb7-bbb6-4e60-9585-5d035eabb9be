package service

import (
	"crypto/md5"
	"fmt"
	"net/http"
	"net/url"
	"sort"
	"strings"
	"time"

	"github.com/smartwalle/alipay/v3"
	"interviewmaster/internal/config"
	"interviewmaster/internal/model"
	"interviewmaster/internal/utils"
)

// PaymentService 支付服务
type PaymentService struct {
	orderService   *OrderService
	productService *ProductService
	userService    *UserService
	config         *config.PaymentConfig
	alipayClient   *alipay.Client
}

// NewPaymentService 创建支付服务实例
func NewPaymentService() *PaymentService {
	cfg := config.GlobalConfig
	if cfg == nil {
		panic("配置未初始化")
	}

	// 初始化支付宝客户端
	var alipayClient *alipay.Client
	if cfg.Payment.Alipay.AppID != "" && cfg.Payment.Alipay.PrivateKey != "" && 
		!strings.Contains(cfg.Payment.Alipay.PrivateKey, "...") { // 检查是否为示例配置
		var err error
		alipayClient, err = alipay.New(cfg.Payment.Alipay.AppID, cfg.Payment.Alipay.PrivateKey, cfg.Payment.Alipay.IsProduction)
		if err != nil {
			fmt.Printf("警告: 初始化支付宝客户端失败: %v\n", err)
			fmt.Println("请检查支付宝配置，支付宝支付功能将不可用")
			alipayClient = nil
		} else {
			// 设置支付宝公钥
			if cfg.Payment.Alipay.PublicKey != "" && !strings.Contains(cfg.Payment.Alipay.PublicKey, "...") {
				err = alipayClient.LoadAliPayPublicKey(cfg.Payment.Alipay.PublicKey)
				if err != nil {
					fmt.Printf("警告: 加载支付宝公钥失败: %v\n", err)
					fmt.Println("支付宝回调验证功能将不可用")
				}
			}
		}
	} else {
		fmt.Println("支付宝配置未完整配置，支付宝支付功能将不可用")
	}

	return &PaymentService{
		orderService:   NewOrderService(),
		productService: NewProductService(),
		userService:    NewUserService(),
		config:         &cfg.Payment,
		alipayClient:   alipayClient,
	}
}

// CreatePaymentOrder 创建支付订单
func (s *PaymentService) CreatePaymentOrder(userID uint64, productID string) (*model.Order, error) {
	// 检查商品是否存在
	product, err := s.productService.GetProductByID(productID)
	if err != nil {
		return nil, err
	}
	if product == nil {
		return nil, fmt.Errorf("商品不存在")
	}
	if product.Status != 1 {
		return nil, fmt.Errorf("商品已下架")
	}

	// 创建订单
	order, err := s.orderService.CreateOrder(userID, productID)
	if err != nil {
		return nil, err
	}

	return order, nil
}

// WeChatPayRequest 微信支付请求
type WeChatPayRequest struct {
	OrderNo     string  `json:"order_no"`
	Amount      float64 `json:"amount"`
	Description string  `json:"description"`
	ClientIP    string  `json:"client_ip"`
}

// WeChatPayResponse 微信支付响应
type WeChatPayResponse struct {
	PrepayID  string `json:"prepay_id"`
	CodeURL   string `json:"code_url,omitempty"` // 二维码URL，移动端不需要
	PaySign   string `json:"pay_sign"`
	TimeStamp string `json:"timestamp"`
	NonceStr  string `json:"nonce_str"`
	Package   string `json:"package"`
	SignType  string `json:"sign_type"`
	AppID     string `json:"app_id"`     // 移动端需要的参数
	PartnerID string `json:"partner_id"` // 商户号
}

// CreateWeChatPayment 创建微信支付
func (s *PaymentService) CreateWeChatPayment(orderNo string, clientIP string) (*WeChatPayResponse, error) {
	// 获取订单信息
	order, err := s.orderService.GetOrderByOrderNo(orderNo)
	if err != nil {
		return nil, err
	}
	if order == nil {
		return nil, fmt.Errorf("订单不存在")
	}
	if order.Status != model.OrderStatusPending {
		return nil, fmt.Errorf("订单状态不正确")
	}

	// 构建微信支付参数
	params := map[string]string{
		"appid":            s.config.WeChat.AppID,
		"mch_id":           s.config.WeChat.MchID,
		"nonce_str":        utils.GenerateSessionID(),
		"body":             fmt.Sprintf("面试助手-%s", order.Product.Name),
		"out_trade_no":     orderNo,
		"total_fee":        fmt.Sprintf("%.0f", order.Amount*100), // 转换为分
		"spbill_create_ip": clientIP,
		"notify_url":       s.config.WeChat.NotifyURL,
		"trade_type":       "APP", // 移动端APP支付
	}

	// 生成签名
	sign := s.generateWeChatSign(params)
	params["sign"] = sign

	// 构建XML请求体
	xmlBody := s.buildWeChatXML(params)

	// 发送请求到微信支付API
	resp, err := http.Post("https://api.mch.weixin.qq.com/pay/unifiedorder",
		"application/xml", strings.NewReader(xmlBody))
	if err != nil {
		return nil, fmt.Errorf("微信支付API请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 解析响应
	// 这里应该解析XML响应，为简化示例，返回模拟数据
	prepayID := "prepay_id_" + orderNo
	response := &WeChatPayResponse{
		PrepayID:  prepayID,
		TimeStamp: fmt.Sprintf("%d", time.Now().Unix()),
		NonceStr:  utils.GenerateSessionID(),
		Package:   "Sign=WXPay",
		SignType:  "MD5",
		AppID:     s.config.WeChat.AppID,
		PartnerID: s.config.WeChat.MchID,
	}

	// 生成支付签名
	response.PaySign = s.generateWeChatPaySign(response)

	return response, nil
}

// AlipayAppPayResponse 支付宝App支付响应
type AlipayAppPayResponse struct {
	OrderString string `json:"order_string"` // 移动端支付宝SDK需要的订单信息字符串
}

// CreateAlipayPayment 创建支付宝App支付
func (s *PaymentService) CreateAlipayPayment(orderNo string, returnURL string) (*AlipayAppPayResponse, error) {
	if s.alipayClient == nil {
		return nil, fmt.Errorf("支付宝客户端未初始化")
	}

	// 获取订单信息
	order, err := s.orderService.GetOrderByOrderNo(orderNo)
	if err != nil {
		return nil, err
	}
	if order == nil {
		return nil, fmt.Errorf("订单不存在")
	}
	if order.Status != model.OrderStatusPending {
		return nil, fmt.Errorf("订单状态不正确")
	}

	// 创建支付宝App支付请求
	var p = alipay.TradeAppPay{}
	p.NotifyURL = s.config.Alipay.NotifyURL
	p.OutTradeNo = orderNo
	p.TotalAmount = fmt.Sprintf("%.2f", order.Amount)
	p.Subject = fmt.Sprintf("面试助手-%s", order.Product.Name)
	p.ProductCode = "QUICK_MSECURITY_PAY"
	
	// 设置超时时间
	p.TimeoutExpress = "30m"

	// 调用支付宝API生成订单字符串
	orderString, err := s.alipayClient.TradeAppPay(p)
	if err != nil {
		return nil, fmt.Errorf("创建支付宝支付失败: %v", err)
	}

	response := &AlipayAppPayResponse{
		OrderString: orderString,
	}

	return response, nil
}

// HandleWeChatNotify 处理微信支付回调
func (s *PaymentService) HandleWeChatNotify(xmlData string) error {
	// 解析XML数据
	// 这里应该解析实际的XML数据，为简化示例，假设解析成功

	// 验证签名
	// if !s.verifyWeChatSign(params) {
	//     return fmt.Errorf("签名验证失败")
	// }

	// 模拟解析结果
	orderNo := "IM20240101000001" // 从XML中解析

	// 处理支付成功
	return s.orderService.PayOrder(orderNo, "wechat")
}

// HandleAlipayNotify 处理支付宝支付回调
func (s *PaymentService) HandleAlipayNotify(params map[string]string) error {
	if s.alipayClient == nil {
		return fmt.Errorf("支付宝客户端未初始化")
	}

	// 将map转换为url.Values
	values := url.Values{}
	for k, v := range params {
		values.Set(k, v)
	}

	// 验证签名
	err := s.alipayClient.VerifySign(values)
	if err != nil {
		return fmt.Errorf("签名验证失败: %v", err)
	}

	orderNo := params["out_trade_no"]
	tradeStatus := params["trade_status"]

	// 检查交易状态
	if tradeStatus == "TRADE_SUCCESS" || tradeStatus == "TRADE_FINISHED" {
		return s.orderService.PayOrder(orderNo, "alipay")
	}

	return nil
}

// generateWeChatSign 生成微信支付签名
func (s *PaymentService) generateWeChatSign(params map[string]string) string {
	// 排序参数
	var keys []string
	for k := range params {
		if k != "sign" && params[k] != "" {
			keys = append(keys, k)
		}
	}
	sort.Strings(keys)

	// 构建签名字符串
	var signStr strings.Builder
	for i, k := range keys {
		if i > 0 {
			signStr.WriteString("&")
		}
		signStr.WriteString(k)
		signStr.WriteString("=")
		signStr.WriteString(params[k])
	}
	signStr.WriteString("&key=")
	signStr.WriteString(s.config.WeChat.APIKey)

	// MD5签名
	hash := md5.Sum([]byte(signStr.String()))
	return fmt.Sprintf("%X", hash)
}

// generateWeChatPaySign 生成微信支付签名
func (s *PaymentService) generateWeChatPaySign(resp *WeChatPayResponse) string {
	params := map[string]string{
		"appid":     resp.AppID,
		"partnerid": resp.PartnerID,
		"prepayid":  resp.PrepayID,
		"package":   resp.Package,
		"noncestr":  resp.NonceStr,
		"timestamp": resp.TimeStamp,
	}
	return s.generateWeChatSign(params)
}

// buildWeChatXML 构建微信支付XML
func (s *PaymentService) buildWeChatXML(params map[string]string) string {
	var xml strings.Builder
	xml.WriteString("<xml>")
	for k, v := range params {
		xml.WriteString(fmt.Sprintf("<%s><![CDATA[%s]]></%s>", k, v, k))
	}
	xml.WriteString("</xml>")
	return xml.String()
}


// GetPaymentMethods 获取支付方式
func (s *PaymentService) GetPaymentMethods() []map[string]interface{} {
	return []map[string]interface{}{
		{
			"type":        "wechat",
			"name":        "微信支付",
			"icon":        "wechat",
			"description": "使用微信APP支付",
			"enabled":     true,
		},
		{
			"type":        "alipay",
			"name":        "支付宝",
			"icon":        "alipay",
			"description": "使用支付宝APP支付",
			"enabled":     true,
		},
	}
}
