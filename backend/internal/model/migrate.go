package model

import (
	"log"

	"gorm.io/gorm"
)

// AutoMigrate 自动迁移数据库表结构
func AutoMigrate(db *gorm.DB) error {
	log.Println("开始数据库迁移...")

	// 强制修正 faqs 表的字符集
	if db.Migrator().HasTable(&FAQ{}) {
		log.Println("正在检查和修正 faqs 表的字符集...")
		if err := db.Exec("ALTER TABLE `faqs` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;").Error; err != nil {
			log.Printf("修正 faqs 表字符集失败: %v", err)
			return err
		}
		if err := db.Exec("ALTER TABLE `faqs` MODIFY `category` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;").Error; err != nil {
			log.Printf("修正 faqs.category 列字符集失败: %v", err)
			return err
		}
		log.Println("faqs 表字符集修正完成。")
	}

	// 强制修正 help_articles 表的字符集
	if db.Migrator().HasTable(&HelpArticle{}) {
		log.Println("正在检查和修正 help_articles 表的字符集...")
		if err := db.Exec("ALTER TABLE `help_articles` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;").Error; err != nil {
			log.Printf("修正 help_articles 表字符集失败: %v", err)
			return err
		}
		// 修正所有可能包含中文的字段
		if err := db.Exec("ALTER TABLE `help_articles` MODIFY `title` VARCHAR(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci, MODIFY `content` TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci, MODIFY `category` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci, MODIFY `tags` VARCHAR(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;").Error; err != nil {
			log.Printf("修正 help_articles 表列字符集失败: %v", err)
			return err
		}
		log.Println("help_articles 表字符集修正完成。")
	}

	// 迁移所有表
	err := db.AutoMigrate(
		&User{},
		&Order{},
		&InterviewLog{},
		&SMSCode{},
		&Product{},
		&Admin{},
		&Feedback{},
		&FAQ{},
		&HelpArticle{},
	)

	if err != nil {
		log.Printf("数据库迁移失败: %v", err)
		return err
	}

	log.Println("数据库迁移完成")
	return nil
}

// InitDefaultData 初始化默认数据
func InitDefaultData(db *gorm.DB) error {
	log.Println("开始初始化默认数据...")

	// 检查并创建默认商品
	if err := initDefaultProducts(db); err != nil {
		return err
	}

	// 检查并创建默认管理员
	if err := initDefaultAdmin(db); err != nil {
		return err
	}

	// 检查并创建默认FAQ和帮助文章
	if err := initDefaultHelpContent(db); err != nil {
		return err
	}

	log.Println("默认数据初始化完成")
	return nil
}

// initDefaultProducts 初始化默认商品
func initDefaultProducts(db *gorm.DB) error {
	// 检查是否已有商品数据
	var count int64
	db.Model(&Product{}).Count(&count)
	if count > 0 {
		log.Println("商品数据已存在，跳过初始化")
		return nil
	}

	// 创建默认商品（时长制套餐）
	products := []Product{
		{
			ID:          "duration_30",
			Name:        "基础套餐",
			Description: "30分钟面试时长，适合短期使用",
			Price:       45.00,
			Count:       0,
			Duration:    1800, // 30分钟 = 1800秒
			Type:        ProductTypeDuration,
			Status:      1,
			SortOrder:   1,
		},
		{
			ID:          "duration_60",
			Name:        "标准套餐",
			Description: "60分钟面试时长，适合日常练习",
			Price:       69.90,
			Count:       0,
			Duration:    3600, // 60分钟 = 3600秒
			Type:        ProductTypeDuration,
			Status:      1,
			SortOrder:   2,
		},
	}

	for _, product := range products {
		if err := db.Create(&product).Error; err != nil {
			log.Printf("创建商品失败: %v", err)
			return err
		}
	}

	log.Println("默认商品创建完成")
	return nil
}

// initDefaultAdmin 初始化默认管理员
func initDefaultAdmin(db *gorm.DB) error {
	// 检查是否已有管理员数据
	var count int64
	db.Model(&Admin{}).Count(&count)
	if count > 0 {
		log.Println("管理员数据已存在，跳过初始化")
		return nil
	}

	// 创建默认管理员账号
	// 密码: admin123 (实际使用时应该使用bcrypt加密)
	admin := Admin{
		Username: "admin",
		Password: "$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi", // admin123
		Name:     "系统管理员",
		Email:    "<EMAIL>",
		Role:     "super_admin",
		Status:   1,
	}

	if err := db.Create(&admin).Error; err != nil {
		log.Printf("创建默认管理员失败: %v", err)
		return err
	}

	log.Println("默认管理员创建完成")
	return nil
}

// initDefaultHelpContent 初始化默认帮助内容
func initDefaultHelpContent(db *gorm.DB) error {
	// 检查并创建默认FAQ
	var faqCount int64
	db.Model(&FAQ{}).Count(&faqCount)
	if faqCount == 0 {
		faqs := []FAQ{
			{
				Question:  "如何开始使用面试助手？",
				Answer:    "1. 首先注册并登录账户\n2. 购买时长套餐或使用免费试用\n3. 进入面试页面，点击开始面试\n4. 戴上耳机，开始模拟面试",
				Category:  "使用指南",
				SortOrder: 1,
				Status:    FAQStatusPublished,
			},
			{
				Question:  "为什么听不到AI的声音？",
				Answer:    "请检查以下几点：\n1. 确保手机音量已开启\n2. 检查耳机连接是否正常\n3. 确认应用已获得麦克风权限\n4. 尝试重启应用",
				Category:  "常见问题",
				SortOrder: 2,
				Status:    FAQStatusPublished,
			},
			{
				Question:  "如何购买更多面试时长？",
				Answer:    "1. 点击首页的\"购买时长\"按钮\n2. 选择适合的套餐\n3. 选择支付方式（微信/支付宝）\n4. 完成支付后时长会自动到账",
				Category:  "付费相关",
				SortOrder: 3,
				Status:    FAQStatusPublished,
			},
			{
				Question:  "面试过程中如何获得最佳体验？",
				Answer:    "1. 选择安静的环境进行面试\n2. 使用有线耳机以获得更好的音质\n3. 确保网络连接稳定\n4. 说话时语速适中，发音清晰",
				Category:  "使用技巧",
				SortOrder: 4,
				Status:    FAQStatusPublished,
			},
			{
				Question:  "支持哪些面试类型？",
				Answer:    "目前支持以下面试类型：\n1. 技术面试（前端、后端、算法等）\n2. 行为面试（团队合作、项目经验等）\n3. 综合面试（技术+行为结合）\n4. 自定义面试场景",
				Category:  "功能介绍",
				SortOrder: 5,
				Status:    FAQStatusPublished,
			},
		}

		for _, faq := range faqs {
			if err := db.Create(&faq).Error; err != nil {
				log.Printf("创建FAQ失败: %v", err)
				return err
			}
		}
		log.Println("默认FAQ创建完成")
	}

	// 检查并创建默认帮助文章
	var articleCount int64
	db.Model(&HelpArticle{}).Count(&articleCount)
	if articleCount == 0 {
		// 需要先获取管理员ID
		var admin Admin
		if err := db.First(&admin).Error; err != nil {
			log.Printf("获取管理员信息失败: %v", err)
			return err
		}

		articles := []HelpArticle{
			{
				Title:     "面试助手完整使用指南",
				Content:   "# 面试助手使用指南\n\n## 快速开始\n\n### 1. 注册登录\n首次使用需要注册账户，支持手机号验证码登录。\n\n### 2. 购买套餐\n- 新用户享有免费试用时长\n- 可根据需要购买不同时长的套餐\n- 支持微信支付和支付宝支付\n\n### 3. 开始面试\n- 点击首页\"开始面试\"按钮\n- 选择面试类型和难度\n- 戴上耳机，确保环境安静\n- 开始与AI面试官对话\n\n## 高级功能\n\n### 语音设置\n可以在设置中选择不同的AI声音，包括男声和女声选项。\n\n### 面试历史\n查看历史面试记录，分析面试表现，持续改进。\n\n### 反馈系统\n对AI回答进行评价，帮助我们改进服务质量。",
				Category:  "使用指南",
				Tags:      "新手指南,完整教程,使用技巧",
				SortOrder: 1,
				Status:    HelpArticleStatusPublished,
				AuthorID:  admin.ID,
			},
			{
				Title:     "常见技术面试问题及回答技巧",
				Content:   "# 技术面试问题汇总\n\n## 前端开发\n\n### JavaScript基础\n- 闭包的概念和应用\n- 异步编程（Promise、async/await）\n- 事件循环机制\n- 原型链和继承\n\n### React/Vue框架\n- 组件生命周期\n- 状态管理\n- 虚拟DOM原理\n- 性能优化技巧\n\n## 后端开发\n\n### 数据库\n- SQL查询优化\n- 索引设计原则\n- 事务处理\n- 数据库设计范式\n\n### 系统设计\n- 缓存策略\n- 负载均衡\n- 微服务架构\n- 分布式系统\n\n## 算法与数据结构\n\n### 常见算法\n- 排序算法\n- 搜索算法\n- 动态规划\n- 贪心算法\n\n### 数据结构\n- 数组和链表\n- 栈和队列\n- 树和图\n- 哈希表\n\n## 回答技巧\n\n1. **STAR法则**：Situation（情况）、Task（任务）、Action（行动）、Result（结果）\n2. **具体化**：用具体的项目经验和数据支撑回答\n3. **逻辑清晰**：按照逻辑顺序组织回答内容\n4. **主动提问**：适时向面试官提出相关问题",
				Category:  "面试技巧",
				Tags:      "技术面试,面试题目,回答技巧",
				SortOrder: 2,
				Status:    HelpArticleStatusPublished,
				AuthorID:  admin.ID,
			},
			{
				Title:     "面试心理准备和注意事项",
				Content:   "# 面试心理准备指南\n\n## 面试前准备\n\n### 心理建设\n- 保持自信，相信自己的能力\n- 做好充分的技术和项目准备\n- 了解目标公司和职位要求\n- 准备常见问题的回答\n\n### 技术准备\n- 复习相关技术知识点\n- 准备项目介绍和技术难点\n- 练习算法题和系统设计\n- 了解行业最新技术趋势\n\n## 面试中表现\n\n### 沟通技巧\n- 语速适中，表达清晰\n- 积极互动，展现团队合作精神\n- 诚实回答，不懂就说不懂\n- 展现学习能力和解决问题的思路\n\n### 注意事项\n- 保持良好的仪表和姿态\n- 准时参加面试\n- 带好相关资料和作品\n- 关闭手机或调至静音\n\n## 面试后跟进\n\n### 总结反思\n- 记录面试过程和问题\n- 分析自己的表现\n- 总结经验和不足\n- 为下次面试做准备\n\n### 后续跟进\n- 发送感谢邮件\n- 耐心等待结果\n- 继续其他面试机会\n- 保持积极心态",
				Category:  "面试技巧",
				Tags:      "心理准备,面试技巧,注意事项",
				SortOrder: 3,
				Status:    HelpArticleStatusPublished,
				AuthorID:  admin.ID,
			},
		}

		for _, article := range articles {
			if err := db.Create(&article).Error; err != nil {
				log.Printf("创建帮助文章失败: %v", err)
				return err
			}
		}
		log.Println("默认帮助文章创建完成")
	}

	return nil
}

// DropAllTables 删除所有表（谨慎使用）
func DropAllTables(db *gorm.DB) error {
	log.Println("警告：正在删除所有数据库表...")

	// 按依赖关系逆序删除表
	tables := []interface{}{
		&InterviewLog{},
		&Order{},
		&SMSCode{},
		&User{},
		&Product{},
		&Admin{},
	}

	for _, table := range tables {
		if err := db.Migrator().DropTable(table); err != nil {
			log.Printf("删除表失败: %v", err)
			return err
		}
	}

	log.Println("所有表删除完成")
	return nil
}

// ResetDatabase 重置数据库（删除所有表并重新创建）
func ResetDatabase(db *gorm.DB) error {
	log.Println("开始重置数据库...")

	// 删除所有表
	if err := DropAllTables(db); err != nil {
		return err
	}

	// 重新创建表
	if err := AutoMigrate(db); err != nil {
		return err
	}

	// 初始化默认数据
	if err := InitDefaultData(db); err != nil {
		return err
	}

	log.Println("数据库重置完成")
	return nil
}
