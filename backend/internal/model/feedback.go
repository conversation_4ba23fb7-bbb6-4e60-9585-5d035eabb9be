package model

import (
	"time"
)

// Feedback 用户反馈模型
type Feedback struct {
	ID           uint64     `gorm:"primaryKey;autoIncrement" json:"id"`
	UserID       uint64     `gorm:"type:bigint unsigned;not null;index;comment:用户ID" json:"user_id"`
	Type         string     `gorm:"type:varchar(20);not null;comment:反馈类型 suggestion/bug/other" json:"type"`
	Content      string     `gorm:"type:text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;not null;comment:反馈内容" json:"content"`
	ContactEmail string     `gorm:"type:varchar(100);comment:联系邮箱" json:"contact_email"`
	Status       uint8      `gorm:"type:tinyint;not null;default:0;index;comment:处理状态 0:待处理 1:处理中 2:已处理 3:已关闭" json:"status"`
	AdminReply   string     `gorm:"type:text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;comment:管理员回复" json:"admin_reply"`
	AdminID      *uint64    `gorm:"type:bigint unsigned;comment:处理管理员ID" json:"admin_id"`
	ProcessedAt  *time.Time `gorm:"comment:处理时间" json:"processed_at"`
	CreatedAt    time.Time  `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt    time.Time  `gorm:"autoUpdateTime" json:"updated_at"`

	// 关联
	User  User   `gorm:"foreignKey:UserID" json:"user,omitempty"`
	Admin *Admin `gorm:"foreignKey:AdminID" json:"admin,omitempty"`
}

// TableName 指定表名
func (Feedback) TableName() string {
	return "feedbacks"
}

// 反馈类型常量
const (
	FeedbackTypeSuggestion = "suggestion" // 功能建议
	FeedbackTypeBug        = "bug"        // 问题反馈
	FeedbackTypeOther      = "other"      // 其他
)

// 反馈状态常量
const (
	FeedbackStatusPending    = 0 // 待处理
	FeedbackStatusProcessing = 1 // 处理中
	FeedbackStatusProcessed  = 2 // 已处理
	FeedbackStatusClosed     = 3 // 已关闭
)

// HelpArticle 帮助文章模型
type HelpArticle struct {
	ID        uint64    `gorm:"primaryKey;autoIncrement" json:"id"`
	Title     string    `gorm:"type:varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;not null;comment:标题" json:"title"`
	Content   string    `gorm:"type:text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;not null;comment:内容" json:"content"`
	Category  string    `gorm:"type:varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;not null;index;comment:分类" json:"category"`
	Tags      string    `gorm:"type:varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;comment:标签，逗号分隔" json:"tags"`
	ViewCount uint32    `gorm:"type:int unsigned;default:0;comment:查看次数" json:"view_count"`
	SortOrder int       `gorm:"type:int;default:0;index;comment:排序" json:"sort_order"`
	Status    uint8     `gorm:"type:tinyint;not null;default:1;index;comment:状态 0:下架 1:发布" json:"status"`
	AuthorID  uint64    `gorm:"type:bigint unsigned;not null;comment:作者ID" json:"author_id"`
	CreatedAt time.Time `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt time.Time `gorm:"autoUpdateTime" json:"updated_at"`

	// 关联
	Author Admin `gorm:"foreignKey:AuthorID" json:"author,omitempty"`
}

// TableName 指定表名
func (HelpArticle) TableName() string {
	return "help_articles"
}

// FAQ 常见问题模型
type FAQ struct {
	ID        uint64    `gorm:"primaryKey;autoIncrement" json:"id"`
	Question  string    `gorm:"type:varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;not null;comment:问题" json:"question"`
	Answer    string    `gorm:"type:text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;not null;comment:答案" json:"answer"`
	Category  string    `gorm:"type:varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;not null;index;comment:分类" json:"category"`
	ViewCount uint32    `gorm:"type:int unsigned;default:0;comment:查看次数" json:"view_count"`
	SortOrder int       `gorm:"type:int;default:0;index;comment:排序" json:"sort_order"`
	Status    uint8     `gorm:"type:tinyint;not null;default:1;index;comment:状态 0:下架 1:发布" json:"status"`
	CreatedAt time.Time `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt time.Time `gorm:"autoUpdateTime" json:"updated_at"`
}

// TableName 指定表名
func (FAQ) TableName() string {
	return "faqs"
}

// 帮助文章状态常量
const (
	HelpArticleStatusDraft     = 0 // 草稿
	HelpArticleStatusPublished = 1 // 已发布
)

// FAQ状态常量
const (
	FAQStatusDraft     = 0 // 草稿
	FAQStatusPublished = 1 // 已发布
)
