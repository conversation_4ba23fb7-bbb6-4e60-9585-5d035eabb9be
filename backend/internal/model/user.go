package model

import (
	"time"
)

// User 用户模型
type User struct {
	ID                     uint64    `gorm:"primaryKey;autoIncrement" json:"id"`
	Phone                  string    `gorm:"type:varchar(20);uniqueIndex;not null" json:"phone"`
	Nickname               string    `gorm:"type:varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;default:''" json:"nickname"`
	BalanceCount           uint32    `gorm:"type:int unsigned;default:0;comment:剩余次数" json:"balance_count"`
	BalanceDuration        uint32    `gorm:"type:int unsigned;default:0;comment:剩余时长(秒)" json:"balance_duration"`
	PaidTotalDuration      uint32    `gorm:"type:int unsigned;default:0;comment:付费总时长(秒)" json:"paid_total_duration"`
	PaidUsedDuration       uint32    `gorm:"type:int unsigned;default:0;comment:已使用付费时长(秒)" json:"paid_used_duration"`
	FreeTrialUsedDuration  uint32    `gorm:"type:int unsigned;default:0;comment:已使用免费试用时长(秒)" json:"free_trial_used_duration"`
	FreeTrialTotalDuration uint32    `gorm:"type:int unsigned;comment:免费试用总时长(秒)" json:"free_trial_total_duration"`
	ABTestGroup            string    `gorm:"type:varchar(10);comment:A/B测试分组" json:"ab_test_group"`
	CreatedAt              time.Time `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt              time.Time `gorm:"autoUpdateTime" json:"updated_at"`
}

// TableName 指定表名
func (User) TableName() string {
	return "users"
}

// Order 订单模型
type Order struct {
	ID        uint64     `gorm:"primaryKey;autoIncrement" json:"id"`
	OrderNo   string     `gorm:"type:varchar(64);uniqueIndex;not null;comment:订单号" json:"order_no"`
	UserID    uint64     `gorm:"type:bigint unsigned;not null;index;comment:用户ID" json:"user_id"`
	ProductID string     `gorm:"type:varchar(50);not null;comment:商品ID" json:"product_id"`
	Amount    float64    `gorm:"type:decimal(10,2);not null;comment:订单金额" json:"amount"`
	Status    uint8      `gorm:"type:tinyint;not null;default:0;index;comment:订单状态 0:待支付 1:已支付 2:已关闭" json:"status"`
	PayType   string     `gorm:"type:varchar(20);comment:支付方式 wechat/alipay" json:"pay_type"`
	CreatedAt time.Time  `gorm:"autoCreateTime" json:"created_at"`
	PaidAt    *time.Time `gorm:"comment:支付时间" json:"paid_at"`

	// 关联
	User    User    `gorm:"foreignKey:UserID" json:"user,omitempty"`
	Product Product `gorm:"foreignKey:ProductID" json:"product,omitempty"`
}

// TableName 指定表名
func (Order) TableName() string {
	return "orders"
}

// InterviewLog 面试日志模型
type InterviewLog struct {
	ID              uint64     `gorm:"primaryKey;autoIncrement" json:"id"`
	UserID          uint64     `gorm:"type:bigint unsigned;not null;index;comment:用户ID" json:"user_id"`
	SessionID       string     `gorm:"type:varchar(128);not null;index;comment:会话ID" json:"session_id"`
	PromptVersion   string     `gorm:"type:varchar(20);comment:使用的提示词版本" json:"prompt_version"`
	QuestionText    string     `gorm:"type:text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;comment:识别出的问题文本" json:"question_text"`
	AnswerText      string     `gorm:"type:text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;comment:生成的回答文本" json:"answer_text"`
	ResponseTimeMs  int        `gorm:"type:int;comment:从问题结束到回答开始的延迟(ms)" json:"response_time_ms"`
	UserFeedback    int8       `gorm:"type:tinyint;default:0;comment:用户反馈 0:无 1:赞 -1:踩" json:"user_feedback"`
	StartedAt       *time.Time `gorm:"comment:面试开始时间" json:"started_at"`
	EndedAt         *time.Time `gorm:"comment:面试结束时间" json:"ended_at"`
	DurationSeconds uint32     `gorm:"type:int unsigned;default:0;comment:面试持续时长(秒)" json:"duration_seconds"`
	UsedTrialTime   bool       `gorm:"type:tinyint;default:0;comment:是否使用了试用时长" json:"used_trial_time"`
	CreatedAt       time.Time  `gorm:"autoCreateTime" json:"created_at"`

	// 关联
	User User `gorm:"foreignKey:UserID" json:"user,omitempty"`
}

// TableName 指定表名
func (InterviewLog) TableName() string {
	return "interview_logs"
}

// SMSCode 短信验证码模型
type SMSCode struct {
	ID        uint64    `gorm:"primaryKey;autoIncrement" json:"id"`
	Phone     string    `gorm:"type:varchar(20);not null;index:idx_phone_code;comment:手机号" json:"phone"`
	Code      string    `gorm:"type:varchar(10);not null;index:idx_phone_code;comment:验证码" json:"code"`
	Type      uint8     `gorm:"type:tinyint;not null;default:1;comment:类型 1:注册 2:登录 3:重置密码" json:"type"`
	Used      uint8     `gorm:"type:tinyint;not null;default:0;comment:是否已使用 0:未使用 1:已使用" json:"used"`
	ExpiresAt time.Time `gorm:"not null;index;comment:过期时间" json:"expires_at"`
	CreatedAt time.Time `gorm:"autoCreateTime" json:"created_at"`
}

// TableName 指定表名
func (SMSCode) TableName() string {
	return "sms_codes"
}

// Product 商品模型
type Product struct {
	ID          string    `gorm:"primaryKey;type:varchar(50);comment:商品ID" json:"id"`
	Name        string    `gorm:"type:varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;not null;comment:商品名称" json:"name"`
	Description string    `gorm:"type:text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;comment:商品描述" json:"description"`
	Price       float64   `gorm:"type:decimal(10,2);not null;comment:价格" json:"price"`
	Count       uint32    `gorm:"type:int unsigned;default:0;comment:包含次数" json:"count"`
	Duration    uint32    `gorm:"type:int unsigned;default:0;comment:包含时长(秒)" json:"duration"`
	Type        uint8     `gorm:"type:tinyint;not null;default:1;comment:类型 1:次数包 2:时长包 3:包月" json:"type"`
	Status      uint8     `gorm:"type:tinyint;not null;default:1;index:idx_status_sort;comment:状态 0:下架 1:上架" json:"status"`
	SortOrder   int       `gorm:"type:int;default:0;index:idx_status_sort;comment:排序" json:"sort_order"`
	CreatedAt   time.Time `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt   time.Time `gorm:"autoUpdateTime" json:"updated_at"`
}

// TableName 指定表名
func (Product) TableName() string {
	return "products"
}

// Admin 管理员模型
type Admin struct {
	ID          uint64     `gorm:"primaryKey;autoIncrement" json:"id"`
	Username    string     `gorm:"type:varchar(50);uniqueIndex;not null;comment:用户名" json:"username"`
	Password    string     `gorm:"type:varchar(255);not null;comment:密码hash" json:"-"`
	Name        string     `gorm:"type:varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;not null;comment:姓名" json:"name"`
	Email       string     `gorm:"type:varchar(100);comment:邮箱" json:"email"`
	Role        string     `gorm:"type:varchar(20);default:'admin';comment:角色" json:"role"`
	Status      uint8      `gorm:"type:tinyint;not null;default:1;comment:状态 0:禁用 1:启用" json:"status"`
	LastLoginAt *time.Time `gorm:"comment:最后登录时间" json:"last_login_at"`
	CreatedAt   time.Time  `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt   time.Time  `gorm:"autoUpdateTime" json:"updated_at"`
}

// TableName 指定表名
func (Admin) TableName() string {
	return "admins"
}

// 订单状态常量
const (
	OrderStatusPending = 0 // 待支付
	OrderStatusPaid    = 1 // 已支付
	OrderStatusClosed  = 2 // 已关闭
)

// 短信验证码类型常量
const (
	SMSTypeRegister = 1 // 注册
	SMSTypeLogin    = 2 // 登录
	SMSTypeReset    = 3 // 重置密码
)

// 商品类型常量
const (
	ProductTypeCount    = 1 // 次数包
	ProductTypeDuration = 2 // 时长包
	ProductTypeMonthly  = 3 // 包月
)

// 用户反馈常量
const (
	FeedbackNone     = 0  // 无反馈
	FeedbackPositive = 1  // 赞
	FeedbackNegative = -1 // 踩
)
