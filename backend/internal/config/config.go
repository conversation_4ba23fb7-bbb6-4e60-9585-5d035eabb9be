package config

import (
	"encoding/json"
	"fmt"
	"os"
	"time"
)

// Config 应用配置结构
type Config struct {
	Server       ServerConfig    `json:"server"`
	Database     DatabaseConfig  `json:"database"`
	JWT          JWTConfig       `json:"jwt"`
	Gemini       GeminiConfig    `json:"gemini"`
	AliyunSMS    AliyunSMSConfig `json:"aliyun_sms"`
	Payment      PaymentConfig   `json:"payment"`
	ABTest       ABTestConfig    `json:"ab_test"`
	Log          LogConfig       `json:"log"`
	UserSettings UserSettings    `json:"user_settings"`
}

// ServerConfig 服务器配置
type ServerConfig struct {
	Host         string `json:"host"`
	Port         string `json:"port"`
	Mode         string `json:"mode"`
	ReadTimeout  int64  `json:"read_timeout"`
	WriteTimeout int64  `json:"write_timeout"`
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	MySQL MySQLConfig `json:"mysql"`
	Redis RedisConfig `json:"redis"`
}

// MySQLConfig MySQL配置
type MySQLConfig struct {
	Host            string        `json:"host"`
	Port            string        `json:"port"`
	Username        string        `json:"username"`
	Password        string        `json:"password"`
	Database        string        `json:"database"`
	Charset         string        `json:"charset"`
	ParseTime       bool          `json:"parse_time"`
	Loc             string        `json:"loc"`
	MaxIdleConns    int           `json:"max_idle_conns"`
	MaxOpenConns    int           `json:"max_open_conns"`
	ConnMaxLifetime time.Duration `json:"conn_max_lifetime"`
}

// RedisConfig Redis配置
type RedisConfig struct {
	Host         string `json:"host"`
	Port         string `json:"port"`
	Password     string `json:"password"`
	DB           int    `json:"db"`
	PoolSize     int    `json:"pool_size"`
	MinIdleConns int    `json:"min_idle_conns"`
}

// JWTConfig JWT配置
type JWTConfig struct {
	Secret      string `json:"secret"`
	ExpireHours string `json:"expire_hours"`
}

// GeminiConfig Gemini API配置
type GeminiConfig struct {
	APIKey                string        `json:"api_key"`
	Model                 string        `json:"model"`
	BaseURL               string        `json:"base_url"`
	ProxyURL              string        `json:"proxy_url"`       // 代理服务器URL（可选）
	ConnectionMode        string        `json:"connection_mode"` // 连接模式: auto, direct, proxy
	WebSocketHost         string        `json:"websocket_host"`  // WebSocket主机地址
	APIVersion            string        `json:"api_version"`     // API版本
	Timeout               time.Duration `json:"timeout"`
	MaxConcurrentSessions int           `json:"max_concurrent_sessions"`
}

// SMSConfig 短信配置
type SMSConfig struct {
	Aliyun AliyunSMSConfig `json:"aliyun"`
}

// AliyunSMSConfig 阿里云短信配置
type AliyunSMSConfig struct {
	AccessKeyID     string `json:"access_key_id"`
	AccessKeySecret string `json:"access_key_secret"`
	SignName        string `json:"sign_name"`
	TemplateCode    string `json:"template_code"`
	Region          string `json:"region"`
	Test            bool   `json:"test"`
	TestCode        string `json:"testCode"`
}

// PaymentConfig 支付配置
type PaymentConfig struct {
	WeChat WeChatPayConfig `json:"wechat"`
	Alipay AlipayConfig    `json:"alipay"`
}

// WeChatPayConfig 微信支付配置
type WeChatPayConfig struct {
	AppID     string `json:"app_id"`
	MchID     string `json:"mch_id"`
	APIKey    string `json:"api_key"`
	NotifyURL string `json:"notify_url"`
}

// AlipayConfig 支付宝配置
type AlipayConfig struct {
	AppID        string `json:"app_id"`
	PrivateKey   string `json:"private_key"`
	PublicKey    string `json:"public_key"`
	NotifyURL    string `json:"notify_url"`
	IsProduction bool   `json:"is_production"` // 是否为生产环境
}

// 删除WebSocket和Audio配置，改为客户端直连模式

// ABTestConfig A/B测试配置
type ABTestConfig struct {
	Enabled      bool     `json:"enabled"`
	DefaultGroup string   `json:"default_group"`
	Groups       []string `json:"groups"`
}

// LogConfig 日志配置
type LogConfig struct {
	Level      string `json:"level"`
	FilePath   string `json:"file_path"`
	MaxSize    int    `json:"max_size"`
	MaxBackups int    `json:"max_backups"`
	MaxAge     int    `json:"max_age"`
}

// UserSettings 用户设置配置
type UserSettings struct {
	FreeTrialTotalDuration int `json:"free_trial_total_duration"`
}

var GlobalConfig *Config

// LoadConfig 加载配置文件
func LoadConfig(configPath string) (*Config, error) {
	// 如果配置文件不存在，尝试加载示例配置
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		configPath = "config.example.json"
	}

	file, err := os.Open(configPath)
	if err != nil {
		return nil, fmt.Errorf("打开配置文件失败: %v", err)
	}
	defer file.Close()

	var config Config
	decoder := json.NewDecoder(file)
	if err := decoder.Decode(&config); err != nil {
		return nil, fmt.Errorf("解析配置文件失败: %v", err)
	}

	// 设置默认值
	setDefaults(&config)

	GlobalConfig = &config
	return &config, nil
}

// setDefaults 设置默认配置值
func setDefaults(config *Config) {
	if config.Server.Host == "" {
		config.Server.Host = "0.0.0.0"
	}
	if config.Server.Port == "" {
		config.Server.Port = "8080"
	}
	if config.Server.Mode == "" {
		config.Server.Mode = "debug"
	}
	if config.Server.ReadTimeout == 0 {
		config.Server.ReadTimeout = 60
	}
	if config.Server.WriteTimeout == 0 {
		config.Server.WriteTimeout = 60
	}

	if config.JWT.ExpireHours == "" {
		config.JWT.ExpireHours = "24h"
	}

	if config.Gemini.Timeout == 0 {
		config.Gemini.Timeout = 30 * time.Second
	}
	if config.Gemini.MaxConcurrentSessions == 0 {
		config.Gemini.MaxConcurrentSessions = 100
	}
	if config.Gemini.ConnectionMode == "" {
		config.Gemini.ConnectionMode = "auto"
	}
	if config.Gemini.WebSocketHost == "" {
		config.Gemini.WebSocketHost = "generativelanguage.googleapis.com"
	}
	if config.Gemini.APIVersion == "" {
		config.Gemini.APIVersion = "v1alpha"
	}
}

// GetDSN 获取MySQL连接字符串
func (c *MySQLConfig) GetDSN() string {
	return fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=%s&parseTime=%t&loc=%s",
		c.Username, c.Password, c.Host, c.Port, c.Database, c.Charset, c.ParseTime, c.Loc)
}

// GetRedisAddr 获取Redis地址
func (c *RedisConfig) GetRedisAddr() string {
	return fmt.Sprintf("%s:%s", c.Host, c.Port)
}

// GetConfig 获取全局配置
func GetConfig() *Config {
	return GlobalConfig
}
