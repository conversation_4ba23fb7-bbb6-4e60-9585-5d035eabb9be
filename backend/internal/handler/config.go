package handler

import (
	"log/slog"
	"net/http"

	"github.com/gin-gonic/gin"

	"interviewmaster/internal/config"
	"interviewmaster/internal/utils"
)

// ConfigHandler 配置处理器
type ConfigHandler struct{}

// NewConfigHandler 创建配置处理器
func NewConfigHandler() *ConfigHandler {
	return &ConfigHandler{}
}

// GeminiConfigResponse Gemini配置响应
type GeminiConfigResponse struct {
	ConnectionMode string `json:"connection_mode"` // auto, direct, proxy
	Model          string `json:"model"`
	WebSocketHost  string `json:"websocket_host"`
	APIVersion     string `json:"api_version"`
}

// GetGeminiConfig 获取Gemini配置
func (h *ConfigHandler) GetGeminiConfig(c *gin.Context) {
	slog.Info("📡 收到获取Gemini配置请求")

	cfg := config.GlobalConfig
	if cfg == nil {
		slog.Error("❌ 配置未初始化")
		utils.ErrorResponse(c, http.StatusInternalServerError, "配置未初始化")
		return
	}

	response := GeminiConfigResponse{
		ConnectionMode: cfg.Gemini.ConnectionMode,
		Model:          cfg.Gemini.Model,
		WebSocketHost:  cfg.Gemini.WebSocketHost,
		APIVersion:     cfg.Gemini.APIVersion,
	}

	slog.Info("✅ 返回Gemini配置",
		"connectionMode", response.ConnectionMode,
		"model", response.Model,
	)

	utils.SuccessResponse(c, response)
}

// UpdateGeminiConfigRequest 更新Gemini配置请求
type UpdateGeminiConfigRequest struct {
	ConnectionMode string `json:"connection_mode" binding:"required,oneof=auto direct proxy"`
}

// UpdateGeminiConfig 更新Gemini配置（管理员功能）
func (h *ConfigHandler) UpdateGeminiConfig(c *gin.Context) {
	var req UpdateGeminiConfigRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ValidationErrorResponse(c, "参数错误: "+err.Error())
		return
	}

	cfg := config.GlobalConfig
	if cfg == nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "配置未初始化")
		return
	}

	// 更新配置
	cfg.Gemini.ConnectionMode = req.ConnectionMode

	response := GeminiConfigResponse{
		ConnectionMode: cfg.Gemini.ConnectionMode,
		Model:          cfg.Gemini.Model,
		WebSocketHost:  cfg.Gemini.WebSocketHost,
		APIVersion:     cfg.Gemini.APIVersion,
	}

	utils.SuccessResponse(c, response)
}
