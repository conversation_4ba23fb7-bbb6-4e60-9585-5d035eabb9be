package handler

import (
	"sync"

	"interviewmaster/internal/service"
	"log/slog"

	"github.com/gorilla/websocket"
)

// ConnectionManager 管理WebSocket连接和面试会话ID的映射
type ConnectionManager struct {
	connections map[*websocket.Conn]string
	mutex       sync.RWMutex
}

var (
	managerInstance *ConnectionManager
	once            sync.Once
)

// GetConnectionManager 获取ConnectionManager的单例
func GetConnectionManager() *ConnectionManager {
	once.Do(func() {
		managerInstance = &ConnectionManager{
			connections: make(map[*websocket.Conn]string),
		}
	})
	return managerInstance
}

// Register 注册一个连接和sessionID的映射
func (m *ConnectionManager) Register(conn *websocket.Conn, sessionID string) {
	m.mutex.Lock()
	defer m.mutex.Unlock()
	m.connections[conn] = sessionID
	slog.Info("🔗 ConnectionManager: 注册连接", "sessionID", sessionID, "remoteAddr", conn.RemoteAddr().String())
}

// Unregister 注销一个连接
func (m *ConnectionManager) Unregister(conn *websocket.Conn) {
	m.mutex.Lock()
	defer m.mutex.Unlock()
	if sessionID, ok := m.connections[conn]; ok {
		delete(m.connections, conn)
		slog.Info("🔌 ConnectionManager: 注销连接", "sessionID", sessionID, "remoteAddr", conn.RemoteAddr().String())
	}
}

// HandleDisconnect 处理连接断开的业务逻辑
func (m *ConnectionManager) HandleDisconnect(conn *websocket.Conn) {
	m.mutex.RLock()
	sessionID, ok := m.connections[conn]
	m.mutex.RUnlock()

	if ok {
		slog.Info("🚨 ConnectionManager: 检测到连接断开，开始处理业务逻辑", "sessionID", sessionID)
		// 在一个新的goroutine中执行，避免阻塞
		go func(sid string) {
			// 这里我们无法直接获取userID，但EndInterviewSession内部会从数据库查询
			// 注意：EndInterviewSession需要userID，这是一个潜在的问题。
			// 我们需要找到一种方法来传递userID或修改EndInterviewSession。
			// 暂时，我们先假设可以找到一种方式调用它。
			// 理想情况下，Register时也应该存储userID。

			// 创建一个新的InterviewService实例来结束会话
			interviewService := service.NewInterviewService()

			// 我们需要userID来调用EndInterviewSession。
			// 让我们先获取与此会话关联的userID。
			log, err := interviewService.GetLatestLogBySessionID(sid)
			if err != nil {
				slog.Error("❌ ConnectionManager: 获取会话日志失败", "sessionID", sid, "error", err)
				return
			}
			if log == nil {
				slog.Warn("⚠️ ConnectionManager: 未找到会话日志", "sessionID", sid)
				return
			}

			if err := interviewService.EndInterviewSession(log.UserID, sid); err != nil {
				// 忽略“会话已结束”的错误，因为可能由多个事件触发
				if err.Error() != "面试会话不存在或已结束" {
					slog.Error("❌ ConnectionManager: 自动结束面试会话失败", "sessionID", sid, "error", err)
				} else {
					slog.Info("ℹ️ ConnectionManager: 会话已由其他方式结束", "sessionID", sid)
				}
			} else {
				slog.Info("✅ ConnectionManager: 成功自动结束面试会话", "sessionID", sid)
			}
		}(sessionID)
	}
}
