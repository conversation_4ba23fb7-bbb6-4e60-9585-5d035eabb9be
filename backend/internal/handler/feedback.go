package handler

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"interviewmaster/internal/service"
	"interviewmaster/internal/utils"
)

type FeedbackHandler struct {
	feedbackService *service.FeedbackService
}

func NewFeedbackHandler(feedbackService *service.FeedbackService) *FeedbackHandler {
	return &FeedbackHandler{
		feedbackService: feedbackService,
	}
}

// CreateFeedback 创建用户反馈
func (h *FeedbackHandler) CreateFeedback(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		utils.ErrorResponse(c, http.StatusUnauthorized, "用户未登录")
		return
	}

	var req struct {
		Type         string `json:"type" binding:"required,oneof=suggestion bug other"`
		Content      string `json:"content" binding:"required,min=10,max=2000"`
		ContactEmail string `json:"contact_email" binding:"omitempty,email"`
	}

	if err := c.<PERSON>(&req); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "参数错误: "+err.Error())
		return
	}

	feedback, err := h.feedbackService.CreateFeedback(
		userID.(uint64),
		req.Type,
		req.Content,
		req.ContactEmail,
	)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, err.Error())
		return
	}

	utils.SuccessResponseWithMessage(c, "反馈提交成功，我们会尽快处理", feedback)
}

// GetUserFeedbacks 获取用户反馈列表
func (h *FeedbackHandler) GetUserFeedbacks(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		utils.ErrorResponse(c, http.StatusUnauthorized, "用户未登录")
		return
	}

	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "10"))

	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 50 {
		pageSize = 10
	}

	feedbacks, total, err := h.feedbackService.GetUserFeedbacks(userID.(uint64), page, pageSize)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, err.Error())
		return
	}

	utils.SuccessResponse(c, gin.H{
		"feedbacks": feedbacks,
		"total":     total,
		"page":      page,
		"page_size": pageSize,
	})
}

// GetFeedbackDetail 获取反馈详情
func (h *FeedbackHandler) GetFeedbackDetail(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		utils.ErrorResponse(c, http.StatusUnauthorized, "用户未登录")
		return
	}

	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "无效的反馈ID")
		return
	}

	feedback, err := h.feedbackService.GetFeedbackByID(id, userID.(uint64))
	if err != nil {
		utils.ErrorResponse(c, http.StatusNotFound, "反馈不存在")
		return
	}

	utils.SuccessResponse(c, feedback)
}

// GetFAQs 获取常见问题列表
func (h *FeedbackHandler) GetFAQs(c *gin.Context) {
	category := c.Query("category")

	faqs, err := h.feedbackService.GetFAQs(category)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, err.Error())
		return
	}

	utils.SuccessResponse(c, gin.H{
		"faqs": faqs,
	})
}

// GetFAQCategories 获取FAQ分类
func (h *FeedbackHandler) GetFAQCategories(c *gin.Context) {
	categories, err := h.feedbackService.GetFAQCategories()
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, err.Error())
		return
	}

	utils.SuccessResponse(c, gin.H{
		"categories": categories,
	})
}

// IncrementFAQViewCount 增加FAQ查看次数
func (h *FeedbackHandler) IncrementFAQViewCount(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "无效的FAQ ID")
		return
	}

	err = h.feedbackService.IncrementFAQViewCount(id)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, err.Error())
		return
	}

	utils.SuccessResponseWithMessage(c, "操作成功", nil)
}

// GetHelpArticles 获取帮助文章列表
func (h *FeedbackHandler) GetHelpArticles(c *gin.Context) {
	category := c.Query("category")

	articles, err := h.feedbackService.GetHelpArticles(category)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, err.Error())
		return
	}

	utils.SuccessResponse(c, gin.H{
		"articles": articles,
	})
}

// GetHelpArticleDetail 获取帮助文章详情
func (h *FeedbackHandler) GetHelpArticleDetail(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "无效的文章ID")
		return
	}

	article, err := h.feedbackService.GetHelpArticleByID(id)
	if err != nil {
		utils.ErrorResponse(c, http.StatusNotFound, "文章不存在")
		return
	}

	utils.SuccessResponse(c, article)
}

// GetHelpCategories 获取帮助文章分类
func (h *FeedbackHandler) GetHelpCategories(c *gin.Context) {
	categories, err := h.feedbackService.GetHelpCategories()
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, err.Error())
		return
	}

	utils.SuccessResponse(c, gin.H{
		"categories": categories,
	})
}