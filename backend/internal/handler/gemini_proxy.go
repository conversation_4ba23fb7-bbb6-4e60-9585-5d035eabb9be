package handler

import (
	"context"
	"fmt"
	"io"
	"log/slog"
	"net"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"sync"
	"time"

	"encoding/json"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"

	"interviewmaster/internal/config"
	"interviewmaster/internal/utils"
)

const (
	// CloseServiceUnavailable 自定义WebSocket关闭码，表示服务不可用（例如，时长用尽）
	CloseServiceUnavailable = 4001
)

// GeminiProxyHandler Gemini代理处理器
type GeminiProxyHandler struct {
	upgrader websocket.Upgrader
}

// NewGeminiProxyHandler 创建Gemini代理处理器
func NewGeminiProxyHandler() *GeminiProxyHandler {
	return &GeminiProxyHandler{
		upgrader: websocket.Upgrader{
			CheckOrigin: func(r *http.Request) bool {
				// 允许所有来源，生产环境应该限制
				return true
			},
			ReadBufferSize:  1024 * 16, // 增加到16KB，匹配dialer设置
			WriteBufferSize: 1024 * 16, // 增加到16KB，匹配dialer设置
		},
	}
}

// ProxyWebSocket 代理WebSocket连接到Gemini Live API
func (h *GeminiProxyHandler) ProxyWebSocket(c *gin.Context) {
	connManager := GetConnectionManager()
	slog.Info("🔗 收到WebSocket代理请求", "path", c.Request.URL.Path, "query", c.Request.URL.RawQuery)

	// 获取用户信息和token类型
	tokenType, _ := c.Get("token_type")
	var userID uint64

	if tokenType == "ephemeral" {
		// 对于临时令牌，我们需要从token中提取用户信息
		// 临时令牌是通过认证用户获取的，所以我们可以信任它
		// 但我们需要一种方式来获取用户ID

		// 方案1: 从数据库查询最近获取此临时令牌的用户
		// 方案2: 在临时令牌中编码用户信息
		// 方案3: 要求客户端同时传递用户ID

		// 这里我们暂时使用一个简化的方案：从请求中获取用户ID
		// 在生产环境中，应该有更安全的方式来验证临时令牌对应的用户
		userIDStr := c.Query("user_id")
		if userIDStr == "" {
			slog.Error("❌ WebSocket代理: 临时令牌缺少用户ID参数")
			utils.ErrorResponse(c, http.StatusBadRequest, "临时令牌缺少用户ID参数")
			return
		}

		var err error
		userID, err = strconv.ParseUint(userIDStr, 10, 64)
		if err != nil {
			slog.Error("❌ WebSocket代理: 用户ID格式错误", "userIDStr", userIDStr, "error", err)
			utils.ErrorResponse(c, http.StatusBadRequest, "用户ID格式错误")
			return
		}

		slog.Info("✅ WebSocket代理: 临时令牌认证成功", "userID", userID)
	} else {
		// 对于JWT token，从中间件设置的上下文获取用户ID
		userIDValue, exists := c.Get("user_id")
		if !exists {
			slog.Error("❌ WebSocket代理: 用户未认证")
			utils.ErrorResponse(c, http.StatusUnauthorized, "用户未认证")
			return
		}
		var ok bool
		userID, ok = userIDValue.(uint64)
		if !ok {
			slog.Error("❌ WebSocket代理: 用户ID格式错误", "userIDValue", userIDValue)
			utils.ErrorResponse(c, http.StatusInternalServerError, "用户ID格式错误")
			return
		}

		slog.Info("✅ WebSocket代理: JWT认证成功", "userID", userID)
	}

	slog.Info("✅ WebSocket代理: 用户认证成功", "userID", userID)

	// 获取临时令牌
	token := c.Query("access_token")
	if token == "" {
		token = c.Query("key")
	}
	if token == "" {
		slog.Error("❌ WebSocket代理: 缺少访问令牌", "query", c.Request.URL.RawQuery)
		utils.ErrorResponse(c, http.StatusBadRequest, "缺少访问令牌")
		return
	}

	slog.Info("✅ WebSocket代理: 获取到访问令牌", "tokenPrefix", token[:min(len(token), 20)]+"...")

	// 获取方法参数
	method := c.DefaultQuery("method", "BidiGenerateContent")
	if strings.Contains(token, "auth_tokens/") {
		method = "BidiGenerateContentConstrained"
		slog.Info("🔄 WebSocket代理: 检测到临时令牌，使用受限方法", "method", method)
	} else {
		slog.Info("🔄 WebSocket代理: 使用标准方法", "method", method)
	}

	cfg := config.GlobalConfig
	if cfg == nil {
		slog.Error("❌ WebSocket代理: 配置未初始化")
		utils.ErrorResponse(c, http.StatusInternalServerError, "配置未初始化")
		return
	}

	// 构建目标WebSocket URL
	var keyParam string
	if strings.Contains(token, "auth_tokens/") {
		keyParam = "access_token=" + token
	} else {
		keyParam = "key=" + token
	}

	targetURL := fmt.Sprintf("wss://generativelanguage.googleapis.com/ws/google.ai.generativelanguage.v1alpha.GenerativeService.%s?%s", method, keyParam)

	slog.Info("🎯 WebSocket代理: 构建目标URL", "userID", userID, "targetURL", targetURL[:min(len(targetURL), 100)]+"...")

	slog.Info("🔄 WebSocket代理: 开始升级客户端连接")

	// 升级客户端连接为WebSocket
	clientConn, err := h.upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		slog.Error("❌ WebSocket代理: 升级客户端连接失败", "error", err, "userID", userID)
		return
	}

	defer func() {
		slog.Info("🔌 WebSocket代理: 关闭客户端连接", "userID", userID)
		connManager.HandleDisconnect(clientConn)
		connManager.Unregister(clientConn)
		clientConn.Close()
	}()

	slog.Info("✅ WebSocket代理: 客户端连接升级成功", "userID", userID)

	// 连接到Gemini API
	slog.Info("🌐 WebSocket代理: 开始连接到Gemini API", "targetURL", targetURL[:min(len(targetURL), 100)]+"...")

	dialer := websocket.DefaultDialer
	dialer.HandshakeTimeout = 30 * time.Second

	// 设置更长的读写超时，避免过早断开连接
	dialer.ReadBufferSize = 1024 * 16  // 增加读缓冲区到16KB
	dialer.WriteBufferSize = 1024 * 16 // 增加写缓冲区到16KB

	// 针对跨网段连接的优化
	dialer.NetDialContext = func(ctx context.Context, network, addr string) (net.Conn, error) {
		d := &net.Dialer{
			Timeout:   30 * time.Second,
			KeepAlive: 30 * time.Second, // 启用TCP keepalive，30秒间隔
		}
		conn, err := d.DialContext(ctx, network, addr)
		if err != nil {
			return nil, err
		}

		// 设置TCP keepalive参数并记录连接信息
		if tcpConn, ok := conn.(*net.TCPConn); ok {
			tcpConn.SetKeepAlive(true)
			tcpConn.SetKeepAlivePeriod(30 * time.Second)

			localAddr := tcpConn.LocalAddr()
			remoteAddr := tcpConn.RemoteAddr()
			slog.Info("🔗 WebSocket代理: TCP连接建立",
				"userID", userID,
				"local", localAddr.String(),
				"remote", remoteAddr.String(),
				"keepalive", "30s")
		}

		return conn, nil
	}

	// 设置代理（如果配置了）
	if cfg.Gemini.ProxyURL != "" {
		proxyURL, err := url.Parse(cfg.Gemini.ProxyURL)
		if err == nil {
			dialer.Proxy = http.ProxyURL(proxyURL)
			slog.Info("🔗 WebSocket代理: 使用外部代理连接", "proxy", cfg.Gemini.ProxyURL)
		} else {
			slog.Error("❌ WebSocket代理: 解析外部代理URL失败", "proxyURL", cfg.Gemini.ProxyURL, "error", err)
		}
	}

	geminiConn, resp, err := dialer.Dial(targetURL, nil)
	if err != nil {
		slog.Error("❌ WebSocket代理: 连接到Gemini API失败",
			"error", err,
			"userID", userID,
			"targetURL", targetURL[:min(len(targetURL), 100)]+"...",
			"responseStatus", func() string {
				if resp != nil {
					return resp.Status
				}
				return "无响应"
			}())

		// 向客户端发送错误信息
		clientConn.WriteMessage(websocket.CloseMessage,
			websocket.FormatCloseMessage(websocket.CloseInternalServerErr,
				fmt.Sprintf("无法连接到AI服务: %v", err)))
		return
	}

	defer func() {
		slog.Info("🔌 WebSocket代理: 关闭Gemini连接", "userID", userID)
		geminiConn.Close()
	}()

	slog.Info("✅ WebSocket代理: 成功连接到Gemini API", "userID", userID)

	// 设置连接的读写超时，防止连接僵死
	// 放宽超时限制，避免在用户思考或长时间静默时断开连接
	geminiConn.SetReadDeadline(time.Now().Add(600 * time.Second)) // 10分钟

	// 创建双向数据转发
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// 创建写入锁，防止并发写入WebSocket连接
	var clientWriteMutex sync.Mutex

	slog.Info("🔄 WebSocket代理: 开始双向数据转发", "userID", userID)

	// 统计数据
	var clientToGeminiCount, geminiToClientCount int64

	// 添加连接状态监控（不包含心跳，保持与直连模式一致）
	go func() {
		ticker := time.NewTicker(30 * time.Second) // 每30秒记录一次连接状态
		defer ticker.Stop()

		for {
			select {
			case <-ticker.C:
				// 检查连接状态并记录详细信息
				clientState := "unknown"
				geminiState := "unknown"

				if clientConn != nil {
					switch clientConn.UnderlyingConn() {
					case nil:
						clientState = "closed"
					default:
						clientState = "active"
					}
				}

				if geminiConn != nil {
					switch geminiConn.UnderlyingConn() {
					case nil:
						geminiState = "closed"
					default:
						geminiState = "active"
					}
				}

				slog.Info("📊 WebSocket代理: 连接状态监控",
					"userID", userID,
					"clientState", clientState,
					"geminiState", geminiState,
					"clientToGeminiCount", clientToGeminiCount,
					"geminiToClientCount", geminiToClientCount,
					"uptime", time.Since(time.Now().Add(-time.Duration(time.Now().Unix()-time.Now().Unix())*time.Second)))

			case <-ctx.Done():
				slog.Info("🛑 WebSocket代理: 停止连接状态监控", "userID", userID)
				return
			}
		}
	}()

	// 客户端到Gemini的数据转发
	go func() {
		defer func() {
			if r := recover(); r != nil {
				slog.Error("❌ WebSocket代理: 客户端转发goroutine发生panic",
					"userID", userID,
					"panic", r,
					"messageCount", clientToGeminiCount)
			}
			slog.Info("📤 WebSocket代理: 客户端到Gemini转发结束", "userID", userID, "messageCount", clientToGeminiCount)
			cancel()
			geminiConn.Close() // 确保另一端也能感知到关闭
		}()

		for {
			if ctx.Err() != nil {
				return
			}

			// 设置一个比客户端心跳间隔（30秒）更长的超时（75秒）。
			// 如果在此期间没有收到任何消息（包括心跳），连接将被视为超时。
			clientConn.SetReadDeadline(time.Now().Add(75 * time.Second))
			messageType, reader, err := clientConn.NextReader()
			if err != nil {
				// 检查是否是超时错误
				if netErr, ok := err.(net.Error); ok && netErr.Timeout() {
					slog.Error("🚨 WebSocket代理: 客户端读取超时（长时间未收到心跳），关闭连接", "userID", userID, "timeout", "75s")
					return // 终止goroutine
				}

				if websocket.IsCloseError(err, websocket.CloseGoingAway, websocket.CloseNormalClosure) {
					slog.Info("🔌 WebSocket代理: 客户端正常关闭连接", "userID", userID)
				} else {
					slog.Error("❌ WebSocket代理: 读取客户端消息时发生错误，关闭连接", "error", err, "userID", userID)
				}
				return // 任何其他错误都终止goroutine
			}

			// 先将消息完整读入缓冲区，再决定是否转发
			data, err := io.ReadAll(reader)
			if err != nil {
				slog.Error("❌ WebSocket代理: 读取客户端消息体失败", "error", err, "userID", userID)
				return // 读取失败，终止转发
			}

			// 检查是否为内部控制消息或心跳消息，如果是，则处理后不转发
			if messageType == websocket.TextMessage {
				var msg map[string]interface{}
				if json.Unmarshal(data, &msg) == nil {
					if msgType, ok := msg["type"].(string); ok {
						switch msgType {
						case "register_session":
							sessionID, _ := msg["session_id"].(string)
							if sessionID != "" {
								connManager.Register(clientConn, sessionID)
								slog.Info("✅ WebSocket代理: 注册会话成功", "userID", userID, "sessionID", sessionID)
							}
							continue // 处理完后，跳过转发
						case "heartbeat":
							slog.Debug("❤️ WebSocket代理: 收到客户端心跳", "userID", userID)
							continue // 收到心跳，跳过转发，循环会重置ReadDeadline
						}
					}
				}
			}

			// --- 如果代码执行到这里，说明消息需要被转发 ---

			// 检查Gemini连接状态
			if geminiConn == nil {
				slog.Warn("⚠️ WebSocket代理: Gemini连接为空，无法转发", "userID", userID)
				return
			}

			// 获取写入器并转发消息
			writer, err := geminiConn.NextWriter(messageType)
			if err != nil {
				if strings.Contains(err.Error(), "use of closed network connection") {
					slog.Info("🔌 WebSocket代理: Gemini连接已关闭，停止转发", "userID", userID)
				} else {
					slog.Error("❌ WebSocket代理: 创建Gemini写入器失败", "error", err, "userID", userID)
				}
				return
			}

			if _, writeErr := writer.Write(data); writeErr != nil {
				slog.Error("❌ WebSocket代理: 转发到Gemini时发生写入错误", "error", writeErr, "userID", userID)
				writer.Close() // 尽力关闭
				return
			}

			if err := writer.Close(); err != nil {
				if !strings.Contains(err.Error(), "use of closed network connection") {
					slog.Error("❌ WebSocket代理: 关闭Gemini写入器失败", "error", err, "userID", userID)
				}
				return
			}

			clientToGeminiCount++
			// 修正日志记录，确保在关闭写入器之前记录
			slog.Debug("📤 WebSocket代理: 转发客户端消息到Gemini",
				"userID", userID,
				"messageType", messageType,
				"dataSize", len(data),
				"messageCount", clientToGeminiCount)
		}
	}()

	// Gemini到客户端的数据转发
	go func() {
		defer func() {
			if r := recover(); r != nil {
				slog.Error("❌ WebSocket代理: Gemini转发goroutine发生panic",
					"userID", userID,
					"panic", r,
					"messageCount", geminiToClientCount)
			}
			slog.Info("📥 WebSocket代理: Gemini到客户端转发结束", "userID", userID, "messageCount", geminiToClientCount)
			cancel()
			clientConn.Close() // 确保另一端也能感知到关闭
		}()

		const maxWriteFailures = 3

		for {
			if ctx.Err() != nil {
				return
			}
			if geminiConn == nil {
				slog.Warn("⚠️ WebSocket代理: Gemini连接为空，退出转发", "userID", userID)
				return
			}

			geminiConn.SetReadDeadline(time.Now().Add(600 * time.Second))
			messageType, reader, err := geminiConn.NextReader()
			if err != nil {
				if websocket.IsCloseError(err, websocket.CloseGoingAway, websocket.CloseNormalClosure) {
					slog.Info("🔌 WebSocket代理: Gemini正常关闭连接", "userID", userID)
				} else if strings.Contains(err.Error(), "i/o timeout") {
					slog.Debug("ℹ️ WebSocket代理: Gemini读取超时（正常现象）", "userID", userID)
					continue
				} else {
					slog.Error("❌ WebSocket代理: 读取Gemini消息时发生错误，关闭连接",
						"error", err,
						"error_type", fmt.Sprintf("%T", err),
						"userID", userID)

					// 检查是否是特定的服务不可用错误
					if wsErr, ok := err.(*websocket.CloseError); ok && wsErr.Code == websocket.CloseInternalServerErr &&
						strings.Contains(wsErr.Text, "The service is currently unavailable.") {
						slog.Warn("⚠️ WebSocket代理: 检测到Gemini服务不可用错误，发送自定义关闭码", "userID", userID)
						clientWriteMutex.Lock()
						clientConn.WriteMessage(websocket.CloseMessage,
							websocket.FormatCloseMessage(CloseServiceUnavailable, "当前服务时长已用完，请充值"))
						clientWriteMutex.Unlock()
						return // 终止goroutine
					}
				}
				// 发生不可恢复的错误时，主动panic，以便捕获更详细的堆栈信息
				// 这有助于调试类似 "repeated read on failed websocket connection" 的问题
				panic(fmt.Sprintf("unrecoverable error while reading from Gemini: %v", err))
			}

			// 将消息完整读入缓冲区，以便在写入失败时可以重试
			data, err := io.ReadAll(reader)
			if err != nil {
				slog.Error("❌ WebSocket代理: 从Gemini读取消息体失败", "error", err, "userID", userID)
				return // 读取失败，终止转发
			}

			var writeErr error
			// 尝试写入数据，如果失败则重试
			for attempt := 1; attempt <= maxWriteFailures; attempt++ {
				clientWriteMutex.Lock()
				writeErr = clientConn.WriteMessage(messageType, data)
				clientWriteMutex.Unlock()

				if writeErr == nil {
					break // 写入成功
				}

				slog.Warn("⚠️ WebSocket代理: 写入客户端失败", "error", writeErr, "userID", userID, "attempt", attempt)

				// 如果不是最后一次尝试，则等待1秒后重试
				if attempt < maxWriteFailures {
					time.Sleep(1 * time.Second)
				}
			}

			// 如果所有重试都失败，则关闭连接
			if writeErr != nil {
				slog.Error("🚨 WebSocket代理: 写入客户端连续失败达到阈值，关闭连接", "userID", userID, "error", writeErr)
				return // 终止goroutine
			}

			geminiToClientCount++
			slog.Debug("📥 WebSocket代理: 转发Gemini消息到客户端",
				"userID", userID,
				"messageType", messageType,
				"dataSize", len(data),
				"messageCount", geminiToClientCount)
		}
	}()

	// 等待任一goroutine结束
	<-ctx.Done()
	slog.Info("🔚 WebSocket代理: 数据转发结束", "userID", userID)
}
