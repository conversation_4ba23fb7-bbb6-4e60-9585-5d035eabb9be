package handler

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"log/slog"
	"net"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"sync"
	"time"

	"encoding/json"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"

	"interviewmaster/internal/config"
	"interviewmaster/internal/utils"
)

const (
	// CloseServiceUnavailable 自定义WebSocket关闭码，表示服务不可用（例如，时长用尽）
	CloseServiceUnavailable = 4001
)

// GeminiProxyHandler Gemini代理处理器
type GeminiProxyHandler struct {
	upgrader websocket.Upgrader
}

// NewGeminiProxyHandler 创建Gemini代理处理器
func NewGeminiProxyHandler() *GeminiProxyHandler {
	return &GeminiProxyHandler{
		upgrader: websocket.Upgrader{
			CheckOrigin: func(r *http.Request) bool {
				// 允许所有来源，生产环境应该限制
				return true
			},
			// 🚀 优化缓冲区大小：减少缓冲延迟，适合音频流式传输
			ReadBufferSize:  1024 * 4, // 减少到4KB，降低缓冲延迟
			WriteBufferSize: 1024 * 4, // 减少到4KB，降低缓冲延迟
			// 启用压缩可能增加延迟，对音频流不启用
			EnableCompression: false,
		},
	}
}

// ProxyWebSocket 代理WebSocket连接到Gemini Live API
func (h *GeminiProxyHandler) ProxyWebSocket(c *gin.Context) {
	connManager := GetConnectionManager()
	slog.Info("🔗 收到WebSocket代理请求", "path", c.Request.URL.Path, "query", c.Request.URL.RawQuery)

	// 获取用户信息和token类型
	tokenType, _ := c.Get("token_type")
	var userID uint64

	if tokenType == "ephemeral" {
		// 对于临时令牌，我们需要从token中提取用户信息
		// 临时令牌是通过认证用户获取的，所以我们可以信任它
		// 但我们需要一种方式来获取用户ID

		// 方案1: 从数据库查询最近获取此临时令牌的用户
		// 方案2: 在临时令牌中编码用户信息
		// 方案3: 要求客户端同时传递用户ID

		// 这里我们暂时使用一个简化的方案：从请求中获取用户ID
		// 在生产环境中，应该有更安全的方式来验证临时令牌对应的用户
		userIDStr := c.Query("user_id")
		if userIDStr == "" {
			slog.Error("❌ WebSocket代理: 临时令牌缺少用户ID参数")
			utils.ErrorResponse(c, http.StatusBadRequest, "临时令牌缺少用户ID参数")
			return
		}

		var err error
		userID, err = strconv.ParseUint(userIDStr, 10, 64)
		if err != nil {
			slog.Error("❌ WebSocket代理: 用户ID格式错误", "userIDStr", userIDStr, "error", err)
			utils.ErrorResponse(c, http.StatusBadRequest, "用户ID格式错误")
			return
		}

		slog.Info("✅ WebSocket代理: 临时令牌认证成功", "userID", userID)
	} else {
		// 对于JWT token，从中间件设置的上下文获取用户ID
		userIDValue, exists := c.Get("user_id")
		if !exists {
			slog.Error("❌ WebSocket代理: 用户未认证")
			utils.ErrorResponse(c, http.StatusUnauthorized, "用户未认证")
			return
		}
		var ok bool
		userID, ok = userIDValue.(uint64)
		if !ok {
			slog.Error("❌ WebSocket代理: 用户ID格式错误", "userIDValue", userIDValue)
			utils.ErrorResponse(c, http.StatusInternalServerError, "用户ID格式错误")
			return
		}

		slog.Info("✅ WebSocket代理: JWT认证成功", "userID", userID)
	}

	slog.Info("✅ WebSocket代理: 用户认证成功", "userID", userID)

	// 获取临时令牌
	token := c.Query("access_token")
	if token == "" {
		token = c.Query("key")
	}
	if token == "" {
		slog.Error("❌ WebSocket代理: 缺少访问令牌", "query", c.Request.URL.RawQuery)
		utils.ErrorResponse(c, http.StatusBadRequest, "缺少访问令牌")
		return
	}

	slog.Info("✅ WebSocket代理: 获取到访问令牌", "tokenPrefix", token[:min(len(token), 20)]+"...")

	// 获取方法参数
	method := c.DefaultQuery("method", "BidiGenerateContent")
	if strings.Contains(token, "auth_tokens/") {
		method = "BidiGenerateContentConstrained"
		slog.Info("🔄 WebSocket代理: 检测到临时令牌，使用受限方法", "method", method)
	} else {
		slog.Info("🔄 WebSocket代理: 使用标准方法", "method", method)
	}

	cfg := config.GlobalConfig
	if cfg == nil {
		slog.Error("❌ WebSocket代理: 配置未初始化")
		utils.ErrorResponse(c, http.StatusInternalServerError, "配置未初始化")
		return
	}

	// 构建目标WebSocket URL
	var keyParam string
	if strings.Contains(token, "auth_tokens/") {
		keyParam = "access_token=" + token
	} else {
		keyParam = "key=" + token
	}

	targetURL := fmt.Sprintf("wss://generativelanguage.googleapis.com/ws/google.ai.generativelanguage.v1alpha.GenerativeService.%s?%s", method, keyParam)

	slog.Info("🎯 WebSocket代理: 构建目标URL", "userID", userID, "targetURL", targetURL[:min(len(targetURL), 100)]+"...")

	slog.Info("🔄 WebSocket代理: 开始升级客户端连接")

	// 升级客户端连接为WebSocket
	clientConn, err := h.upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		slog.Error("❌ WebSocket代理: 升级客户端连接失败", "error", err, "userID", userID)
		return
	}

	defer func() {
		slog.Info("🔌 WebSocket代理: 关闭客户端连接", "userID", userID)
		connManager.HandleDisconnect(clientConn)
		connManager.Unregister(clientConn)
		clientConn.Close()
	}()

	slog.Info("✅ WebSocket代理: 客户端连接升级成功", "userID", userID)

	// 连接到Gemini API
	slog.Info("🌐 WebSocket代理: 开始连接到Gemini API", "targetURL", targetURL[:min(len(targetURL), 100)]+"...")

	// 🚀 优化dialer设置：减少缓冲区，降低延迟
	dialer := websocket.DefaultDialer
	dialer.HandshakeTimeout = 30 * time.Second
	dialer.ReadBufferSize = 1024 * 4  // 减少到4KB，降低缓冲延迟
	dialer.WriteBufferSize = 1024 * 4 // 减少到4KB，降低缓冲延迟
	dialer.EnableCompression = false  // 禁用压缩，减少CPU开销和延迟

	// 🚀 优化TCP连接参数：减少延迟，提高响应速度
	dialer.NetDialContext = func(ctx context.Context, network, addr string) (net.Conn, error) {
		d := &net.Dialer{
			Timeout:   15 * time.Second, // 减少连接超时
			KeepAlive: 15 * time.Second, // 减少keepalive间隔，更快检测连接问题
		}
		conn, err := d.DialContext(ctx, network, addr)
		if err != nil {
			return nil, err
		}

		// 设置TCP优化参数
		if tcpConn, ok := conn.(*net.TCPConn); ok {
			tcpConn.SetKeepAlive(true)
			tcpConn.SetKeepAlivePeriod(15 * time.Second) // 更短的keepalive周期
			tcpConn.SetNoDelay(true)                     // 禁用Nagle算法，减少延迟

			localAddr := tcpConn.LocalAddr()
			remoteAddr := tcpConn.RemoteAddr()
			slog.Info("🔗 WebSocket代理: TCP连接建立（低延迟优化）",
				"userID", userID,
				"local", localAddr.String(),
				"remote", remoteAddr.String(),
				"keepalive", "15s",
				"nodelay", true)
		}

		return conn, nil
	}

	// 设置代理（如果配置了）
	if cfg.Gemini.ProxyURL != "" {
		proxyURL, err := url.Parse(cfg.Gemini.ProxyURL)
		if err == nil {
			dialer.Proxy = http.ProxyURL(proxyURL)
			slog.Info("🔗 WebSocket代理: 使用外部代理连接", "proxy", cfg.Gemini.ProxyURL)
		} else {
			slog.Error("❌ WebSocket代理: 解析外部代理URL失败", "proxyURL", cfg.Gemini.ProxyURL, "error", err)
		}
	}

	geminiConn, resp, err := dialer.Dial(targetURL, nil)
	if err != nil {
		slog.Error("❌ WebSocket代理: 连接到Gemini API失败",
			"error", err,
			"userID", userID,
			"targetURL", targetURL[:min(len(targetURL), 100)]+"...",
			"responseStatus", func() string {
				if resp != nil {
					return resp.Status
				}
				return "无响应"
			}())

		// 向客户端发送错误信息
		clientConn.WriteMessage(websocket.CloseMessage,
			websocket.FormatCloseMessage(websocket.CloseInternalServerErr,
				fmt.Sprintf("无法连接到AI服务: %v", err)))
		return
	}

	defer func() {
		slog.Info("🔌 WebSocket代理: 关闭Gemini连接", "userID", userID)
		geminiConn.Close()
	}()

	slog.Info("✅ WebSocket代理: 成功连接到Gemini API（低延迟模式）", "userID", userID)

	// 🚀 优化超时设置：保持合理的超时时间，但不设置过长的初始超时
	// 在实际读取时动态设置超时，避免长时间阻塞

	// 创建双向数据转发
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// 创建写入锁，防止并发写入WebSocket连接
	var clientWriteMutex sync.Mutex

	slog.Info("🔄 WebSocket代理: 开始双向数据转发", "userID", userID)

	// 统计数据
	var clientToGeminiCount, geminiToClientCount int64

	// 添加连接状态监控（不包含心跳，保持与直连模式一致）
	go func() {
		ticker := time.NewTicker(30 * time.Second) // 每30秒记录一次连接状态
		defer ticker.Stop()

		for {
			select {
			case <-ticker.C:
				// 检查连接状态并记录详细信息
				clientState := "unknown"
				geminiState := "unknown"

				if clientConn != nil {
					switch clientConn.UnderlyingConn() {
					case nil:
						clientState = "closed"
					default:
						clientState = "active"
					}
				}

				if geminiConn != nil {
					switch geminiConn.UnderlyingConn() {
					case nil:
						geminiState = "closed"
					default:
						geminiState = "active"
					}
				}

				slog.Info("📊 WebSocket代理: 连接状态监控",
					"userID", userID,
					"clientState", clientState,
					"geminiState", geminiState,
					"clientToGemini", clientToGeminiCount,
					"geminiToClient", geminiToClientCount)

			case <-ctx.Done():
				slog.Info("🛑 WebSocket代理: 停止连接状态监控", "userID", userID)
				return
			}
		}
	}()

	// 客户端到Gemini的数据转发 - 优化为流式转发
	go func() {
		defer func() {
			if r := recover(); r != nil {
				slog.Error("❌ WebSocket代理: 客户端转发goroutine发生panic",
					"userID", userID,
					"panic", r,
					"messageCount", clientToGeminiCount)
			}
			slog.Info("📤 WebSocket代理: 客户端到Gemini转发结束", "userID", userID, "messageCount", clientToGeminiCount)
			cancel()
			geminiConn.Close() // 确保另一端也能感知到关闭
		}()

		for {
			if ctx.Err() != nil {
				return
			}

			// 设置一个比客户端心跳间隔（30秒）更长的超时（75秒）。
			clientConn.SetReadDeadline(time.Now().Add(75 * time.Second))
			messageType, reader, err := clientConn.NextReader()
			if err != nil {
				// 检查是否是超时错误
				if netErr, ok := err.(net.Error); ok && netErr.Timeout() {
					slog.Error("🚨 WebSocket代理: 客户端读取超时（长时间未收到心跳），关闭连接", "userID", userID, "timeout", "75s")
					return // 终止goroutine
				}

				if websocket.IsCloseError(err, websocket.CloseGoingAway, websocket.CloseNormalClosure) {
					slog.Info("🔌 WebSocket代理: 客户端正常关闭连接", "userID", userID)
				} else {
					slog.Error("❌ WebSocket代理: 读取客户端消息时发生错误，关闭连接", "error", err, "userID", userID)
				}
				return // 任何其他错误都终止goroutine
			}

			// 🚀 流式转发优化：对于二进制消息，直接流式转发
			if messageType == websocket.BinaryMessage {
				writer, err := geminiConn.NextWriter(messageType)
				if err != nil {
					if strings.Contains(err.Error(), "use of closed network connection") {
						slog.Info("🔌 WebSocket代理: Gemini连接已关闭，停止转发", "userID", userID)
					} else {
						slog.Error("❌ WebSocket代理: 创建Gemini写入器失败", "error", err, "userID", userID)
					}
					return
				}

				buf := make([]byte, 1024)
				_, copyErr := io.CopyBuffer(writer, reader, buf)
				closeErr := writer.Close()

				if copyErr != nil {
					slog.Error("❌ WebSocket代理: 流式转发二进制数据失败", "error", copyErr, "userID", userID)
					return
				}
				if closeErr != nil && !strings.Contains(closeErr.Error(), "use of closed network connection") {
					slog.Error("❌ WebSocket代理: 关闭Gemini写入器失败", "error", closeErr, "userID", userID)
					return
				}

				clientToGeminiCount++
				slog.Debug("🎵 WebSocket代理: 流式转发二进制数据", "userID", userID, "messageCount", clientToGeminiCount)
				continue
			}

			// 🚀 对于文本消息，先尝试流式转发，只有控制消息才读取内容
			if messageType == websocket.TextMessage {
				// 先peek一小部分数据来判断消息类型，避免读取整个消息
				peekBuf := make([]byte, 256) // 只读取前256字节来判断
				n, err := reader.Read(peekBuf)
				if err != nil && err != io.EOF {
					slog.Error("❌ WebSocket代理: 读取消息头失败", "error", err, "userID", userID)
					return
				}

				peekData := peekBuf[:n]
				isControlMessage := false

				// 快速检查是否为控制消息（避免完整JSON解析）
				if bytes.Contains(peekData, []byte(`"type"`)) {
					if bytes.Contains(peekData, []byte(`"register_session"`)) ||
						bytes.Contains(peekData, []byte(`"heartbeat"`)) {
						isControlMessage = true
					}
				}

				if isControlMessage {
					// 只有控制消息才完整读取
					remainingData, err := io.ReadAll(reader)
					if err != nil {
						slog.Error("❌ WebSocket代理: 读取控制消息失败", "error", err, "userID", userID)
						return
					}

					fullData := append(peekData, remainingData...)
					var msg map[string]interface{}
					if json.Unmarshal(fullData, &msg) == nil {
						if msgType, ok := msg["type"].(string); ok {
							switch msgType {
							case "register_session":
								sessionID, _ := msg["session_id"].(string)
								if sessionID != "" {
									connManager.Register(clientConn, sessionID)
									slog.Info("✅ WebSocket代理: 注册会话成功", "userID", userID, "sessionID", sessionID)
								}
								continue // 处理完后，跳过转发
							case "heartbeat":
								slog.Debug("❤️ WebSocket代理: 收到客户端心跳", "userID", userID)
								continue // 收到心跳，跳过转发
							}
						}
					}
				} else {
					// 🚀 非控制消息（包括音频数据）直接流式转发，不解析JSON
					writer, err := geminiConn.NextWriter(messageType)
					if err != nil {
						if strings.Contains(err.Error(), "use of closed network connection") {
							slog.Info("🔌 WebSocket代理: Gemini连接已关闭，停止转发", "userID", userID)
						} else {
							slog.Error("❌ WebSocket代理: 创建Gemini写入器失败", "error", err, "userID", userID)
						}
						return
					}

					// 先写入已读取的peek数据
					if _, err := writer.Write(peekData); err != nil {
						slog.Error("❌ WebSocket代理: 写入peek数据失败", "error", err, "userID", userID)
						writer.Close()
						return
					}

					// 然后流式转发剩余数据
					buf := make([]byte, 1024)
					_, copyErr := io.CopyBuffer(writer, reader, buf)
					closeErr := writer.Close()

					if copyErr != nil {
						slog.Error("❌ WebSocket代理: 流式转发音频JSON失败", "error", copyErr, "userID", userID)
						return
					}
					if closeErr != nil && !strings.Contains(closeErr.Error(), "use of closed network connection") {
						slog.Error("❌ WebSocket代理: 关闭Gemini写入器失败", "error", closeErr, "userID", userID)
						return
					}

					clientToGeminiCount++
					slog.Debug("🎵 WebSocket代理: 流式转发音频JSON", "userID", userID, "messageCount", clientToGeminiCount)
					continue
				}
			}

			// 检查Gemini连接状态
			if geminiConn == nil {
				slog.Warn("⚠️ WebSocket代理: Gemini连接为空，无法转发", "userID", userID)
				return
			}

			// 转发文本消息（JSON数据）
			writer, err := geminiConn.NextWriter(messageType)
			if err != nil {
				if strings.Contains(err.Error(), "use of closed network connection") {
					slog.Info("🔌 WebSocket代理: Gemini连接已关闭，停止转发", "userID", userID)
				} else {
					slog.Error("❌ WebSocket代理: 创建Gemini写入器失败", "error", err, "userID", userID)
				}
				return
			}

			if _, writeErr := writer.Write(data); writeErr != nil {
				slog.Error("❌ WebSocket代理: 转发文本消息到Gemini失败", "error", writeErr, "userID", userID)
				writer.Close()
				return
			}

			if err := writer.Close(); err != nil {
				if !strings.Contains(err.Error(), "use of closed network connection") {
					slog.Error("❌ WebSocket代理: 关闭Gemini写入器失败", "error", err, "userID", userID)
				}
				return
			}

			clientToGeminiCount++
			slog.Debug("📤 WebSocket代理: 转发文本消息到Gemini",
				"userID", userID,
				"messageType", messageType,
				"dataSize", len(data),
				"messageCount", clientToGeminiCount)
		}
	}()

	// Gemini到客户端的数据转发 - 优化为流式转发，移除重试机制
	go func() {
		defer func() {
			if r := recover(); r != nil {
				slog.Error("❌ WebSocket代理: Gemini转发goroutine发生panic",
					"userID", userID,
					"panic", r,
					"messageCount", geminiToClientCount)
			}
			slog.Info("📥 WebSocket代理: Gemini到客户端转发结束", "userID", userID, "messageCount", geminiToClientCount)
			cancel()
			clientConn.Close() // 确保另一端也能感知到关闭
		}()

		for {
			if ctx.Err() != nil {
				return
			}
			if geminiConn == nil {
				slog.Warn("⚠️ WebSocket代理: Gemini连接为空，退出转发", "userID", userID)
				return
			}

			geminiConn.SetReadDeadline(time.Now().Add(600 * time.Second))
			messageType, reader, err := geminiConn.NextReader()
			if err != nil {
				if websocket.IsCloseError(err, websocket.CloseGoingAway, websocket.CloseNormalClosure) {
					slog.Info("🔌 WebSocket代理: Gemini正常关闭连接", "userID", userID)
				} else if strings.Contains(err.Error(), "i/o timeout") {
					slog.Debug("ℹ️ WebSocket代理: Gemini读取超时（正常现象）", "userID", userID)
					continue
				} else {
					slog.Error("❌ WebSocket代理: 读取Gemini消息时发生错误，关闭连接",
						"error", err,
						"error_type", fmt.Sprintf("%T", err),
						"userID", userID)

					// 检查是否是特定的服务不可用错误
					if wsErr, ok := err.(*websocket.CloseError); ok && wsErr.Code == websocket.CloseInternalServerErr &&
						strings.Contains(wsErr.Text, "The service is currently unavailable.") {
						slog.Warn("⚠️ WebSocket代理: 检测到Gemini服务不可用错误，发送自定义关闭码", "userID", userID)
						clientWriteMutex.Lock()
						clientConn.WriteMessage(websocket.CloseMessage,
							websocket.FormatCloseMessage(CloseServiceUnavailable, "当前服务时长已用完，请充值"))
						clientWriteMutex.Unlock()
						return // 终止goroutine
					}
				}
				// 发生不可恢复的错误时，主动panic，以便捕获更详细的堆栈信息
				panic(fmt.Sprintf("unrecoverable error while reading from Gemini: %v", err))
			}

			// 🚀 流式转发优化：对于二进制消息（音频数据），直接流式转发
			if messageType == websocket.BinaryMessage {
				// 音频数据直接流式转发到客户端，零拷贝
				clientWriteMutex.Lock()
				writer, err := clientConn.NextWriter(messageType)
				if err != nil {
					clientWriteMutex.Unlock()
					slog.Error("❌ WebSocket代理: 创建客户端写入器失败", "error", err, "userID", userID)
					return
				}

				// 使用更小的缓冲区进行流式拷贝，进一步减少延迟
				buf := make([]byte, 1024) // 1KB缓冲区，与客户端到Gemini保持一致
				_, copyErr := io.CopyBuffer(writer, reader, buf)
				closeErr := writer.Close()
				clientWriteMutex.Unlock()

				if copyErr != nil {
					slog.Error("❌ WebSocket代理: 流式转发音频数据到客户端失败", "error", copyErr, "userID", userID)
					return
				}
				if closeErr != nil {
					slog.Error("❌ WebSocket代理: 关闭客户端写入器失败", "error", closeErr, "userID", userID)
					return
				}

				geminiToClientCount++
				slog.Debug("🎵 WebSocket代理: 流式转发音频数据到客户端", "userID", userID, "messageCount", geminiToClientCount)
				continue
			}

			// 对于文本消息，读取后直接转发，不重试
			data, err := io.ReadAll(reader)
			if err != nil {
				slog.Error("❌ WebSocket代理: 从Gemini读取文本消息失败", "error", err, "userID", userID)
				return
			}

			// 直接写入客户端，不重试
			clientWriteMutex.Lock()
			writeErr := clientConn.WriteMessage(messageType, data)
			clientWriteMutex.Unlock()

			if writeErr != nil {
				slog.Error("❌ WebSocket代理: 写入客户端失败，关闭连接", "userID", userID, "error", writeErr)
				return // 写入失败直接终止，不重试
			}

			geminiToClientCount++
			slog.Debug("📥 WebSocket代理: 转发文本消息到客户端",
				"userID", userID,
				"messageType", messageType,
				"dataSize", len(data),
				"messageCount", geminiToClientCount)
		}
	}()

	// 等待任一goroutine结束
	<-ctx.Done()
	slog.Info("🔚 WebSocket代理: 数据转发结束", "userID", userID)
}
