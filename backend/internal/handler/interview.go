package handler

import (
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"

	"interviewmaster/internal/middleware"
	"interviewmaster/internal/model"
	"interviewmaster/internal/service"
	"interviewmaster/internal/utils"
)

// InterviewHandler 面试处理器
type InterviewHandler struct {
	interviewService *service.InterviewService
	userService      *service.UserService
}

// NewInterviewHandler 创建面试处理器实例
func NewInterviewHandler() *InterviewHandler {
	return &InterviewHandler{
		interviewService: service.NewInterviewService(),
		userService:      service.NewUserService(),
	}
}

// StartInterviewRequest 开始面试请求
type StartInterviewRequest struct {
	Domain string `json:"domain" binding:"required"`
}

// StartInterviewResponse 开始面试响应
type StartInterviewResponse struct {
	SessionID          string `json:"session_id"`
	CanUseTrial        bool   `json:"can_use_trial"`
	TrialRemainingTime uint32 `json:"trial_remaining_time"` // 试用剩余时长(秒)
	PaidRemainingTime  uint32 `json:"paid_remaining_time"`  // 付费剩余时长(秒)
	TotalRemainingTime uint32 `json:"total_remaining_time"` // 总剩余时长(秒)
}

// StartInterview 开始面试
func (h *InterviewHandler) StartInterview(c *gin.Context) {
	userID := middleware.GetUserID(c)
	if userID == 0 {
		utils.UnauthorizedResponse(c, "用户未登录")
		return
	}

	var req StartInterviewRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ValidationErrorResponse(c, "参数错误: "+err.Error())
		return
	}

	// 获取用户信息
	user, err := h.userService.GetUserByID(userID)
	if err != nil {
		utils.InternalErrorResponse(c, "获取用户信息失败")
		return
	}
	if user == nil {
		utils.NotFoundResponse(c, "用户不存在")
		return
	}

	// 检查用户是否有可用时长
	trialRemainingTime := uint32(0)
	if user.FreeTrialUsedDuration < user.FreeTrialTotalDuration {
		trialRemainingTime = user.FreeTrialTotalDuration - user.FreeTrialUsedDuration
	}

	paidRemainingTime := user.BalanceDuration
	totalRemainingTime := trialRemainingTime + paidRemainingTime

	// 检查用户是否有足够的时长（至少需要1分钟才能开始面试）
	if totalRemainingTime < 60 {
		utils.ErrorResponse(c, http.StatusBadRequest, "您的可用时长不足1分钟，无法开始面试，请购买套餐")
		return
	}

	// 生成会话ID
	sessionID := utils.GenerateSessionID()

	// 创建面试会话记录
	now := time.Now()
	interviewLog := &model.InterviewLog{
		UserID:        userID,
		SessionID:     sessionID,
		StartedAt:     &now,
		UsedTrialTime: trialRemainingTime > 0,
	}

	if err := h.interviewService.CreateInterviewLog(interviewLog); err != nil {
		utils.InternalErrorResponse(c, "创建面试会话失败")
		return
	}

	response := &StartInterviewResponse{
		SessionID:          sessionID,
		CanUseTrial:        trialRemainingTime > 0,
		TrialRemainingTime: trialRemainingTime,
		PaidRemainingTime:  paidRemainingTime,
		TotalRemainingTime: totalRemainingTime,
	}

	utils.SuccessResponse(c, response)
}

// EndInterviewRequest 结束面试请求
type EndInterviewRequest struct {
	SessionID string `json:"session_id" binding:"required"`
}

// EndInterview 结束面试
func (h *InterviewHandler) EndInterview(c *gin.Context) {
	userID := middleware.GetUserID(c)
	if userID == 0 {
		utils.UnauthorizedResponse(c, "用户未登录")
		return
	}

	var req EndInterviewRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ValidationErrorResponse(c, "参数错误: "+err.Error())
		return
	}

	// 结束面试会话并计算时长
	if err := h.interviewService.EndInterviewSession(userID, req.SessionID); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, err.Error())
		return
	}

	utils.SuccessResponseWithMessage(c, "面试结束", nil)
}

// GetInterviewStats 获取面试统计
func (h *InterviewHandler) GetInterviewStats(c *gin.Context) {
	userID := middleware.GetUserID(c)
	if userID == 0 {
		utils.UnauthorizedResponse(c, "用户未登录")
		return
	}

	stats, err := h.interviewService.GetInterviewStats(userID)
	if err != nil {
		utils.InternalErrorResponse(c, "获取面试统计失败")
		return
	}

	utils.SuccessResponse(c, stats)
}

// UpdateInterviewFeedback 更新面试反馈
func (h *InterviewHandler) UpdateInterviewFeedback(c *gin.Context) {
	userID := middleware.GetUserID(c)
	if userID == 0 {
		utils.UnauthorizedResponse(c, "用户未登录")
		return
	}

	// 获取面试日志ID
	logIDStr := c.Param("id")
	logID, err := strconv.ParseUint(logIDStr, 10, 64)
	if err != nil {
		utils.ValidationErrorResponse(c, "无效的日志ID")
		return
	}

	var req struct {
		Feedback int8 `json:"feedback" binding:"required,oneof=-1 0 1"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ValidationErrorResponse(c, "参数错误: "+err.Error())
		return
	}

	// 更新反馈
	if err := h.interviewService.UpdateInterviewLogFeedback(logID, userID, req.Feedback); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, err.Error())
		return
	}

	utils.SuccessResponseWithMessage(c, "反馈更新成功", nil)
}

// EvaluateInterviewRequest 评估面试请求
type EvaluateInterviewRequest struct {
	SessionID  string `json:"session_id" binding:"required"`
	Transcript string `json:"transcript" binding:"required"`
	Profession string `json:"profession" binding:"required"`
	Difficulty string `json:"difficulty" binding:"required"`
	Duration   int    `json:"duration"` // 面试时长（分钟）
}

// InterviewScore 面试评分结果
type InterviewScore struct {
	OverallScore int `json:"overall_score"`
	Dimensions   struct {
		Communication         int `json:"communication"`
		ProfessionalKnowledge int `json:"professional_knowledge"`
		ProblemSolving        int `json:"problem_solving"`
		AttitudeConfidence    int `json:"attitude_confidence"`
	} `json:"dimensions"`
	Feedback    string   `json:"feedback"`
	Suggestions []string `json:"suggestions"`
}

// EvaluateInterview 评估模拟面试表现
func (h *InterviewHandler) EvaluateInterview(c *gin.Context) {
	userID := middleware.GetUserID(c)
	if userID == 0 {
		utils.UnauthorizedResponse(c, "用户未登录")
		return
	}

	var req EvaluateInterviewRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ValidationErrorResponse(c, "请求参数错误: "+err.Error())
		return
	}

	// 构建AI评分提示词
	evaluationPrompt := buildEvaluationPrompt(req.Profession, req.Difficulty, req.Duration, req.Transcript)

	// 调用AI进行评分
	score, err := h.callAIForEvaluation(evaluationPrompt)
	if err != nil {
		utils.InternalErrorResponse(c, "AI评分失败: "+err.Error())
		return
	}

	// TODO: 保存评分结果到数据库
	// 这里可以扩展保存评分历史记录的功能

	utils.SuccessResponse(c, score)
}

// buildEvaluationPrompt 构建评分提示词
func buildEvaluationPrompt(profession, difficulty string, duration int, transcript string) string {
	professionMap := map[string]string{
		"technical_interview":    "技术岗位",
		"hr_interview":          "人力资源",
		"sales_interview":       "销售岗位",
		"marketing_interview":   "市场营销",
		"finance_interview":     "金融岗位",
		"legal_interview":       "法律岗位",
		"medical_interview":     "医疗岗位",
		"education_interview":   "教育岗位",
		"architecture_interview": "建筑设计",
		"psychology_interview":  "心理咨询",
	}

	difficultyMap := map[string]string{
		"junior":       "初级",
		"intermediate": "中级",
		"senior":       "高级",
	}

	professionName := professionMap[profession]
	if professionName == "" {
		professionName = "通用岗位"
	}

	difficultyName := difficultyMap[difficulty]
	if difficultyName == "" {
		difficultyName = "中级"
	}

	return fmt.Sprintf(`您是一位专业的面试评估专家。请根据以下面试对话记录，对候选人进行客观、专业的评分。

面试信息：
- 面试职业：%s
- 难度级别：%s
- 面试时长：%d分钟

面试对话记录：
%s

评分要求：
请从以下4个维度进行评分（每项0-25分，总分100分）：

1. 沟通表达（0-25分）：
   - 语言流畅度和清晰度
   - 逻辑思维和表达结构
   - 回答的完整性和准确性

2. 专业知识（0-25分）：
   - 专业技能和知识深度
   - 行业理解和认知水平
   - 知识应用和实践能力

3. 问题解决（0-25分）：
   - 分析问题的思维逻辑
   - 解决方案的创新性和可行性
   - 应对复杂情况的能力

4. 态度自信（0-25分）：
   - 自信程度和积极性
   - 应变能力和抗压性
   - 学习意愿和成长潜力

请严格按照以下JSON格式返回评分结果（不要包含任何其他文字）：
{
  "overall_score": 总分数字,
  "dimensions": {
    "communication": 沟通表达得分,
    "professional_knowledge": 专业知识得分,
    "problem_solving": 问题解决得分,
    "attitude_confidence": 态度自信得分
  },
  "feedback": "详细的面试表现评价，200字以内",
  "suggestions": [
    "具体的改进建议1",
    "具体的改进建议2",
    "具体的改进建议3"
  ]
}`, professionName, difficultyName, duration, transcript)
}

// callAIForEvaluation 调用AI进行评分
func (h *InterviewHandler) callAIForEvaluation(prompt string) (*InterviewScore, error) {
	// 这里使用模拟的AI评分结果
	// 在实际项目中，应该调用真实的AI API（如OpenAI、Google Gemini等）
	
	// 基于对话长度和内容复杂度生成动态评分
	score := generateMockScore(prompt)
	
	return score, nil
}

// generateMockScore 生成模拟评分（实际项目中应替换为真实AI调用）
func generateMockScore(prompt string) *InterviewScore {
	// 基于提示词长度简单模拟评分
	transcriptLength := len(prompt)
	
	// 根据对话长度调整基础分数
	baseScore := 70
	if transcriptLength > 2000 {
		baseScore = 80
	}
	if transcriptLength > 3000 {
		baseScore = 85
	}
	
	// 各维度分数（模拟）
	communication := baseScore/4 + 2
	professional := baseScore/4 - 1
	problemSolving := baseScore/4 + 1
	attitude := baseScore/4 - 2
	
	// 确保分数在合理范围内
	if communication > 25 { communication = 25 }
	if professional > 25 { professional = 25 }
	if problemSolving > 25 { problemSolving = 25 }
	if attitude > 25 { attitude = 25 }
	
	if communication < 10 { communication = 10 }
	if professional < 10 { professional = 10 }
	if problemSolving < 10 { problemSolving = 10 }
	if attitude < 10 { attitude = 10 }
	
	totalScore := communication + professional + problemSolving + attitude
	
	return &InterviewScore{
		OverallScore: totalScore,
		Dimensions: struct {
			Communication         int `json:"communication"`
			ProfessionalKnowledge int `json:"professional_knowledge"`
			ProblemSolving        int `json:"problem_solving"`
			AttitudeConfidence    int `json:"attitude_confidence"`
		}{
			Communication:         communication,
			ProfessionalKnowledge: professional,
			ProblemSolving:        problemSolving,
			AttitudeConfidence:    attitude,
		},
		Feedback: generateFeedback(totalScore),
		Suggestions: generateSuggestions(communication, professional, problemSolving, attitude),
	}
}

// generateFeedback 生成评价反馈
func generateFeedback(score int) string {
	if score >= 90 {
		return "您在面试中表现优秀！回答问题思路清晰，专业知识扎实，表达流畅自然。展现出了良好的专业素养和沟通能力，是一位非常有潜力的候选人。继续保持这种优秀的表现，相信您会在职场中取得更大的成功。"
	} else if score >= 80 {
		return "您在面试中表现良好，回答问题思路清晰，专业知识掌握扎实。在沟通表达方面表现出色，能够准确理解问题并给出合理回答。还有一些提升空间，建议在某些方面继续加强，整体表现值得肯定。"
	} else if score >= 70 {
		return "您在面试中表现中等，基本能够回答面试官的问题，展现了一定的专业基础。在表达和逻辑方面还有提升空间，建议多加练习和准备。通过持续学习和改进，相信您的面试表现会有显著提升。"
	} else if score >= 60 {
		return "您在面试中的表现还需要改进。虽然展现了一定的基础知识，但在回答的完整性、逻辑性和专业深度方面还有较大提升空间。建议加强专业知识学习，多进行面试练习，提高表达和沟通能力。"
	} else {
		return "您的面试表现需要显著改进。建议系统性地加强专业知识学习，提高沟通表达能力，多进行模拟面试练习。通过充分的准备和练习，相信您的面试技能会有很大提升。不要气馁，继续努力！"
	}
}

// generateSuggestions 生成改进建议
func generateSuggestions(communication, professional, problemSolving, attitude int) []string {
	suggestions := []string{}
	
	if communication < 20 {
		suggestions = append(suggestions, "建议加强语言表达能力，多练习口语表达和逻辑组织")
	}
	
	if professional < 20 {
		suggestions = append(suggestions, "建议深入学习专业知识，关注行业最新发展动态")
	}
	
	if problemSolving < 20 {
		suggestions = append(suggestions, "建议提升问题分析和解决能力，多思考解决方案的可行性")
	}
	
	if attitude < 20 {
		suggestions = append(suggestions, "建议增强自信心，保持积极的面试态度和学习心态")
	}
	
	// 确保至少有3条建议
	if len(suggestions) < 3 {
		suggestions = append(suggestions, "多进行模拟面试练习，提高面试技巧和应变能力")
		suggestions = append(suggestions, "准备具体的工作案例和项目经验，增强回答的说服力")
		suggestions = append(suggestions, "关注面试礼仪和职业形象，给面试官留下良好印象")
	}
	
	// 限制建议数量为3-5条
	if len(suggestions) > 5 {
		suggestions = suggestions[:5]
	}
	
	return suggestions
}
