package handler

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log/slog"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"

	"interviewmaster/internal/config"
	"interviewmaster/internal/service"
	"interviewmaster/internal/utils"
)

const (
	defaultTokenUses          = 1
	defaultTokenExpireMinutes = 30
	maxTokenExpireMinutes     = 60
	liveConnectAPIVersion     = "v1alpha"
	newSessionCreationWindow  = 30 * time.Minute // 增加到30分钟，与令牌过期时间一致
)

// min 返回两个整数中的较小值
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// GeminiHandler Gemini处理器
type GeminiHandler struct {
	genaiClient interface{} // 不使用genai客户端
	userService *service.UserService
}

// NewGeminiHandler 创建Gemini处理器实例
func NewGeminiHandler(ctx context.Context) (*GeminiHandler, error) {
	cfg := config.GlobalConfig
	if cfg == nil || cfg.Gemini.APIKey == "" {
		return nil, fmt.Errorf("Gemini配置或API Key未初始化")
	}

	return &GeminiHandler{
		genaiClient: nil, // 使用REST API，不需要客户端
		userService: service.NewUserService(),
	}, nil
}

// CreateEphemeralTokenRequest 创建临时令牌请求
type CreateEphemeralTokenRequest struct {
	PromptVersion string `json:"prompt_version"` // 提示词版本，可选
	Uses          int    `json:"uses"`           // 使用次数，默认1
	ExpireMinutes int    `json:"expire_minutes"` // 过期时间（分钟），默认30
}

// CreateEphemeralTokenResponse 创建临时令牌响应
type CreateEphemeralTokenResponse struct {
	Token     string    `json:"token"`
	ExpiresAt time.Time `json:"expires_at"`
	Uses      int       `json:"uses"`
}

// EphemeralTokenWithConfigResponse 包含配置的临时令牌响应
type EphemeralTokenWithConfigResponse struct {
	Token            string    `json:"token"`
	ExpiresAt        time.Time `json:"expires_at"`
	Uses             int       `json:"uses"`
	RemainingSeconds uint32    `json:"remaining_seconds"`
	RemainingMinutes uint32    `json:"remaining_minutes"`
	// Gemini配置
	GeminiConfig struct {
		ConnectionMode string `json:"connection_mode"`
		Model          string `json:"model"`
		WebSocketHost  string `json:"websocket_host"`
		APIVersion     string `json:"api_version"`
	} `json:"gemini_config"`
}

// CreateEphemeralToken 创建临时令牌
func (h *GeminiHandler) CreateEphemeralToken(c *gin.Context) {
	// 获取用户信息
	userIDValue, exists := c.Get("user_id")
	if !exists {
		utils.ErrorResponse(c, http.StatusUnauthorized, "用户未认证")
		return
	}
	userID, ok := userIDValue.(uint64)
	if !ok {
		utils.ErrorResponse(c, http.StatusInternalServerError, "用户ID格式错误")
		return
	}

	var req CreateEphemeralTokenRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ValidationErrorResponse(c, "参数错误: "+err.Error())
		return
	}

	// 获取用户信息和剩余时长
	user, err := h.userService.GetUserByID(userID)
	if err != nil {
		utils.InternalErrorResponse(c, "获取用户信息失败")
		return
	}
	if user == nil {
		utils.NotFoundResponse(c, "用户不存在")
		return
	}

	// 计算用户总剩余时长
	trialRemainingTime := uint32(0)
	if user.FreeTrialUsedDuration < user.FreeTrialTotalDuration {
		trialRemainingTime = user.FreeTrialTotalDuration - user.FreeTrialUsedDuration
	}
	totalRemainingSeconds := trialRemainingTime + user.BalanceDuration

	// 检查用户是否有剩余时长（至少需要1分钟才能开始面试）
	if totalRemainingSeconds < 60 {
		utils.ErrorResponse(c, http.StatusForbidden, "您的可用时长不足1分钟，无法开始面试，请购买套餐后继续使用")
		return
	}

	// 设置默认值
	if req.Uses <= 0 {
		req.Uses = defaultTokenUses
	}

	// 过期时间根据用户剩余时长计算，而不是使用固定的ExpireMinutes
	req.ExpireMinutes = int(totalRemainingSeconds/60) + 1 // 转换为分钟并加1分钟缓冲

	// 验证参数（放宽限制，因为现在是根据用户时长动态计算）
	if req.ExpireMinutes > 24*60 { // 最大24小时
		req.ExpireMinutes = 24 * 60
	}

	// 调用真实的Gemini API
	token, err := h.createGeminiEphemeralToken(c.Request.Context(), userID, &req)
	if err != nil {
		slog.Error("Failed to create ephemeral token", "error", err, "userID", userID)
		utils.ErrorResponse(c, http.StatusInternalServerError, "创建临时令牌失败，请稍后重试")
		return
	}

	// 获取全局配置
	cfg := config.GlobalConfig
	if cfg == nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "配置未初始化")
		return
	}

	// 构建包含配置的响应
	response := EphemeralTokenWithConfigResponse{
		Token:            token.Token,
		ExpiresAt:        token.ExpiresAt,
		Uses:             token.Uses,
		RemainingSeconds: totalRemainingSeconds,
		RemainingMinutes: totalRemainingSeconds / 60,
	}

	// 添加Gemini配置
	response.GeminiConfig.ConnectionMode = cfg.Gemini.ConnectionMode
	response.GeminiConfig.Model = cfg.Gemini.Model
	response.GeminiConfig.WebSocketHost = cfg.Gemini.WebSocketHost
	response.GeminiConfig.APIVersion = cfg.Gemini.APIVersion

	slog.Info("✅ 返回临时令牌和Gemini配置",
		"userID", userID,
		"tokenLength", len(token.Token),
		"connectionMode", response.GeminiConfig.ConnectionMode)

	utils.SuccessResponse(c, response)
}

// createGeminiEphemeralToken 调用Gemini API创建临时令牌
func (h *GeminiHandler) createGeminiEphemeralToken(ctx context.Context, userID uint64, req *CreateEphemeralTokenRequest) (*CreateEphemeralTokenResponse, error) {
	cfg := config.GlobalConfig
	if cfg == nil || cfg.Gemini.APIKey == "" {
		return nil, fmt.Errorf("Gemini配置或API Key未初始化")
	}

	// 设置令牌的过期时间和会话创建时间
	now := time.Now().UTC()
	expireTime := now.Add(time.Duration(req.ExpireMinutes) * time.Minute)
	newSessionExpireTime := now.Add(newSessionCreationWindow)

	// 构建请求体
	requestBody := map[string]interface{}{
		"uses":                    req.Uses,
		"expire_time":             expireTime.Format(time.RFC3339Nano),
		"new_session_expire_time": newSessionExpireTime.Format(time.RFC3339Nano),
	}

	jsonData, err := json.Marshal(requestBody)
	if err != nil {
		return nil, fmt.Errorf("序列化请求失败: %w", err)
	}

	// 目标 URL
	url := fmt.Sprintf("%s/v1alpha/auth_tokens?key=%s", cfg.Gemini.BaseURL, cfg.Gemini.APIKey)

	// 创建 POST 请求
	httpReq, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("创建HTTP请求失败: %w", err)
	}
	httpReq.Header.Set("Content-Type", "application/json")

	// 发送请求
	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("发送HTTP请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %w", err)
	}

	// 记录响应信息
	slog.Info("API response",
		"statusCode", resp.StatusCode,
		"responseBody", string(body),
	)

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		slog.Error("API request failed", "statusCode", resp.StatusCode, "responseBody", string(body))
		return nil, fmt.Errorf("API请求失败，状态码: %d, 响应: %s", resp.StatusCode, string(body))
	}

	// 解析响应
	var result map[string]interface{}
	if err := json.Unmarshal(body, &result); err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	// 提取令牌信息
	token, ok := result["token"].(string)
	if !ok {
		if name, ok := result["name"].(string); ok {
			token = name
		} else {
			return nil, fmt.Errorf("响应中缺少令牌信息 ('token' or 'name'): %+v", result)
		}
	}

	// 返回结果
	return &CreateEphemeralTokenResponse{
		Token:     token,
		ExpiresAt: expireTime,
		Uses:      req.Uses,
	}, nil
}
