{"server": {"port": "8080", "mode": "debug", "read_timeout": 60, "write_timeout": 60}, "database": {"mysql": {"host": "localhost", "port": "3306", "username": "root", "password": "password", "database": "interview_master", "charset": "utf8mb4", "parse_time": true, "loc": "Local", "max_idle_conns": 10, "max_open_conns": 100, "conn_max_lifetime": 3600}, "redis": {"host": "localhost", "port": "6379", "password": "", "db": 0, "pool_size": 10, "min_idle_conns": 5}}, "jwt": {"secret": "your-secret", "expire_hours": 24}, "gemini": {"api_key": "your-initial-api-key-here", "model": "gemini-2.5-flash-preview-native-audio-dialog", "base_url": "https://generativelanguage.googleapis.com", "proxy_url": "", "connection_mode": "auto", "timeout": 30, "max_concurrent_sessions": 100}, "aliyun_sms": {"access_key_id": "your-aliyun-access-key-id", "access_key_secret": "your-aliyun-access-key-secret", "sign_name": "your-sms-sign-name", "template_code": "your-sms-template-code"}, "payment": {"wechat": {"app_id": "wx1234567890abcdef", "mch_id": "1234567890", "api_key": "your32characterapikeyhere123456789", "notify_url": "https://your-domain.com/api/v1/notify/wechat"}, "alipay": {"app_id": "2021001234567890", "private_key": "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC...", "public_key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA...", "notify_url": "https://your-domain.com/api/v1/notify/alipay", "is_production": false}}, "ab_test": {"enabled": true, "default_group": "A", "groups": ["A", "B"]}, "log": {"level": "info", "file_path": "logs/app.log", "max_size": 100, "max_backups": 5, "max_age": 30}}